// import 'package:flutter/material.dart';
// import 'package:mandob/core/extensions/context_extensions.dart';
// import 'package:mandob/pages/printers/view/printers/widgets/printer_card/printer_card.dart';
// import 'package:mandob/providers/printers_provider.dart';
// import 'package:provider/provider.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class MainPrinterList extends StatelessWidget {
//   const MainPrinterList({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     final printers = context.watch<PrinterVM>().printer;
//
//     // if (printers.isEmpty) {
//     //   return Center(
//     //     child: Text(
//     //       context.isEng ? 'No Printers Found' : 'لا يوجد طابعات',
//     //       style: AppTextStyles.labelLarge,
//     //     ),
//     //   );
//     // }
//
//     return ListView.separated(
//       padding: const EdgeInsets.all(AppSpaces.padding16),
//       shrinkWrap: true,
//       itemCount: printers.length,
//       itemBuilder: (context, index) {
//         final printer = printers[index];
//
//         return PrinterCard(
//           printer: printer,
//         );
//       },
//       separatorBuilder: (context, index) => AppGaps.gap4,
//     );
//   }
// }
