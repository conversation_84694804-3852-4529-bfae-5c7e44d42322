import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/pages/printers/models/printers_model.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class PrinterCard extends StatelessWidget {
  final PrinterModel printer;

  const PrinterCard({
    super.key,
    required this.printer,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Row(
          children: [
            //! Titles
            Expanded(
              child: _MainTitles(
                printer: printer,
              ),
            ),
          ],
        ).decorated(
          margin: const EdgeInsets.all(AppSpaces.padding4),
          padding: const EdgeInsets.all(AppSpaces.padding16),
          radius: BorderRadius.circular(AppRadius.radius12),
          color: ColorManager.lightFieldColor.withOpacity(0.5),
          border: printer.isDefault
              ? Border.all(
                  color: ColorManager.primaryColor,
                  width: 1,
                )
              : null,
        ),

        //! Actions
        Positioned.fill(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              printer.isDefault
                  ? const SizedBox()
                  : TextButton(
                      onPressed: () {
                        // printerController.setDefaultPrinter(printer.id);
                      },
                      child: Text(
                        context.isEng ? 'Set Default' : 'تعيين كافتراضي',
                        style: AppTextStyles.labelLarge.copyWith(
                          color: ColorManager.primaryColor,
                        ),
                      ).paddingSymmetric(vertical: 12),
                    ),
              // PrinterActions(
              //   printer: printer,
              // ).align(
              //   context.isEnglish ? Alignment.topRight : Alignment.topLeft,
              // ),
            ],
          ),
        ),

        Align(
          alignment: Alignment.topLeft,
          child: printer.isDefault
              ? const Icon(
                  Icons.check_circle,
                  color: ColorManager.primaryColor,
                  size: 20,
                ).paddingSymmetric(
                  horizontal: AppSpaces.padding4,
                )
              : const SizedBox(),
        ),
      ],
    );
  }
}

class _MainTitles extends StatelessWidget {
  final PrinterModel printer;

  const _MainTitles({required this.printer});

  @override
  Widget build(BuildContext context) {
    return [
      //! Name
      Text(
        printer.name,
        style: AppTextStyles.title.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),

      AppGaps.gap4,

      //! IP Address
      Text(
        printer.ipAddress,
        style: AppTextStyles.subTitle.copyWith(
          color: Colors.grey,
        ),
      ),
    ].column(
      crossAxisAlignment: CrossAxisAlignment.start,
    );
  }
}
