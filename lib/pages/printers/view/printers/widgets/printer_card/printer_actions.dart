// import 'package:flutter/material.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// import '../../../../models/printers_model.dart';
//
// class PrinterActions extends StatelessWidget {
//   final PrinterModel printer;
//
//   const PrinterActions({super.key, required this.printer});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final printerController =
//         ref.watch(printersChangeNotifierController(context));
//
//     return ActionButtons(
//         onEdit: () => context.to(AddPrinterScreen(
//               printer: printer,
//             )),
//         onDelete: () => showPlatformDialog(context,
//                 isDelete: true,
//                 title: context.tr.deletePrinter,
//                 content: context.tr.deletePrinterMessage, action: () async {
//               await printerController.deletePrinter(printer.id);
//             })).paddingOnly(
//       top: AppSpaces.xSmallPadding,
//       bottom: AppSpaces.xSmallPadding,
//       right: AppSpaces.xSmallPadding,
//     );
//   }
// }
