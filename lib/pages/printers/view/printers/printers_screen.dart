// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:mandob/pages/printers/view/printers/widgets/printer_cards_list.dart';
//
// class PrintersScreen extends HookWidget {
//   const PrintersScreen({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       // appBar: BaseAppBar(
//       //     title: context.tr.printers,
//       //     onBackButtonPressed: () {
//       //       // bottomNavCtrl.changeIndex(4);
//       //       // context.toReplacement(const POSMainScreen());
//       //     }),
//       // floatingActionButton: BaseFloatingButton(
//       //   // onPressed: () => context.to(const AddPrinterScreen()),
//       //   icon: const CircleAvatar(
//       //     backgroundColor: Colors.white,
//       //     radius: 18,
//       //     child: Icon(
//       //       Icons.add,
//       //       color: ColorManager.primaryColor,
//       //     ),
//       //   ),
//       //   label: context.tr.addPrinter,
//       // ),
//       floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
//       body: const MainPrinterList(),
//     );
//   }
// }
