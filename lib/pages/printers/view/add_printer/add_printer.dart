// import 'package:drago_blue_printer/drago_blue_printer.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:mandob/core/extensions/context_extensions.dart';
// import 'package:mandob/pages/printers/models/printers_model.dart';
// import 'package:mandob/providers/printers_provider.dart';
// import 'package:mandob/utils/app_bar.dart';
// import 'package:mandob/utils/color_manager.dart';
// import 'package:mandob/utils/show_bar/flush_bar.dart';
// import 'package:provider/provider.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// import '../../../../utils/text_field.dart';
// import 'widgets/add_bt_printer_widget.dart';
//
// class AddPrinterScreen extends HookWidget {
//   final PrinterModel? printer;
//
//   const AddPrinterScreen({super.key, this.printer});
//
//   @override
//   Widget build(
//     BuildContext context,
//   ) {
//     final nameController = useTextEditingController(text: printer?.name);
//
//     final macAddress = useState<String>('');
//
//     final isEdit = printer != null;
//
//     final printerController = context.read<PrinterVM>();
//
//     final formKey = useState(GlobalKey<FormState>());
//
//     void addEditPrinter() async {
//       final printer = PrinterModel(
//         id: this.printer?.id,
//         name: nameController.text,
//         ipAddress: macAddress.value,
//         isDefault: this.printer?.isDefault ?? false,
//       );
//
//       if (isEdit) {
//         // await printerController.editPrinter(printer: printer);
//       } else {
//         await printerController.addPrinter(
//             printer: BluetoothDevice(
//           printer.name,
//           printer.ipAddress,
//         ));
//       }
//
//       if (!context.mounted) return;
//
//       context.back();
//
//       // context.toReplacement(const PrintersScreen());
//
//       showBar(
//         context,
//         isEdit
//             ? (context.isEng ? "Edited Successfully" : "تم التعديل بنجاح")
//             : (context.isEng ? "Added Successfully" : "تم الاضافة بنجاح"),
//         backgroundColor: ColorManager.primaryColor,
//       );
//     }
//
//     void validateAndAddEditPrinter() async {
//       if (!formKey.value.currentState!.validate()) return;
//
//       if (macAddress.value.isEmpty) {
//         showBar(
//           context,
//           context.isEng ? "Please add a printer" : "من فضلك اضف طابعة",
//         );
//         return;
//       }
//       context.back();
//
//       addEditPrinter();
//     }
//
//     return Form(
//       key: formKey.value,
//       child: Scaffold(
//         bottomNavigationBar: Button(
//                 color: ColorManager.primaryColor,
//                 isWhiteText: true,
//                 isLoading: printerController.isLoading,
//                 label: isEdit
//                     ? (context.isEng ? "Edit Printer" : "تعديل الطابعة")
//                     : (context.isEng ? "Add Printer" : "اضافة طابعة"),
//                 // isEdit ? context.tr.editPrinter : context.tr.addPrinter,
//                 onPressed: validateAndAddEditPrinter)
//             .paddingAll(AppSpaces.padding16),
//         appBar: appBarWidget(
//           context,
//           title: isEdit
//               ? (context.isEng ? "Edit Printer" : "تعديل الطابعة")
//               : (context.isEng ? "Add Printer" : "اضافة طابعة"),
//         ),
//         body: SingleChildScrollView(
//           child: Column(
//             children: [
//               //! Name
//               TextFieldWidget(
//                 controller: nameController,
//                 label: context.isEng ? "Name" : "الاسم",
//               ),
//
//               AppGaps.gap12,
//
//               //! Add BT Printer Widget
//               AddBTPrinterWidget(
//                 macAddress: macAddress,
//               ),
//
//               // AppGaps.gap96,
//
//               // const Spacer(),
//             ],
//           ).paddingAll(AppSpaces.padding16),
//         ),
//       ),
//     );
//   }
// }
