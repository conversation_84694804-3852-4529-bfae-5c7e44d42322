// import 'package:flutter/material.dart';
// import 'package:mandob/core/extensions/context_extensions.dart';
// import 'package:mandob/pages/printers/models/printers_model.dart';
// import 'package:mandob/pages/printers/view/add_printer/add_printer.dart';
// import 'package:mandob/utils/text_field.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class AddPrinterWidgets extends StatelessWidget {
//   final (
//     TextEditingController name,
//     TextEditingController ipAddress,
//   ) nameControllers;
//   final PrinterModel? printer;
//   final ValueNotifier<PrinterTypes> printerType;
//
//   const AddPrinterWidgets({
//     super.key,
//     required this.nameControllers,
//     this.printer,
//     required this.printerType,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final nameController = nameControllers.$1;
//     final ipAddressController = nameControllers.$2;
//
//     return Column(
//       children: [
//         //! Name
//         TextFieldWidget(
//           controller: nameController,
//           label: context.isEng ? "Name" : "الاسم",
//         ),
//
//         // context.mediumGap,
//         //
//         // //! IP Address
//         // BaseTextField(
//         //   controller: ipAddressController,
//         //   title: context.tr.ipAddress,
//         //   hint: '${context.tr.enter} ${context.tr.ipAddress}',
//         // ),
//
//         AppGaps.gap12,
//
//
//       ],
//     );
//   }
// }
