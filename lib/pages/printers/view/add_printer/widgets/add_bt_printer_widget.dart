// import 'dart:async';
//
// import 'package:drago_blue_printer/drago_blue_printer.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:mandob/core/extensions/context_extensions.dart';
// import 'package:mandob/utils/color_manager.dart';
// import 'package:mandob/utils/show_bar/flush_bar.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// DragoBluePrinter _bluetooth = DragoBluePrinter.instance;
//
// class AddBTPrinterWidget extends HookWidget {
//   final ValueNotifier<String> macAddress;
//
//   const AddBTPrinterWidget({
//     super.key,
//     required this.macAddress,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final _connected = useState(false);
//     final _devices = useState(<BluetoothDevice>[]);
//     final selectedDevice = useState<BluetoothDevice?>(null);
//
//     // final pathImage = useState<String?>(null);
//
//     // initSaveToPath() async {
//     //   //read and write
//     //   //image max 300px X 300px
//     //   const filename = 'table.png';
//     //   var bytes = await rootBundle.load("assets/table.png");
//     //   String dir = (await getApplicationDocumentsDirectory()).path;
//     //   writeToFile(bytes, '$dir/$filename');
//     //   pathImage.value = '$dir/$filename';
//     //
//     //   Log.i('PathImage: ${pathImage.value}');
//     //
//     //   //? know image width
//     //   //?
//     // }
//     //
//     // initSaveToPath2() async {
//     //   //read and write
//     //   //image max 300px X 300px
//     //   const filename = 'resized_table.png';
//     //   var bytes = await rootBundle.load("assets/resized_table.png");
//     //   String dir = (await getApplicationDocumentsDirectory()).path;
//     //   writeToFile(bytes, '$dir/$filename');
//     //   pathImage2.value = '$dir/$filename';
//     //
//     //   Log.i('PathImage: ${pathImage2.value}');
//     //
//     //   //? know image width
//     //   //?
//     // }
//
//     Future<void> initPlatformState() async {
//       bool isConnected = await _bluetooth.isConnected ?? false;
//       List<BluetoothDevice> devices = [];
//       try {
//         devices = await _bluetooth.getBondedDevices();
//       } on PlatformException {
//         showBar(
//           context,
//           "Error Occurred !",
//           backgroundColor: ColorManager.errorColor,
//         );
//       }
//
//       _bluetooth.onStateChanged().listen((state) {
//         switch (state) {
//           case DragoBluePrinter.CONNECTED:
//             _connected.value = true;
//             print("bluetooth device state: connected");
//
//             break;
//           case DragoBluePrinter.DISCONNECTED:
//             _connected.value = false;
//             print("bluetooth device state: disconnected");
//
//             break;
//           case DragoBluePrinter.DISCONNECT_REQUESTED:
//             _connected.value = false;
//             print("bluetooth device state: disconnect requested");
//
//             break;
//           case DragoBluePrinter.STATE_TURNING_OFF:
//             _connected.value = false;
//             print("bluetooth device state: bluetooth turning off");
//
//             break;
//           case DragoBluePrinter.STATE_OFF:
//             _connected.value = false;
//             print("bluetooth device state: bluetooth off");
//
//             break;
//           case DragoBluePrinter.STATE_ON:
//             _connected.value = false;
//             print("bluetooth device state: bluetooth on");
//
//             break;
//           case DragoBluePrinter.STATE_TURNING_ON:
//             _connected.value = false;
//             print("bluetooth device state: bluetooth turning on");
//
//             break;
//           case DragoBluePrinter.ERROR:
//             _connected.value = false;
//             print("bluetooth device state: error");
//
//             break;
//           default:
//             print(state);
//             break;
//         }
//       });
//
//       if (!context.mounted) return;
//
//       _devices.value = devices;
//
//       if (isConnected) {
//         _connected.value = true;
//       }
//     }
//
//     useEffect(() {
//       initPlatformState();
//       // initSaveToPath();
//       // initSaveToPath2();
//       return () {};
//     }, []);
//
//     List<DropdownMenuItem<BluetoothDevice>> _getDeviceItems() {
//       List<DropdownMenuItem<BluetoothDevice>> items = [];
//
//       if (_devices.value.isEmpty) {
//         items.add(const DropdownMenuItem(
//           child: Text('NONE'),
//         ));
//       } else {
//         for (var device in _devices.value) {
//           items.add(DropdownMenuItem(
//             value: device,
//             child: Text(device.name ?? ''),
//           ));
//         }
//       }
//       return items;
//     }
//
//     void _connect() {
//       if (selectedDevice.value == null) {
//         showBar(
//           context,
//           context.isEng ? "No Printer Found !" : "لم يتم العثور على طابعة",
//           backgroundColor: ColorManager.errorColor,
//         );
//       } else {
//         _bluetooth.isConnected.then((isConnected) {
//           if (!(isConnected ?? false)) {
//             _bluetooth.connect(selectedDevice.value!).catchError((error) {
//               _connected.value = false;
//             });
//
//             _connected.value = true;
//
//             macAddress.value = selectedDevice.value!.address ?? '';
//
//             GetStorageService.setData(
//               key: LocalKeys.printer,
//               value: selectedDevice.value?.toMap(),
//             );
//           }
//         });
//       }
//     }
//
//     void _disconnect() {
//       _bluetooth.disconnect();
//       _connected.value = false;
//     }
//
//     return Column(
//       children: [
//         Row(
//           crossAxisAlignment: CrossAxisAlignment.center,
//           mainAxisAlignment: MainAxisAlignment.start,
//           children: <Widget>[
//             AppGaps.gap12,
//             Text(
//               context.isEng ? 'Device:' : 'الجهاز:',
//               style: const TextStyle(
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//             AppGaps.gap12,
//             Expanded(
//               child: DropdownButton(
//                 borderRadius: BorderRadius.circular(AppRadius.radius12),
//                 underline: Container(
//                   height: 2,
//                   color: ColorManager.primaryColor,
//                 ),
//                 isExpanded: true,
//                 icon: const Icon(Icons.arrow_drop_down_circle_outlined),
//                 items: _getDeviceItems(),
//                 onChanged: (value) => selectedDevice.value = value,
//                 value: selectedDevice.value,
//               ),
//             ),
//             AppGaps.gap12,
//             ElevatedButton(
//               style:
//                   ElevatedButton.styleFrom(backgroundColor: ColorManager.brown),
//               onPressed: () {
//                 initPlatformState();
//               },
//               child: Text(
//                 context.isEng ? 'Refresh' : 'تحديث',
//                 style: const TextStyle(color: Colors.white),
//               ),
//             ),
//           ],
//         ),
//         AppGaps.gap16,
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//           children: <Widget>[
//             ElevatedButton(
//               style: ElevatedButton.styleFrom(
//                   backgroundColor: _connected.value
//                       ? Colors.red
//                       : ColorManager.secondaryColor),
//               onPressed: _connected.value ? _disconnect : _connect,
//               child: Text(
//                 _connected.value
//                     ? (context.isEng ? 'Disconnect' : 'قطع الاتصال')
//                     : (context.isEng ? 'Connect' : 'اتصل'),
//                 // _connected.value ? 'Disconnect' : 'Connect',
//                 style: const TextStyle(color: Colors.white),
//               ),
//             ),
//             // ElevatedButton(
//             //   style: ElevatedButton.styleFrom(primary: ColorManager.darkGrey),
//             //   onPressed: () {
//             //     testPrint.printInvoice(pathImage2.value!);
//             //   },
//             //   child: const Text('PRINT TEST',
//             //       style: TextStyle(color: Colors.white)),
//             // ),
//             // ElevatedButton(
//             //   style: ElevatedButton.styleFrom(primary: ColorManager.darkGrey),
//             //   onPressed: () {
//             //     testPrint.printInvoice(pathImage.value!);
//             //   },
//             //   child: const Text('PRINT TEST 2',
//             //       style: TextStyle(color: Colors.white)),
//             // ),
//           ],
//         ),
//       ],
//     );
//   }
// }
