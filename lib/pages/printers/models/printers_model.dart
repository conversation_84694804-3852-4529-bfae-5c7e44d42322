import 'package:equatable/equatable.dart';

class PrinterModel extends Equatable {
  final int? id;
  final String name;
  final String ipAddress;
  final bool isDefault;

  const PrinterModel({
    this.id,
    this.name = "",
    this.ipAddress = "",
    this.isDefault = true,
  });

  //? From Json
  factory PrinterModel.fromJson(Map<String, dynamic> json) {
    return PrinterModel(
      id: json['id'],
      name: json['name'] ?? '',
      ipAddress: json['ipAddress'] ?? '',
      isDefault: json['isDefault'] ?? true,
    );
  }

  //? To Json
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'ipAddress': ipAddress,
      'isDefault': isDefault,
    };
  }

  //? Copy With
  PrinterModel copyWith({
    int? id,
    String? name,
    String? ipAddress,
    bool? isDefault,
  }) {
    return PrinterModel(
      id: id ?? this.id,
      name: name ?? this.name,
      ipAddress: ipAddress ?? this.ipAddress,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  //? Get Default Printer
  // static PrinterModel? getDefaultPrinter() {
  //   final printersData = GetStorageService.getData(
  //     key: LocalKeys.printer,
  //   );
  //
  //   if (printersData == null) return null;
  //
  //   final printers = List<PrinterModel>.from(
  //     printersData.map((x) => x is PrinterModel ? x : PrinterModel.fromJson(x)),
  //   );
  //
  //   return printers.firstWhereOrNull((element) => element.isDefault);
  // }

  @override
  List<Object?> get props => [name, ipAddress, isDefault];
}
