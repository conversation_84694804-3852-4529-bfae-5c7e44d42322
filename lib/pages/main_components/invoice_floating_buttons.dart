import 'package:flutter/material.dart';
import 'package:mandob/utils/color_manager.dart';

class FloatingButtonWidget extends StatelessWidget {
  final VoidCallback onPressed;
  final String? label;
  const FloatingButtonWidget(
      {Key? key, required this.onPressed, required this.label})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      backgroundColor: ColorManager.primaryColor,
      onPressed: onPressed,
      label: Text(
        label!,
        style: const TextStyle(
          color: Colors.white,
        ),
      ),
      icon: const Icon(
        Icons.playlist_add_outlined,
        color: Colors.white,
      ),
    );
  }
}
