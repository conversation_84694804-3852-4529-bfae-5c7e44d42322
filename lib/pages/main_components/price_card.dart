import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:persian_number_utility/persian_number_utility.dart';
import 'package:xr_helper/xr_helper.dart';

class PriceCard extends StatelessWidget {
  final double price;
  final String title;
  final Color color;

  const PriceCard({
    super.key,
    required this.price,
    required this.title,
    this.color = ColorManager.primaryColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        // color: Colors.white,
        border: Border.all(color: color),
      ),
      child: Column(
        children: [
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              title,
              maxLines: 1,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: color,
              ),
              softWrap: true,
            ),
          ),
          AppGaps.gap8,
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
                '${price.toStringAsFixed(2).seRagham()}${context.currency}',
                maxLines: 1,
                softWrap: true,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                )),
          ),
        ],
      ),
    );
  }
}
