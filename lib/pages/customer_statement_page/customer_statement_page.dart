import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/core/shared/drop_down_fields/suppliers_drop_down.dart';
import 'package:mandob/core/shared/language/change_language.widget.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/models/supplier_model.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/pages/customer_statement_page/components/customer_statemnet_table_widget.dart';
import 'package:mandob/providers/collections_provider.dart';
import 'package:mandob/providers/company_provider.dart';
import 'package:mandob/providers/customers_provider.dart';
import 'package:mandob/providers/reports_provider.dart';
import 'package:mandob/providers/supplier_payments_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/widgets/date_widget.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../core/shared/drop_down_fields/customers_drop_down.dart';
import '../../providers/language.dart';
import '../../utils/app_bar.dart';
import 'components/print_statement/print_en_invoices_statement.dart';
import 'components/print_statement/print_invoices_statement_invoices_pdf.dart';

enum CustomerStatementType {
  invoice,
  returned,
  collection,
}

class CustomerPageStatement extends StatefulWidget {
  final bool isPurchase;

  const CustomerPageStatement({
    super.key,
    this.isPurchase = false,
  });

  @override
  _CustomerPageStatementState createState() => _CustomerPageStatementState();
}

class _CustomerPageStatementState extends State<CustomerPageStatement> {
  CustomerStatementType? statementType;
  var total = ValueNotifier<num>(0);

  var totalPaid = ValueNotifier<num>(0);
  var remaining = ValueNotifier<num>(0);
  var currentCellBalance = ValueNotifier<num>(0);

  int? expenseTypeId;

  List<CustomerModel>? mandobUsers;

  CustomerModel? customerD;
  SupplierModel? supplierD;

  String mandob = '';

  UserModel? mandobData;

  int index = 0;

  @override
  void initState() {
    super.initState();
    total.value = 0;
    totalPaid.value = 0;
    remaining.value = 0;

    setState(() {
      statementType = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final lang = Provider.of<LangProvider>(context, listen: false);
    final compInfo =
        Provider.of<CompanyInfoProvider>(context, listen: false).companyInfo;

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Consumer4<UserProvider, ReportsProvider, CollectionsProvider,
            SupplierPaymentsProvider>(
          builder:
              (context, user, _invoice, _collections, _supplierPayments, _) {
            return HookBuilder(builder: (context) {
              // var sDate = ValueNotifier(DateTime.now().subtract(const Duration(days: 1)));
              var sDate =
                  useState(DateTime.now().subtract(const Duration(days: 1)));
              var eDate = useState(DateTime.now());

              return Column(
                children: [
                  appBarWidget(
                    context,
                    title: context.isEng
                        ? (widget.isPurchase
                            ? 'Supplier Statement Page'
                            : 'Customer Statement Page')
                        : (widget.isPurchase
                            ? "كشف حساب الموردين"
                            : "كشف حساب العملاء"),
                    action: statementType != CustomerStatementType.collection
                        ? IconButton(
                            onPressed: () {
                              final invoices = _invoice.invoices;

                              final collections =
                                  _collections.collections ?? [];

                              showChangeLanguageDialog(
                                context,
                                onArTap: () {
                                  printCustomerStatementPdf(
                                    context,
                                    info: compInfo,
                                    receiptData: invoices,
                                    collections: collections,
                                    payments: _supplierPayments.payments ?? [],
                                    fromDate:
                                        sDate.value.toString().split(" ").first,
                                    toDate: DateTime.tryParse(eDate.value
                                            .toString()
                                            .split(" ")
                                            .first)!
                                        .add(const Duration(days: 90))
                                        .toString()
                                        .split(" ")
                                        .first,
                                    isSales: !widget.isPurchase,
                                  );
                                },
                                onEnTap: () {
                                  printEnCustomerStatementPdf(
                                    context,
                                    info: compInfo,
                                    receiptData: invoices,
                                    collections: collections,
                                    payments: _supplierPayments.payments ?? [],
                                    fromDate:
                                        sDate.value.toString().split(" ").first,
                                    toDate: DateTime.tryParse(eDate.value
                                            .toString()
                                            .split(" ")
                                            .first)!
                                        .add(const Duration(days: 90))
                                        .toString()
                                        .split(" ")
                                        .first,
                                    isSales: !widget.isPurchase,
                                  );
                                },
                              );
                            },
                            icon: const Icon(
                              Icons.print,
                              color: ColorManager.secondaryColor,
                              size: 32,
                            ),
                          )
                        : null,
                  ),
                  Consumer<CustomersProvider>(
                      builder: (context, _customers, _) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      child: widget.isPurchase
                          ? SuppliersDropDownButton(
                              width: MediaQuery.sizeOf(context).width,
                              onChanged: (val) {
                                setState(() {
                                  supplierD = val;
                                });
                              },
                              supplier: supplierD,
                            )
                          : CustomersDropDownButton(
                              width: MediaQuery.sizeOf(context).width,
                              onChanged: (val) {
                                setState(() {
                                  customerD = val;
                                });
                              },
                              customer: customerD,
                            ),
                    );
                  }),
                  if (!widget.isPurchase) ...[
                    AppGaps.gap12,
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Text(
                                context.isEng
                                    ? 'Opening Balance: '
                                    : 'الرصيد الإفتتاحي: ',
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 14),
                              ),
                              Text(
                                '${customerD?.openingBalance?.toStringAsFixed(2) ?? 0}${context.currency}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  color: ColorManager.primaryColor,
                                ),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              Text(
                                context.isEng ? 'Debit: ' : 'المديونية: ',
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 14),
                              ),
                              Text(
                                '${(customerD?.debit?.toStringAsFixed(2) ?? 0)}${context.currency}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  color: ColorManager.errorColor,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                  AppGaps.gap12,
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    child: UiWidgets.datePicker(context, () {
                      setState(() {});
                    }, startDate: sDate, endDate: eDate),
                  ),
                  AppGaps.gap12,
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.blueGrey.shade50,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    margin: const EdgeInsets.symmetric(horizontal: 12.0),
                    child: DropdownButton<CustomerStatementType>(
                      isExpanded: true,
                      underline: Container(),
                      onChanged: (value) => setState(() {
                        statementType = value;
                      }),
                      hint: Text(
                        context.isEng ? 'Statement Type' : "نوع الكشف",
                      ),
                      value: statementType,
                      items: widget.isPurchase
                          ? [
                              DropdownMenuItem(
                                  value: null,
                                  child: Text(context.isEng ? 'All' : "الكل")),
                              DropdownMenuItem(
                                value: CustomerStatementType.invoice,
                                child: Text(
                                    context.isEng ? 'Purchases' : "مشتريات"),
                              ),
                              DropdownMenuItem(
                                value: CustomerStatementType.returned,
                                child:
                                    Text(context.isEng ? 'Returns' : "مرتجعات"),
                              ),
                              DropdownMenuItem(
                                value: CustomerStatementType.collection,
                                child:
                                    Text(context.isEng ? 'Payments' : "سدادات"),
                              ),
                            ]
                          : [
                              DropdownMenuItem(
                                  value: null,
                                  child: Text(context.isEng ? 'All' : "الكل")),
                              DropdownMenuItem(
                                value: CustomerStatementType.invoice,
                                child: Text(widget.isPurchase
                                    ? (context.isEng ? 'Purchases' : "مشتريات")
                                    : (context.isEng ? 'Sales' : "مبيعات")),
                              ),
                              DropdownMenuItem(
                                value: CustomerStatementType.returned,
                                child:
                                    Text(context.isEng ? 'Returns' : "مرتجعات"),
                              ),
                              DropdownMenuItem(
                                value: CustomerStatementType.collection,
                                child: Text(
                                    context.isEng ? 'Collections' : "تحصيلات"),
                              ),
                            ],
                    ),
                  ),
                  AppGaps.gap12,
                  const Divider(
                    thickness: 1,
                    color: ColorManager.lightFieldColor,
                  ).paddingSymmetric(horizontal: 12),
                  AppGaps.gap12,
                  CustomerStatementTableWidget(
                      customerD: customerD,
                      supplierD: supplierD,
                      statementType: statementType,
                      isPurchase: widget.isPurchase,
                      sDate: sDate,
                      eDate: eDate,
                      total: total,
                      remaining: remaining,
                      currentCellBalance: currentCellBalance),
                ],
              );
            });
          },
        ),
      ),
    );
  }
}
