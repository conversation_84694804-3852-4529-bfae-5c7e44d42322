import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/models/supplier_model.dart';
import 'package:mandob/pages/customer_statement_page/customer_statement_page.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../models/collection_model.dart';
import '../../../models/invoice_model.dart';
import '../../../models/supplier_payment_model.dart';
import '../../../providers/collections_provider.dart';
import '../../../providers/language.dart';
import '../../../providers/reports_provider.dart';
import '../../../providers/supplier_payments_provider.dart';
import '../../../providers/user_provider.dart';
import '../../../utils/color_manager.dart';

class CustomerStatementTableWidget extends StatelessWidget {
  final CustomerModel? customerD;
  final SupplierModel? supplierD;
  final CustomerStatementType? statementType;
  final bool isPurchase;
  final ValueNotifier<DateTime> sDate;
  final ValueNotifier<DateTime> eDate;
  final ValueNotifier<num> currentCellBalance;
  final ValueNotifier<num> total;
  final ValueNotifier<num> remaining;

  const CustomerStatementTableWidget(
      {super.key,
      this.customerD,
      this.supplierD,
      required this.statementType,
      required this.isPurchase,
      required this.sDate,
      required this.eDate,
      required this.total,
      required this.remaining,
      required this.currentCellBalance});

  @override
  Widget build(BuildContext context) {
    return Consumer4<UserProvider, ReportsProvider, CollectionsProvider,
        SupplierPaymentsProvider>(
      builder: (context, user, _invoice, _collections, _supplierPayments, _) {
        return Expanded(
          child: Builder(
            builder: (context) {
              bool loading = false;

              Future fetch() async {
                total.value = 0;
                remaining.value = 0;

                if (isPurchase) {
                  if (supplierD == null) return;
                  loading = true;

                  await _invoice.fetchReportInvoices(
                    notifyListener: false,
                    isReturned: statementType == CustomerStatementType.returned,
                    isSales: false,
                    customerId: supplierD?.id,
                    startDate: sDate.value,
                    endDate: eDate.value,
                  );

                  await _supplierPayments.fetchPayments(
                    supplierId: supplierD?.id,
                    startDate: sDate.value,
                    endDate: eDate.value,
                  );

                  for (var payment in _supplierPayments.payments!) {
                    remaining.value -= payment.value!;
                  }
                } else {
                  if (customerD == null) return;
                  loading = true;
                  await _invoice.fetchReportInvoices(
                    notifyListener: false,
                    isReturned: statementType == CustomerStatementType.returned,
                    isSales: true,
                    customerId: customerD?.id,
                    startDate: sDate.value,
                    endDate: eDate.value,
                  );

                  await _collections.fetchCollections(
                    customerId: customerD?.id,
                    startDate: sDate.value,
                    endDate: eDate.value,
                  );
                }

                for (var invoice in _invoice.invoices) {
                  total.value += invoice.totalPrice!;
                  remaining.value += invoice.totalPrice! - invoice.paidCash!;
                }

                for (var collection in _collections.collections!) {
                  remaining.value -= collection.value!;
                }

                loading = false;
              }

              return FutureBuilder(
                  future: fetch(),
                  builder: (context, snapshot) => Stack(
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: Column(
                              children: [
                                Table(
                                  border: TableBorder.all(color: Colors.grey),
                                  columnWidths: const {
                                    0: FlexColumnWidth(1),
                                    1: FlexColumnWidth(1.4),
                                    2: FlexColumnWidth(1.2),
                                    3: FlexColumnWidth(0.8),
                                  },
                                  children: [
                                    TableRow(
                                        decoration: const BoxDecoration(
                                          color: Color(0xFFdfdfe3),
                                        ),
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: FittedBox(
                                              fit: BoxFit.scaleDown,
                                              child: Text(
                                                context.isEng
                                                    ? 'Date'
                                                    : "التاريخ",
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: FittedBox(
                                              fit: BoxFit.scaleDown,
                                              child: Text(
                                                context.isEng
                                                    ? 'Invoice Type'
                                                    : "المصدر",
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: FittedBox(
                                              fit: BoxFit.scaleDown,
                                              child: Text(
                                                context.isEng
                                                    ? 'Transaction'
                                                    : "الحركة",
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: FittedBox(
                                              fit: BoxFit.scaleDown,
                                              child: Text(
                                                context.isEng
                                                    ? 'Balance'
                                                    : "الرصيد",
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ),
                                        ])
                                  ],
                                ),
                                Builder(builder: (context) {
                                  if (isPurchase && supplierD == null) {
                                    return const SizedBox.shrink();
                                  }
                                  if (!isPurchase && customerD == null) {
                                    return const SizedBox.shrink();
                                  }

                                  return loading
                                      ? const LoadingWidget()
                                          .paddingOnly(top: 20)
                                      : Expanded(
                                          child: SingleChildScrollView(
                                            child: Builder(builder: (context) {
                                              return Table(
                                                border: TableBorder.all(
                                                    color: Colors.grey),
                                                defaultVerticalAlignment:
                                                    TableCellVerticalAlignment
                                                        .middle,
                                                columnWidths: const {
                                                  0: FlexColumnWidth(1),
                                                  1: FlexColumnWidth(1.4),
                                                  2: FlexColumnWidth(1.2),
                                                  3: FlexColumnWidth(0.8),
                                                },
                                                children: filterData(
                                                  context,
                                                  _invoice.invoices,
                                                  _collections.collections ??
                                                      [],
                                                  _supplierPayments.payments ??
                                                      [],
                                                  statementType,
                                                ),
                                              );
                                            }),
                                          ),
                                        );
                                }),
                                AppGaps.gap12,
                                if (!loading)
                                  Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(12.0),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.withOpacity(0.5),
                                          spreadRadius: 2,
                                          blurRadius: 5,
                                          offset: const Offset(0, 3),
                                        ),
                                      ],
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Column(
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              context.isEng
                                                  ? 'Invoices Total'
                                                  : "اجمالى قيمة الفواتير",
                                              textAlign: TextAlign.center,
                                            ),
                                            Text(
                                              '${total.value.toStringAsFixed(2)}${context.currency}',
                                              textAlign: TextAlign.center,
                                            ),
                                          ],
                                        ),
                                        if (statementType !=
                                            CustomerStatementType
                                                .collection) ...[
                                          const SizedBox(height: 8.0),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                context.isEng
                                                    ? 'Total paid '
                                                    : 'اجمالى المدفوع',
                                                textAlign: TextAlign.center,
                                              ),
                                              Text(
                                                '${(total.value - remaining.value).toStringAsFixed(2)}${context.currency}',
                                                textAlign: TextAlign.center,
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8.0),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                context.isEng
                                                    ? 'Price / remain'
                                                    : "اجمالى الاجل",
                                                textAlign: TextAlign.center,
                                              ),
                                              Text(
                                                '${remaining.value.toStringAsFixed(2)}${context.currency}',
                                                textAlign: TextAlign.center,
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8.0),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                context.isEng
                                                    ? 'Remaining Balance'
                                                    : "الرصيد المتبقي",
                                                textAlign: TextAlign.center,
                                              ),
                                              ValueListenableBuilder(
                                                valueListenable:
                                                    currentCellBalance,
                                                builder:
                                                    (context, value, child) {
                                                  return Text(
                                                    '${(value).toStringAsFixed(2)}${context.currency}',
                                                    textAlign: TextAlign.center,
                                                  );
                                                },
                                              ),
                                            ],
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                AppGaps.gap12,
                              ],
                            ),
                          ),
                        ],
                      ));
            },
          ),
        );
      },
    );
  }

  List<TableRow> filterData(
    BuildContext context,
    List<InvoiceModel> invoices,
    List<CollectionModel> collections,
    List<SupplierPaymentModel> supplierPayments,
    CustomerStatementType? statementType,
  ) {
    final lang = Provider.of<LangProvider>(context, listen: false);

    currentCellBalance.value = 0;

    // Combine invoices, collections, and supplier payments into a single list with a common type
    final combinedList = [
      if (statementType == null ||
          statementType == CustomerStatementType.invoice)
        ...invoices.map((invoice) => {
              'type': 'invoice',
              'date': DateTime.parse(invoice.createdAt!),
              'data': invoice,
            }),
      if (isPurchase &&
          (statementType == null ||
              statementType == CustomerStatementType.collection))
        ...supplierPayments.map((payment) => {
              'type': 'supplierPayment',
              'date': payment.createdAt!,
              'data': payment,
            })
      else if (!isPurchase &&
          (statementType == null ||
              statementType == CustomerStatementType.collection))
        ...collections.map((collection) => {
              'type': 'collection',
              'date': collection.createdAt!.add(const Duration(seconds: 1)),
              'data': collection,
            }),
    ];

    combinedList
        .sort((a, b) => a['date'].toString().compareTo(b['date'].toString()));

    // Generate TableRow widgets
    final childs = combinedList.map((item) {
      String date = DateFormat('dd-MM-yyyy').format(item['date'] as DateTime);
      String description;
      String amount;
      String balance;

      if (item['type'] == 'invoice') {
        final invoice = item['data'] as InvoiceModel;
        if (isPurchase) {
          currentCellBalance.value += invoice.totalPrice!;
        } else {
          currentCellBalance.value -= invoice.totalPrice!;
        }
        description = isPurchase
            ? '${context.isEng ? 'Purchases' : "مشتريات"}/${invoice.invoiceType.toString() == 'cash' ? (context.isEng ? 'Cash' : "نقدية") : (context.isEng ? 'Credit' : "آجله")}'
            : '${!invoice.isReturned! ? (context.isEng ? 'Sales' : "مبيعات") : (context.isEng ? 'Returned' : 'مرتجع')}/${invoice.invoiceType.toString() == 'cash' ? (context.isEng ? 'Cash' : "نقدية") : (context.isEng ? 'Credit' : "آجله")}';
        amount =
            '${invoice.paidCash!.toStringAsFixed(2)}${context.currency}${invoice.invoiceType!.toLowerCase() != 'cash' ? " / ${(invoice.totalPrice! - invoice.paidCash!).toStringAsFixed(2)}${context.currency}" : ''}';
      } else if (item['type'] == 'collection') {
        final collection = item['data'] as CollectionModel;
        currentCellBalance.value += collection.value!;
        description = context.isEng ? 'Collection' : "تحصيل";
        amount = '${collection.value!.toStringAsFixed(2)}${context.currency}';
      } else if (item['type'] == 'supplierPayment') {
        final payment = item['data'] as SupplierPaymentModel;
        description = context.isEng ? 'Supplier Payment' : "سداد موردين";
        amount = '${payment.value!.toStringAsFixed(2)}${context.currency}';
        currentCellBalance.value -= payment.value!;
      } else {
        return const TableRow(children: []);
      }

      balance = currentCellBalance.value.toStringAsFixed(2);

      return TableRow(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              date,
              style: const TextStyle(
                fontFamily: 'Droid',
                fontWeight: FontWeight.bold,
                fontSize: 13,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              description,
              style: const TextStyle(
                fontFamily: 'Droid',
                fontWeight: FontWeight.bold,
                fontSize: 13,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                amount,
                style: TextStyle(
                  fontFamily: 'Droid',
                  fontWeight: FontWeight.bold,
                  fontSize: 13,
                  color: item['type'] == 'invoice'
                      ? ColorManager.errorColor
                      : item['type'] == 'collection' ||
                              item['type'] == 'supplierPayment'
                          ? ColorManager.primaryColor
                          : Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                balance + context.currency,
                style: const TextStyle(
                  fontFamily: 'Droid',
                  fontWeight: FontWeight.bold,
                  fontSize: 13,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      );
    }).toList();

    return childs;
  }
}
