import 'package:intl/intl.dart';
import 'package:mandob/models/supplier_payment_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

import '../../../../models/company_info_model.dart';

Future<void> addEnSupplierPaymentsPdfPage(context,
    {doc,
    ttf,
    ttfBold,
    CompanyInfoModel? info,
    required List<SupplierPaymentModel> payments,
    String? fromDate,
    String? toDate,
    required bool isSales}) async {
  doc.addPage(pw.Page(build: (pw.Context context) {
    return pw.Column(mainAxisSize: pw.MainAxisSize.max, children: [
      pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                mainAxisSize: pw.MainAxisSize.max,
                children: [
                  pw.Text(info!.companyName.toString(),
                      style: pw.TextStyle(
                          fontSize: 2.5 * PdfPageFormat.mm,
                          fontWeight: pw.FontWeight.bold,
                          font: ttfBold),
                      textDirection: pw.TextDirection.rtl,
                      textAlign: pw.TextAlign.left),
                  pw.SizedBox(height: 20),
                  pw.Text('VAT # ${info.vatNumber}',
                      style: pw.TextStyle(
                          fontSize: 2.8 * PdfPageFormat.mm,
                          fontWeight: pw.FontWeight.bold,
                          font: ttfBold),
                      textDirection: pw.TextDirection.ltr,
                      textAlign: pw.TextAlign.left),
                ]),
          ]),
      pw.Divider(height: 5),
      pw.Center(
          child: pw.Column(children: [
        pw.Text(
          'Supplier Payments Report',
          style: pw.TextStyle(
            fontSize: 3 * PdfPageFormat.mm,
            fontWeight: pw.FontWeight.bold,
            font: ttfBold,
          ),
          textDirection: pw.TextDirection.ltr,
        ),
      ])),
      pw.SizedBox(height: 7),
      pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          mainAxisAlignment: pw.MainAxisAlignment.start,
          children: [
            if (fromDate != 'null')
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
                pw.Text(
                  'From: ',
                  style: pw.TextStyle(
                      fontSize: 3.5 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttf),
                  textDirection: pw.TextDirection.ltr,
                ),
                pw.SizedBox(width: 5),
                pw.Text(
                  fromDate!,
                  style: pw.TextStyle(
                      fontSize: 3.5 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttf),
                  textDirection: pw.TextDirection.ltr,
                ),
              ]),
            if (toDate != 'null')
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
                pw.Text(
                  'To: ',
                  style: pw.TextStyle(
                      fontSize: 3.5 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttf),
                  textDirection: pw.TextDirection.ltr,
                ),
                pw.SizedBox(width: 5),
                pw.Text(
                  toDate!,
                  style: pw.TextStyle(
                      fontSize: 3.5 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttf),
                  textDirection: pw.TextDirection.ltr,
                ),
              ]),
            pw.SizedBox(height: 10),
            if (payments!.length < 35) ...[
              getEnSupplierPaymentsList(payments, ttf, isSales),
            ] else ...[
              getEnSupplierPaymentsList(
                  payments.take(35).toList(), ttf, isSales),
            ]
          ]),
    ]);
  }));

  for (int i = 0; i < payments!.length ~/ 45; i++) {
    var data = payments.skip(i * 45).take(45).toList();

    if (data.isNotEmpty) {
      addNewEnSupplierPaymentsPage(
          payments: payments,
          from: data.length + i * 45,
          to: payments.length - i * 45,
          ttf: ttf,
          isSales: isSales,
          doc: doc);
    }
  }
}

pw.Widget getEnSupplierPaymentsList(
    List<SupplierPaymentModel> payments, ttf, bool isSales) {
  pw.Widget cell(
    String text,
    bool header, {
    bool isRTL = false,
  }) =>
      pw.Text(
        text,
        maxLines: 1,
        style: pw.TextStyle(
            fontSize: header ? 3.5 * PdfPageFormat.mm : 2.9 * PdfPageFormat.mm,
            fontWeight: header ? pw.FontWeight.bold : pw.FontWeight.normal,
            font: ttf),
        textDirection: isRTL ? pw.TextDirection.rtl : pw.TextDirection.ltr,
      );

  final _invoiceWidget = pw.ListView(children: [
    pw.Table(columnWidths: {
      0: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
      1: const pw.FlexColumnWidth(0.55 * PdfPageFormat.mm),
      2: const pw.FlexColumnWidth(0.35 * PdfPageFormat.mm),
      3: const pw.FlexColumnWidth(0.4 * PdfPageFormat.mm),
    }, children: [
      pw.TableRow(children: [
        cell("Date and Time", true),
        pw.Center(
            child: cell(
          "Supplier Name",
          true,
          isRTL: true,
        )),
        cell("Amount", true),
        cell('Payment', true),
      ]),
    ]),
    pw.Divider(
        thickness: 0.5 * PdfPageFormat.mm, height: 0.5 * PdfPageFormat.mm),
    pw.Table(
        columnWidths: {
          0: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
          1: const pw.FlexColumnWidth(0.55 * PdfPageFormat.mm),
          2: const pw.FlexColumnWidth(0.35 * PdfPageFormat.mm),
          3: const pw.FlexColumnWidth(0.4 * PdfPageFormat.mm),
        },
        children: payments!
            .map((payment) => pw.TableRow(children: [
                  cell(
                      DateFormat("dd-MM-yyyy hh:mm:ss")
                          .format(payment.createdAt!),
                      false),
                  pw.Center(
                    child: cell(
                      payment.supplierName!,
                      false,
                      isRTL: true,
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(left: 10),
                    child: cell(payment.value!.toStringAsFixed(2), false),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(left: 10),
                    child: cell("Cash", false),
                  ),
                ]))
            .toList())
  ]);
  return _invoiceWidget;
}

void addNewEnSupplierPaymentsPage(
    {required List<SupplierPaymentModel> payments,
    required int from,
    required int to,
    required ttf,
    required isSales,
    required doc}) {
  if (payments.length > from && to < payments.length) {
    List<SupplierPaymentModel> temp = payments.skip(from).take(45).toList();
    doc.addPage(pw.Page(build: (pw.Context context) {
      return getEnSupplierPaymentsList(temp, ttf, isSales);
    }));
  } else {
    List<SupplierPaymentModel> remaining = payments.skip(from).toList();
    List<SupplierPaymentModel> temp =
        payments.skip(from).take(payments.length - remaining.length).toList();

    doc.addPage(pw.Page(build: (pw.Context context) {
      return getEnSupplierPaymentsList(
          payments.skip(from).take(temp.length).toList(), ttf, isSales);
    }));
    return;
  }
}
