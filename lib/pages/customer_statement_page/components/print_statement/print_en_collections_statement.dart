import 'package:intl/intl.dart';
import 'package:mandob/models/collection_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

import '../../../../models/company_info_model.dart';

Future<void> addEnCollectionsPdfPage(context,
    {doc,
    ttf,
    ttfBold,
    CompanyInfoModel? info,
    required List<CollectionModel>? collections,
    String? fromDate,
    String? toDate,
    required bool isSales}) async {
  doc.addPage(pw.Page(build: (pw.Context context) {
    return pw.Column(mainAxisSize: pw.MainAxisSize.max, children: [
      pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                mainAxisSize: pw.MainAxisSize.max,
                children: [
                  pw.Text(info!.companyName.toString(),
                      style: pw.TextStyle(
                          fontSize: 2.5 * PdfPageFormat.mm,
                          fontWeight: pw.FontWeight.bold,
                          font: ttfBold),
                      textDirection: pw.TextDirection.rtl,
                      textAlign: pw.TextAlign.left),
                  pw.SizedBox(height: 20),
                  pw.Text('VAT # ${info.vatNumber}',
                      style: pw.TextStyle(
                          fontSize: 2.8 * PdfPageFormat.mm,
                          fontWeight: pw.FontWeight.bold,
                          font: ttfBold),
                      textDirection: pw.TextDirection.rtl,
                      textAlign: pw.TextAlign.left),
                ]),
          ]),
      pw.Divider(height: 5),
      pw.Center(
          child: pw.Column(children: [
        pw.Text(
          'Collections Report',
          style: pw.TextStyle(
              fontSize: 3 * PdfPageFormat.mm,
              fontWeight: pw.FontWeight.bold,
              font: ttfBold),
          textDirection: pw.TextDirection.ltr,
        ),
      ])),
      pw.SizedBox(height: 7),
      pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          mainAxisAlignment: pw.MainAxisAlignment.start,
          children: [
            if (fromDate != 'null')
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
                pw.Text(
                  'From: ',
                  style: pw.TextStyle(
                      fontSize: 3.5 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttf),
                  textDirection: pw.TextDirection.ltr,
                ),
                pw.SizedBox(width: 5),
                pw.Text(
                  fromDate!,
                  style: pw.TextStyle(
                      fontSize: 3.5 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttf),
                  textDirection: pw.TextDirection.ltr,
                ),
              ]),
            if (toDate != 'null')
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
                pw.Text(
                  'To: ',
                  style: pw.TextStyle(
                      fontSize: 3.5 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttf),
                  textDirection: pw.TextDirection.ltr,
                ),
                pw.SizedBox(width: 5),
                pw.Text(
                  toDate!,
                  style: pw.TextStyle(
                      fontSize: 3.5 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttf),
                  textDirection: pw.TextDirection.ltr,
                ),
              ]),
            pw.SizedBox(height: 10),
            if (collections!.length < 35) ...[
              getEnCollectionsList(collections, ttf, isSales),
            ] else ...[
              getEnCollectionsList(collections.take(35).toList(), ttf, isSales),
            ]
          ]),
    ]);
  }));

  for (int i = 0; i < collections!.length ~/ 45; i++) {
    print('i = $i');
    print('collections.length = ${collections.length}');
    print('collections.length ~/ 45 = ${collections.length ~/ 45}');

    var data = collections.skip(i * 45).take(45).toList();
    print('data = ${data.length}');

    if (data.isNotEmpty) {
      print('collections.length - i * 45 = ${collections.length - i * 45}');
      addNewCollectionsPage(
          collections: collections,
          from: data.length + i * 45,
          to: collections.length - i * 45,
          ttf: ttf,
          isSales: isSales,
          doc: doc);
    }
  }
}

pw.Widget getEnCollectionsList(
    List<CollectionModel>? collections, ttf, bool isSales) {
  pw.Widget cell(String text, bool header, {bool isRtl = false}) => pw.Text(
        text,
        maxLines: 1,
        style: pw.TextStyle(
            fontSize: header ? 3.5 * PdfPageFormat.mm : 2.9 * PdfPageFormat.mm,
            fontWeight: header ? pw.FontWeight.bold : pw.FontWeight.normal,
            font: ttf),
        textDirection: isRtl ? pw.TextDirection.rtl : pw.TextDirection.ltr,
      );

  final _invoiceWidget = pw.ListView(children: [
    pw.Table(columnWidths: {
      0: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
      1: const pw.FlexColumnWidth(0.55 * PdfPageFormat.mm),
      2: const pw.FlexColumnWidth(0.35 * PdfPageFormat.mm),
      3: const pw.FlexColumnWidth(0.4 * PdfPageFormat.mm),
    }, children: [
      pw.TableRow(children: [
        cell("Date and Time", true),
        pw.Center(
            child: cell(
          isSales ? ("Customer Name") : ("Supplier Name"),
          true,
          isRtl: true,
        )),
        cell("Amount", true),
        cell('Payment', true),
      ]),
    ]),
    pw.Divider(
        thickness: 0.5 * PdfPageFormat.mm, height: 0.5 * PdfPageFormat.mm),
    pw.Table(
        columnWidths: {
          0: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
          1: const pw.FlexColumnWidth(0.55 * PdfPageFormat.mm),
          2: const pw.FlexColumnWidth(0.35 * PdfPageFormat.mm),
          3: const pw.FlexColumnWidth(0.4 * PdfPageFormat.mm),
        },
        children: collections!
            .map((invoice) => pw.TableRow(children: [
                  cell(
                      DateFormat("dd-MM-yyyy hh:mm:ss")
                          .format(invoice.createdAt!),
                      false),
                  pw.Center(
                      child: cell(
                    invoice.customerName!,
                    false,
                    isRtl: true,
                  )),
                  cell(
                    invoice.value!.toStringAsFixed(2),
                    false,
                  ),
                  cell("Cash", false),
                ]))
            .toList())
  ]);
  return _invoiceWidget;
}

void addNewCollectionsPage(
    {required List<CollectionModel>? collections,
    required int from,
    required int to,
    required ttf,
    required isSales,
    required doc}) {
  print('add new page ${collections!.length}');
  print('from $from to $to');
  if (collections.length > from && to < collections.length) {
    List<CollectionModel> temp = collections.skip(from).take(45).toList();
    doc.addPage(pw.Page(build: (pw.Context context) {
      return getEnCollectionsList(temp, ttf, isSales);
    }));
  } else {
    List<CollectionModel> remaining = collections.skip(from).toList();
    List<CollectionModel> temp = collections
        .skip(from)
        .take(collections.length - remaining.length)
        .toList();

    print('remaining: ${remaining.length}');
    print('temp: ${temp.length}');

    doc.addPage(pw.Page(build: (pw.Context context) {
      return getEnCollectionsList(
          collections.skip(from).take(temp.length).toList(), ttf, isSales);
    }));
    return;
  }
}
