import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:mandob/models/collection_model.dart';
import 'package:mandob/models/supplier_payment_model.dart';
import 'package:mandob/pages/customer_statement_page/components/print_statement/print_collections_statement.dart';
import 'package:mandob/pages/customer_statement_page/components/print_statement/print_supplier_payment_statement.dart';
import 'package:mandob/utils/extensions.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

import '../../../../models/company_info_model.dart';
import '../../../../models/invoice_model.dart';

Future printCustomerStatementPdf(context,
    {required CompanyInfoModel? info,
    required List<InvoiceModel>? receiptData,
    required List<CollectionModel>? collections,
    required List<SupplierPaymentModel> payments,
    required String? fromDate,
    required String? toDate,
    required bool isSales}) async {
  final doc = pw.Document();

  final font = await rootBundle.load("assets/fonts/cairo/Cairo-Regular.ttf");
  final fontBold = await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf");
  final ttf = pw.Font.ttf(font);
  final ttfBold = pw.Font.ttf(fontBold);

  await addPdfPage(context,
      doc: doc,
      ttf: ttf,
      ttfBold: ttfBold,
      info: info,
      fromDate: fromDate,
      toDate: toDate,
      receiptData: receiptData!,
      isSales: isSales);

  if (isSales) {
    await addCollectionsPdfPage(context,
        doc: doc,
        ttf: ttf,
        ttfBold: ttfBold,
        info: info,
        fromDate: fromDate,
        toDate: toDate,
        collections: collections,
        isSales: isSales);
  } else {
    await addSupplierPaymentsPdfPage(context,
        doc: doc,
        ttf: ttf,
        ttfBold: ttfBold,
        info: info,
        fromDate: fromDate,
        toDate: toDate,
        payments: payments,
        isSales: isSales);
  }

  await showDialog(
      context: context,
      builder: (_) => AlertDialog(
            backgroundColor: Colors.transparent,
            content: SizedBox(
              height: 500,
              width: 300,
              child: PdfPreview(
                previewPageMargin: const EdgeInsets.all(0),
                dynamicLayout: false,
                allowPrinting: true,
                canChangeOrientation: false,
                canChangePageFormat: false,
                canDebug: false,
                initialPageFormat: PdfPageFormat.a4,
                build: (format) => doc.save(),
                useActions: true,
              ),
            ),
          ));
}

Future<void> addPdfPage(context,
    {doc,
    ttf,
    ttfBold,
    CompanyInfoModel? info,
    required List<InvoiceModel>? receiptData,
    String? fromDate,
    String? toDate,
    required bool isSales}) async {
  doc.addPage(pw.Page(
      pageTheme: pw.PageTheme(
          pageFormat: PdfPageFormat.a4,
          theme: pw.ThemeData(bulletStyle: pw.TextStyle(font: ttf))),
      // pageFormat: PdfPageFormat.roll80,
      build: (pw.Context context) {
        return pw.Column(mainAxisSize: pw.MainAxisSize.max, children: [
          pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              crossAxisAlignment: pw.CrossAxisAlignment.center,
              children: [
                pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                    mainAxisSize: pw.MainAxisSize.max,
                    children: [
                      pw.Text(info!.companyName.toString(),
                          style: pw.TextStyle(
                              fontSize: 2.5 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttfBold),
                          textDirection: pw.TextDirection.rtl,
                          textAlign: pw.TextAlign.right),
                      pw.SizedBox(height: 20),
                      pw.Text('VAT # ${info.vatNumber}',
                          style: pw.TextStyle(
                              fontSize: 2.8 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttfBold),
                          textDirection: pw.TextDirection.rtl,
                          textAlign: pw.TextAlign.left),
                    ]),
              ]),
          pw.Divider(height: 5),
          pw.Center(
              child: pw.Column(children: [
            pw.Text(
              ' تقرير  ${isSales ? 'مبيعات' : 'مشتريات'} ',
              style: pw.TextStyle(
                  fontSize: 3 * PdfPageFormat.mm,
                  fontWeight: pw.FontWeight.bold,
                  font: ttfBold),
              textDirection: pw.TextDirection.rtl,
            ),
          ])),
          pw.SizedBox(height: 7),
          pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              mainAxisAlignment: pw.MainAxisAlignment.start,
              children: [
                if (fromDate != 'null')
                  pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.end,
                      children: [
                        pw.Text(
                          fromDate!,
                          style: pw.TextStyle(
                              fontSize: 3.5 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttf),
                          textDirection: pw.TextDirection.rtl,
                        ),
                        pw.SizedBox(width: 5),
                        pw.Text(
                          'من: ',
                          style: pw.TextStyle(
                              fontSize: 3.5 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttf),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ]),
                if (toDate != 'null')
                  pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.end,
                      children: [
                        pw.Text(
                          toDate!,
                          style: pw.TextStyle(
                              fontSize: 3.5 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttf),
                          textDirection: pw.TextDirection.rtl,
                        ),
                        pw.SizedBox(width: 5),
                        pw.Text(
                          'الى: ',
                          style: pw.TextStyle(
                              fontSize: 3.5 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttf),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ]),
                pw.SizedBox(height: 10),
                if (receiptData!.length < 35) ...[
                  getList(receiptData, ttf, isSales),
                ] else ...[
                  getList(receiptData.take(35).toList(), ttf, isSales),
                ]
              ]),
        ]);
      }));

  for (int i = 0; i < receiptData!.length ~/ 45; i++) {
    print('i = $i');
    print('receiptData.length = ${receiptData.length}');
    print('receiptData.length ~/ 45 = ${receiptData.length ~/ 45}');

    var data = receiptData.skip(i * 45).take(45).toList();
    print('data = ${data.length}');

    if (data.isNotEmpty) {
      print('receiptData.length - i * 45 = ${receiptData.length - i * 45}');
      addNewPage(
          receiptData: receiptData,
          from: data.length + i * 45,
          to: receiptData.length - i * 45,
          ttf: ttf,
          isSales: isSales,
          doc: doc);
    }
  }
}

pw.Widget getList(List<InvoiceModel>? receiptData, ttf, bool isSales) {
  pw.Widget cell(String text, bool header) => pw.Text(
        text,
        maxLines: 1,
        style: pw.TextStyle(
            fontSize: header ? 3.5 * PdfPageFormat.mm : 2.9 * PdfPageFormat.mm,
            fontWeight: header ? pw.FontWeight.bold : pw.FontWeight.normal,
            font: ttf),
        textDirection: pw.TextDirection.rtl,
      );

  final _invoiceWidget = pw.ListView(children: [
    pw.Table(columnWidths: {
      0: const pw.FlexColumnWidth(0.41 * PdfPageFormat.mm),
      1: const pw.FlexColumnWidth(0.3 * PdfPageFormat.mm),
      2: const pw.FlexColumnWidth(0.55 * PdfPageFormat.mm),
      3: const pw.FlexColumnWidth(0.25 * PdfPageFormat.mm),
      4: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
    }, children: [
      pw.TableRow(children: [
        // cell("الاجمالى", true),
        // cell("الإجمالي بدون ضريبة", true),
        cell('الدفع', true),
        cell("المبلغ / اجل", true),
        pw.Center(child: cell(isSales ? ("اسم العميل") : ("اسم المورد"), true)),
        cell("التاريخ والوقت", true),
      ]),
    ]),
    pw.Divider(
        thickness: 0.5 * PdfPageFormat.mm, height: 0.5 * PdfPageFormat.mm),
    pw.Table(
        columnWidths: {
          0: const pw.FlexColumnWidth(0.41 * PdfPageFormat.mm),
          1: const pw.FlexColumnWidth(0.3 * PdfPageFormat.mm),
          2: const pw.FlexColumnWidth(0.6 * PdfPageFormat.mm),
          3: const pw.FlexColumnWidth(0.25 * PdfPageFormat.mm),
          4: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
        },
        children: receiptData!
            .map((invoice) => pw.TableRow(children: [
                  cell(
                      invoice.invoiceType.toString() == 'cash' ? "نقدا" : "آجل",
                      false),
                  cell(
                      invoice.paidCash!.roundedPrecisionToString(5) +
                          (invoice.invoiceType!.toLowerCase() != 'cash'
                              ? " / ${(invoice.totalPrice! - invoice.paidCash!).roundedPrecisionToString(5)}"
                              : ''),
                      false),
                  // cell('${invoice.totalWithoutTax!.toStringAsFixed(2)}${context.currency}',
                  //             false),
                  pw.Center(child: cell(invoice.customerName!, false)),
                  cell(
                      DateFormat("dd-MM-yyyy hh:mm:ss")
                          .format(DateTime.parse(invoice.createdAt!)),
                      false),
                ]))
            .toList())
  ]);
  return _invoiceWidget;
}

void addNewPage(
    {required List<InvoiceModel>? receiptData,
    required int from,
    required int to,
    required ttf,
    required isSales,
    required doc}) {
  print('add new page ${receiptData!.length}');
  print('from $from to $to');
  if (receiptData.length > from && to < receiptData.length) {
    List<InvoiceModel> temp = receiptData.skip(from).take(45).toList();
    doc.addPage(pw.Page(build: (pw.Context context) {
      return getList(temp, ttf, isSales);
    }));
  } else {
    List<InvoiceModel> remaining = receiptData.skip(from).toList();
    List<InvoiceModel> temp = receiptData
        .skip(from)
        .take(receiptData.length - remaining.length)
        .toList();

    print('remaining: ${remaining.length}');
    print('temp: ${temp.length}');

    doc.addPage(pw.Page(build: (pw.Context context) {
      return getList(
          receiptData.skip(from).take(temp.length).toList(), ttf, isSales);
    }));
    return;
  }
}
