import 'package:intl/intl.dart';
import 'package:mandob/models/collection_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

import '../../../../models/company_info_model.dart';

Future<void> addCollectionsPdfPage(context,
    {doc,
    ttf,
    ttfBold,
    CompanyInfoModel? info,
    required List<CollectionModel>? collections,
    String? fromDate,
    String? toDate,
    required bool isSales}) async {
  doc.addPage(pw.Page(build: (pw.Context context) {
    return pw.Column(mainAxisSize: pw.MainAxisSize.max, children: [
      pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                mainAxisSize: pw.MainAxisSize.max,
                children: [
                  pw.Text(info!.companyName.toString(),
                      style: pw.TextStyle(
                          fontSize: 2.5 * PdfPageFormat.mm,
                          fontWeight: pw.FontWeight.bold,
                          font: ttfBold),
                      textDirection: pw.TextDirection.rtl,
                      textAlign: pw.TextAlign.right),
                  pw.SizedBox(height: 20),
                  pw.Text('VAT # ${info.vatNumber}',
                      style: pw.TextStyle(
                          fontSize: 2.8 * PdfPageFormat.mm,
                          fontWeight: pw.FontWeight.bold,
                          font: ttfBold),
                      textDirection: pw.TextDirection.rtl,
                      textAlign: pw.TextAlign.left),
                ]),
          ]),
      pw.Divider(height: 5),
      pw.Center(
          child: pw.Column(children: [
        pw.Text(
          ' تقرير تحصيلات ',
          style: pw.TextStyle(
              fontSize: 3 * PdfPageFormat.mm,
              fontWeight: pw.FontWeight.bold,
              font: ttfBold),
          textDirection: pw.TextDirection.rtl,
        ),
      ])),
      pw.SizedBox(height: 7),
      pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          mainAxisAlignment: pw.MainAxisAlignment.start,
          children: [
            if (fromDate != 'null')
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
                pw.Text(
                  fromDate!,
                  style: pw.TextStyle(
                      fontSize: 3.5 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttf),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(width: 5),
                pw.Text(
                  'من: ',
                  style: pw.TextStyle(
                      fontSize: 3.5 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttf),
                  textDirection: pw.TextDirection.rtl,
                ),
              ]),
            if (toDate != 'null')
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
                pw.Text(
                  toDate!,
                  style: pw.TextStyle(
                      fontSize: 3.5 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttf),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(width: 5),
                pw.Text(
                  'الى: ',
                  style: pw.TextStyle(
                      fontSize: 3.5 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttf),
                  textDirection: pw.TextDirection.rtl,
                ),
              ]),
            pw.SizedBox(height: 10),
            if (collections!.length < 35) ...[
              getCollectionsList(collections, ttf, isSales),
            ] else ...[
              getCollectionsList(collections.take(35).toList(), ttf, isSales),
            ]
          ]),
    ]);
  }));

  for (int i = 0; i < collections!.length ~/ 45; i++) {
    print('i = $i');
    print('receiptData.length = ${collections.length}');
    print('receiptData.length ~/ 45 = ${collections.length ~/ 45}');

    var data = collections.skip(i * 45).take(45).toList();
    print('data = ${data.length}');

    if (data.isNotEmpty) {
      print('receiptData.length - i * 45 = ${collections.length - i * 45}');
      addNewCollectionsPage(
          collections: collections,
          from: data.length + i * 45,
          to: collections.length - i * 45,
          ttf: ttf,
          isSales: isSales,
          doc: doc);
    }
  }
}

pw.Widget getCollectionsList(
    List<CollectionModel>? collections, ttf, bool isSales) {
  pw.Widget cell(String text, bool header) => pw.Text(
        text,
        maxLines: 1,
        style: pw.TextStyle(
            fontSize: header ? 3.5 * PdfPageFormat.mm : 2.9 * PdfPageFormat.mm,
            fontWeight: header ? pw.FontWeight.bold : pw.FontWeight.normal,
            font: ttf),
        textDirection: pw.TextDirection.rtl,
      );

  final _invoiceWidget = pw.ListView(children: [
    pw.Table(columnWidths: {
      0: const pw.FlexColumnWidth(0.41 * PdfPageFormat.mm),
      1: const pw.FlexColumnWidth(0.3 * PdfPageFormat.mm),
      2: const pw.FlexColumnWidth(0.62 * PdfPageFormat.mm),
      3: const pw.FlexColumnWidth(0.27 * PdfPageFormat.mm),
      4: const pw.FlexColumnWidth(0.6 * PdfPageFormat.mm),
    }, children: [
      pw.TableRow(children: [
        cell('الدفع', true),
        cell("المبلغ", true),
        pw.Center(child: cell(isSales ? ("اسم العميل") : ("اسم المورد"), true)),
        cell("التاريخ والوقت", true),
      ]),
    ]),
    pw.Divider(
        thickness: 0.5 * PdfPageFormat.mm, height: 0.5 * PdfPageFormat.mm),
    pw.Table(
        columnWidths: {
          0: const pw.FlexColumnWidth(0.41 * PdfPageFormat.mm),
          1: const pw.FlexColumnWidth(0.3 * PdfPageFormat.mm),
          2: const pw.FlexColumnWidth(0.6 * PdfPageFormat.mm),
          3: const pw.FlexColumnWidth(0.25 * PdfPageFormat.mm),
          4: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
        },
        children: collections!
            .map((invoice) => pw.TableRow(children: [
                  cell("نقدا", false),
                  cell(invoice.value!.toStringAsFixed(2), false),
                  pw.Center(child: cell(invoice.customerName!, false)),
                  cell(
                      DateFormat("dd-MM-yyyy hh:mm:ss")
                          .format(invoice.createdAt!),
                      false),
                ]))
            .toList())
  ]);
  return _invoiceWidget;
}

void addNewCollectionsPage(
    {required List<CollectionModel>? collections,
    required int from,
    required int to,
    required ttf,
    required isSales,
    required doc}) {
  print('add new page ${collections!.length}');
  print('from $from to $to');
  if (collections.length > from && to < collections.length) {
    List<CollectionModel> temp = collections.skip(from).take(45).toList();
    doc.addPage(pw.Page(build: (pw.Context context) {
      return getCollectionsList(temp, ttf, isSales);
    }));
  } else {
    List<CollectionModel> remaining = collections.skip(from).toList();
    List<CollectionModel> temp = collections
        .skip(from)
        .take(collections.length - remaining.length)
        .toList();

    print('remaining: ${remaining.length}');
    print('temp: ${temp.length}');

    doc.addPage(pw.Page(build: (pw.Context context) {
      return getCollectionsList(
          collections.skip(from).take(temp.length).toList(), ttf, isSales);
    }));
    return;
  }
}
