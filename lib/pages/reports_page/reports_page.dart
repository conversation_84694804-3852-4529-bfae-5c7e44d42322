import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/invoice_model.dart';
import 'package:mandob/pages/main_components/price_card.dart';
import 'package:mandob/pages/reports_page/components/filter_bottom_sheet.dart';
import 'package:mandob/providers/reports_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:mandob/widgets/date_widget.dart';
import 'package:persian_number_utility/persian_number_utility.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../models/company_info_model.dart';
import '../../providers/company_provider.dart';
import '../../utils/app_bar.dart';

ValueNotifier<DateTime> sReportDate =
    ValueNotifier<DateTime>(DateTime.now().subtract(const Duration(days: 1)));
ValueNotifier<DateTime> eReportDate = ValueNotifier<DateTime>(DateTime.now());

bool isThreeMonthInvoice = false;

class ReportsPage extends StatefulWidget {
  final bool isSales;

  const ReportsPage({
    super.key,
    this.isSales = false,
  });

  @override
  ReportsPageState createState() => ReportsPageState();
}

class ReportsPageState extends State<ReportsPage> {
  String? type;
  List<InvoiceModel> invoicesData = [];
  bool loading = false;
  late Future<void> _invoiceDataFuture;

  @override
  void initState() {
    super.initState();
    sReportDate.value = DateTime.now().subtract(const Duration(days: 1));
    eReportDate.value = DateTime.now();
    final companyProvider =
        Provider.of<CompanyInfoProvider>(context, listen: false);
    companyProvider.fetchCompanyInfo();

    setData();
  }

  void setData() async {
    _invoiceDataFuture = _getInvoiceData();

    Future.delayed(Duration.zero, () {
      _updateInvoiceData();
    });
  }

  Future<void> _getInvoiceData() async {
    final invoicesProvider =
        Provider.of<ReportsProvider>(context, listen: false);

    setState(() {
      loading = true;
    });

    invoicesData = await invoicesProvider.fetchReportInvoices(
      isSales: widget.isSales,
      isReturned: type == (context.isEng ? 'Returns' : 'مرتجعات'),
      notifyListener: false,
      startDate: sReportDate.value,
      endDate: eReportDate.value,
    );

    setState(() {
      loading = false;
    });
  }

  void _updateInvoiceData() async {
    setState(() {
      loading = true;
      _invoiceDataFuture = _getInvoiceData();
    });
  }

  @override
  Widget build(BuildContext context) {
    final _user = Provider.of<UserProvider>(context).activeUser;
    CompanyInfoModel? compInfo =
        Provider.of<CompanyInfoProvider>(context, listen: false).companyInfo;

    double total = 0;
    double totalAfterTax = 0;
    double taxVal = 0;

    type ??= context.isEng ? 'All Invoices' : 'كل الفواتير';

    return FutureBuilder(
      future: _invoiceDataFuture,
      builder: (context, data) {
        List<InvoiceModel> invoices = invoicesData;
        if (total == 0 && totalAfterTax == 0) {
          for (var element in invoices) {
            total = total + element.totalWithoutTax!;
          }
          for (var element in invoices) {
            totalAfterTax = totalAfterTax + element.totalPrice!;
          }
          taxVal = totalAfterTax - total;
        }

        return Scaffold(
          body: Column(
            children: [
              appBarWidget(
                context,
                title: widget.isSales
                    ? (context.isEng ? 'Sales Reports' : 'تقارير المبيعات')
                    : (context.isEng ? 'Purchase Reports' : 'تقارير المشتريات'),
                action: IconButton(
                    icon: const Icon(
                      Icons.print,
                      color: ColorManager.secondaryColor,
                      size: 30,
                    ),
                    onPressed: () {
                      showPrintBottomSheet(
                          context, compInfo, _user, invoices, widget.isSales);
                    }),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 15.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: PriceCard(
                          price: total,
                          title: widget.isSales
                              ? context.isEng
                                  ? 'Total Sales'
                                  : 'إجمالي المبيعات'
                              : (context.isEng
                                  ? 'Total Purchases'
                                  : 'إجمالي المشتريات'),
                          color: Colors.black,
                        )),
                        Expanded(
                            child: PriceCard(
                                price: taxVal.abs(),
                                title: context.isEng
                                    ? 'Total VAT'
                                    : 'اجمالي ض.ق.م.')),
                        Expanded(
                            child: PriceCard(
                          price: totalAfterTax,
                          title: widget.isSales
                              ? (context.isEng ? 'Net Sales' : 'صافي المبيعات')
                              : (context.isEng
                                  ? 'Net Purchases'
                                  : 'صافي المشتريات'),
                          color: Colors.black,
                        )),
                      ],
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12.0),
                child: UiWidgets.datePicker(context, _updateInvoiceData,
                    startDate: sReportDate, endDate: eReportDate),
              ),
              AppGaps.gap12,
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.blueGrey.shade50,
                  borderRadius: BorderRadius.circular(10),
                ),
                margin: const EdgeInsets.symmetric(horizontal: 12.0),
                child: DropdownButton<String>(
                    value: type,
                    underline: Container(),
                    isExpanded: true,
                    items: [
                      ...(widget.isSales
                              ? [
                                  context.isEng
                                      ? 'All Invoices'
                                      : 'كل الفواتير',
                                  context.isEng ? 'Returns' : 'مرتجعات',
                                  context.isEng ? 'Sales' : 'مبيعات'
                                ]
                              : [
                                  context.isEng
                                      ? 'All Invoices'
                                      : 'كل الفواتير',
                                  context.isEng ? 'Returns' : 'مرتجعات',
                                  context.isEng ? 'Purchases' : 'مشتريات'
                                ])
                          .map((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(
                            value,
                            style: const TextStyle(color: Colors.black),
                          ),
                        );
                      })
                    ],
                    onChanged: (val) {
                      setState(() {
                        type = val!;
                        _updateInvoiceData();
                      });
                    }),
              ),
              AppGaps.gap8,
              const Divider(
                thickness: 1,
                color: ColorManager.lightFieldColor,
              ).paddingSymmetric(horizontal: 12),
              AppGaps.gap8,
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12.0),
                  child: Column(
                    children: [
                      Table(
                        border: TableBorder.all(color: Colors.grey),
                        columnWidths: const {
                          0: FlexColumnWidth(1.5),
                          1: FlexColumnWidth(2),
                          2: FlexColumnWidth(1.2),
                          3: FlexColumnWidth(1.4),
                          4: FlexColumnWidth(1.4),
                        },
                        children: [
                          TableRow(
                            decoration: const BoxDecoration(
                              color: Color(0xFFdfdfe3),
                            ),
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    context.isEng ? 'Date' : 'التاريخ',
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    widget.isSales
                                        ? (context.isEng
                                            ? 'Customer'
                                            : 'العميل')
                                        : (context.isEng
                                            ? 'Supplier'
                                            : 'المورد'),
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    context.isEng ? 'Net' : 'الصافي',
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    (context.isEng ? 'VAT' : 'ض.ق.م.'),
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    context.isEng ? 'Total' : 'الاجمالى',
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      invoicesWidget(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget invoicesWidget() {
    return Expanded(
      child: Builder(builder: (context) {
        if (loading) {
          return const LoadingWidget();
        }

        return Stack(
          children: [
            SingleChildScrollView(
              child: Table(
                border: TableBorder.all(color: Colors.grey),
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                columnWidths: const {
                  0: FlexColumnWidth(1.5),
                  1: FlexColumnWidth(2),
                  2: FlexColumnWidth(1.2),
                  3: FlexColumnWidth(1.4),
                  4: FlexColumnWidth(1.4),
                },
                children: invoicesData.map((invoice) {
                  return TableRow(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            invoice.createdAt.toString().substring(0, 10),
                            style: const TextStyle(
                              fontFamily: 'Droid',
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          invoice.customerName ?? '',
                          style: const TextStyle(
                            fontFamily: 'Droid',
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            '${invoice.totalWithoutTax?.toStringAsFixed(2).seRagham()}${context.currency}',
                            style: const TextStyle(
                              fontFamily: 'Droid',
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            '${((invoice.totalPrice ?? 0) - (invoice.totalWithoutTax ?? 0)).toStringAsFixed(2).seRagham()}${context.currency}',
                            style: TextStyle(
                              fontFamily: 'Droid',
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                              color: widget.isSales
                                  ? ColorManager.errorColor
                                  : ColorManager.successColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            '${(invoice.totalPrice?.toStringAsFixed(2)?.seRagham())!}${context.currency}',
                            style: const TextStyle(
                              fontFamily: 'Droid',
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
            if (loading) ...[
              const Align(
                  alignment: Alignment.bottomCenter, child: LoadingWidget()),
            ]
          ],
        );
      }),
    );
  }
}
