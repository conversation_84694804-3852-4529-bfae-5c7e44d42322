import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/core/shared/language/change_language.widget.dart';
import 'package:mandob/pages/reports_page/components/print/en_print_pdf.dart';
import 'package:mandob/pages/reports_page/components/print/excel_sheet.dart';
import 'package:mandob/pages/reports_page/components/print/print_ar_vat_pdf.dart';
import 'package:mandob/pages/reports_page/components/print/print_en_vat_pdf.dart';
import 'package:mandob/pages/reports_page/components/print/print_pdf.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../widgets/date_widget.dart';
import '../reports_page.dart';

void showPrintBottomSheet(
  BuildContext context,
  compInfo,
  user,
  invoices,
  isSales,
) {
  final threeMonthSReportDate = ValueNotifier<DateTime>(
      DateTime.now().subtract(const Duration(days: 90)));
  final threeMonthEReportDate = ValueNotifier<DateTime>(DateTime.now());

  final yearParts = [
    context.isEng ? 'First Quarter' : 'الربع الاول',
    context.isEng ? 'Second Quarter' : 'الربع الثاني',
    context.isEng ? 'Third Quarter' : 'الربع الثالث',
    context.isEng ? 'Fourth Quarter' : 'الربع الرابع',
  ];

  showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState) {
          return HookBuilder(builder: (context) {
            final selectedPart = useState<String?>(null);
            final startDate = useState<DateTime>(sReportDate.value);
            final endDate = useState<DateTime>(eReportDate.value);

            return SizedBox(
                width: double.infinity,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppSpaces.padding16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(
                        height: 30,
                      ),
                      Text(
                        context.isEng
                            ? 'Report ${isSales ? 'Sales' : 'Purchases'}'
                            : 'تقرير ${isSales ? 'المبيعات' : 'المشتريات'}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Text(
                          '${context.isEng ? 'From' : 'من'} ${startDate.value.toString().split(" ").first} ${context.isEng ? 'to' : 'الى'} ${endDate.value.toString().split(" ").first}'),
                      const SizedBox(
                        height: 20,
                      ),
                      UiWidgets.datePicker(
                        context,
                        (value) {
                          if (value.startDate != null) {
                            sReportDate.value = value.startDate;
                          }
                          if (value.endDate != null) {
                            eReportDate.value = value.endDate;
                          } else {
                            eReportDate.value = DateTime.now();
                          }
                          setState(() {
                            isThreeMonthInvoice = false;
                          });
                        },
                        label: context.isEng
                            ? 'Select Date Range'
                            : 'اختر تاريخ من والى',
                        startDate: startDate,
                        endDate: endDate,
                      ),
                      const SizedBox(
                        height: 40,
                      ),
                      Text(
                        context.isEng ? 'Select Period' : 'اختر الفترة',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      SizedBox(
                        height: 45,
                        child: ListView(
                          scrollDirection: Axis.horizontal,
                          children: yearParts.map(
                            (e) {
                              final isSelectedPart = selectedPart.value == e;

                              return Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: AppSpaces.padding4),
                                child: ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: isSelectedPart
                                            ? ColorManager.primaryColor
                                            : ColorManager.secondaryColor),
                                    onPressed: () {
                                      if (selectedPart.value == e) {
                                        selectedPart.value = null;
                                        startDate.value = sReportDate.value;
                                        endDate.value = eReportDate.value;
                                        return;
                                      }

                                      selectedPart.value = e;

                                      if (e ==
                                          (context.isEng
                                              ? 'First Quarter'
                                              : 'الربع الاول')) {
                                        startDate.value =
                                            DateTime(DateTime.now().year, 1, 1);
                                        endDate.value = DateTime(
                                            DateTime.now().year, 3, 31);
                                      } else if (e ==
                                          (context.isEng
                                              ? 'Second Quarter'
                                              : 'الربع الثاني')) {
                                        startDate.value =
                                            DateTime(DateTime.now().year, 4, 1);
                                        endDate.value = DateTime(
                                            DateTime.now().year, 6, 30);
                                      } else if (e ==
                                          (context.isEng
                                              ? 'Third Quarter'
                                              : 'الربع الثالث')) {
                                        startDate.value =
                                            DateTime(DateTime.now().year, 7, 1);
                                        endDate.value = DateTime(
                                            DateTime.now().year, 9, 30);
                                      } else if (e ==
                                          (context.isEng
                                              ? 'Fourth Quarter'
                                              : 'الربع الرابع')) {
                                        startDate.value = DateTime(
                                            DateTime.now().year, 10, 1);
                                        endDate.value = DateTime(
                                            DateTime.now().year, 12, 31);
                                      }
                                    },
                                    child: Text(
                                      e,
                                      style: const TextStyle(
                                        color: Colors.white,
                                      ),
                                    )),
                              );
                            },
                          ).toList(),
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      const Divider(
                        thickness: 1,
                        color: ColorManager.lightFieldColor,
                      ).paddingSymmetric(horizontal: 12),
                      const SizedBox(
                        height: 20,
                      ),
                      Text(
                        context.isEng ? 'File Format' : 'صيغة الملف',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      OutlinedButton(
                        style: ElevatedButton.styleFrom(
                          side: const BorderSide(
                            color: ColorManager.secondaryColor,
                          ),
                        ),
                        onPressed: () {
                          showChangeLanguageDialog(
                            context,
                            onArTap: () {
                              printArVatPdfInvoice(context,
                                  info: compInfo,
                                  fromDate: startDate.value,
                                  toDate: endDate.value,
                                  isSales: isSales);
                            },
                            onEnTap: () {
                              printEnVatPdfInvoice(context,
                                  info: compInfo,
                                  fromDate: startDate.value,
                                  toDate: endDate.value,
                                  isSales: isSales);
                            },
                          );
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              context.isEng
                                  ? 'Print VAT Report'
                                  : 'طباعة تقرير الضريبة',
                              style: const TextStyle(
                                color: ColorManager.secondaryColor,
                              ),
                            ),
                            const SizedBox(
                              width: 20,
                            ),
                            const Icon(
                              FontAwesomeIcons.filePdf,
                              color: ColorManager.secondaryColor,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          OutlinedButton(
                            style: ElevatedButton.styleFrom(
                              side: const BorderSide(
                                color: ColorManager.secondaryColor,
                              ),
                            ),
                            onPressed: () {
                              showChangeLanguageDialog(
                                context,
                                onArTap: () {
                                  printPdfInvoice(context,
                                      info: compInfo,
                                      fromDate: startDate.value,
                                      toDate: endDate.value,
                                      isSales: isSales);
                                },
                                onEnTap: () {
                                  printEnPdfInvoice(context,
                                      info: compInfo,
                                      fromDate: startDate.value,
                                      toDate: endDate.value,
                                      isSales: isSales);
                                },
                              );
                            },
                            child: Row(
                              children: [
                                Text(
                                  context.isEng ? 'Print' : 'طباعة',
                                  style: const TextStyle(
                                    color: ColorManager.secondaryColor,
                                  ),
                                ),
                                const SizedBox(
                                  width: 10,
                                ),
                                const Icon(
                                  FontAwesomeIcons.filePdf,
                                  color: ColorManager.secondaryColor,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            width: 20,
                          ),
                          OutlinedButton(
                              style: ElevatedButton.styleFrom(
                                side: const BorderSide(
                                  color: ColorManager.primaryColor,
                                ),
                              ),
                              onPressed: () {
                                saveExcelSheet(
                                  context,
                                  isSales: isSales,
                                  fromDate: startDate.value,
                                  toDate: endDate.value,
                                );
                              },
                              child: Row(
                                children: [
                                  Text(
                                    context.isEng ? 'File' : 'ملف',
                                    style: const TextStyle(
                                      color: ColorManager.primaryColor,
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  const Icon(
                                    FontAwesomeIcons.fileExcel,
                                    color: ColorManager.primaryColor,
                                  ),
                                ],
                              )),
                        ],
                      ),
                      const SizedBox(
                        height: 40,
                      )
                    ],
                  ),
                ));
          });
        });
      });
}
