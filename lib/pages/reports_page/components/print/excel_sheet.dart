import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:intl/intl.dart';
import 'package:mandob/utils/extensions.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart';

import '../../../../providers/reports_provider.dart';

void saveExcelSheet(
  BuildContext context, {
  required bool isSales,
  required DateTime fromDate,
  required DateTime toDate,
}) async {
  final invoiceModel =
      await context.read<ReportsProvider>().fetchReportInvoices(
            startDate: fromDate,
            endDate: toDate,
            isSales: isSales,
          );

  // Create a new Excel document.
  final Workbook workbook = Workbook();
//Accessing worksheet via index.
  var sheet = workbook.worksheets[0];
// Save the document.
  sheet.getRangeByIndex(1, 1)
    ..setText('الاجمالى')
    ..columnWidth = 20
    ..cellStyle.hAlign = HAlignType.center
    ..cellStyle.bold = true;
  sheet.getRangeByIndex(1, 2)
    ..setText('الإجمالي قبل الضريبة')
    ..columnWidth = 20
    ..cellStyle.hAlign = HAlignType.center
    ..cellStyle.bold = true;
  sheet.getRangeByIndex(1, 3)
    ..setText(isSales ? "اسم العميل" : "اسم المورد")
    ..cellStyle.hAlign = HAlignType.center
    ..columnWidth = 20
    ..cellStyle.bold = true;
  sheet.getRangeByIndex(1, 4)
    ..setText('التاريخ والوقت')
    ..columnWidth = 20
    ..cellStyle.hAlign = HAlignType.center
    ..cellStyle.bold = true;
  sheet.getRangeByIndex(1, 5)
    ..setText('رقم الفاتورة')
    ..columnWidth = 15
    ..cellStyle.bold = true
    ..cellStyle.hAlign = HAlignType.center;
  sheet.getRangeByIndex(1, 6)
    ..setText('م')
    ..columnWidth = 15
    ..cellStyle.bold = true
    ..cellStyle.hAlign = HAlignType.center;

  double totalPriceInvoices = 0;

  for (var i = 0; i < invoiceModel.length; i++) {
    totalPriceInvoices = totalPriceInvoices + invoiceModel[i].totalPrice!;

    //Total Price
    sheet.getRangeByIndex(i == 0 ? 2 : i + 1, 1)
      ..setText(invoiceModel[i].totalPrice!.roundedPrecisionToString(5))
      ..cellStyle.hAlign = HAlignType.center;
    //Total Price Without Tax
    sheet.getRangeByIndex(i == 0 ? 2 : i + 1, 2)
      ..setText(invoiceModel[i].totalWithoutTax!.roundedPrecisionToString(5))
      ..cellStyle.hAlign = HAlignType.center;
    //Customer Name
    sheet.getRangeByIndex(i == 0 ? 2 : i + 1, 3)
      ..setText(invoiceModel[i].customerName)
      ..cellStyle.hAlign = HAlignType.center;
    //Date Time
    sheet.getRangeByIndex(i == 0 ? 2 : i + 1, 4).setText(
        DateFormat("dd-MM-yyyy hh:mm:ss")
            .format(DateTime.parse(invoiceModel[i].createdAt!)));
    //Invoice Number
    sheet.getRangeByIndex(i == 0 ? 2 : i + 1, 5)
      ..setText(invoiceModel[i].invoiceNumber == null
          ? invoiceModel[i].invoiceId.toString().substring(1, 7)
          : invoiceModel[i].invoiceNumber.toString())
      ..cellStyle.hAlign = HAlignType.center;
    //Index Number
    sheet.getRangeByIndex(i == 0 ? 2 : i + 1, 6)
      ..setText(i.toString())
      ..cellStyle.hAlign = HAlignType.center;
  }

  sheet.getRangeByIndex(invoiceModel.length + 1, 1)
    ..setText(totalPriceInvoices.roundedPrecisionToString(5))
    ..cellStyle.hAlign = HAlignType.center;

  sheet.getRangeByIndex(invoiceModel.length + 1, 2)
    ..setText('الإجمالى')
    ..cellStyle.bold = true
    ..cellStyle.hAlign = HAlignType.center;

  var status = await Permission.storage.status;

  await Permission.storage.request();

  if (status.isGranted) {
    final Directory appDocDir = await getApplicationDocumentsDirectory();
    final String appDocPath = appDocDir.path;
    final String path = '$appDocPath/${fromDate.toIso8601String()}.xlsx';

    final List<int> bytes = workbook.saveAsStream();
    await File(path).writeAsBytes(bytes);
    print('Excel sheet saved at $path');

    // await Share.shareFiles([path], text: 'Great Sheet');
    await Share.share(path);
  } else {
    print('Permission Denied');
  }
  if (status.isDenied) {
    await Permission.storage.request();
  }

  workbook.dispose();
}
