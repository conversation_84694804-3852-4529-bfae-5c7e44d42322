import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

Future<void> generateBestSellersPdf(
    BuildContext context, List sortedProducts) async {
  final doc = pw.Document();

  int i = 0;

  while (i < sortedProducts.length) {
    final products = sortedProducts.skip(i).take(45).toList();
    i += 45;
    addPage(doc, products);
  }

  await showDialog(
      context: context,
      builder: (_) => AlertDialog(
            backgroundColor: Colors.transparent,
            content: SizedBox(
              height: 500,
              width: 300,
              child: PdfPreview(
                previewPageMargin: const EdgeInsets.all(0),
                dynamicLayout: false,
                allowPrinting: true,
                canChangeOrientation: false,
                canChangePageFormat: false,
                canDebug: false,
                initialPageFormat: PdfPageFormat.a4,
                build: (format) => doc.save(),
                useActions: true,
                pdfFileName: "تقرير اكثر المنتجات مبيعا.pdf",
              ),
            ),
          ));
}

addPage(doc, sortedProducts) async {
  final font = await rootBundle.load("assets/fonts/cairo/Cairo-Regular.ttf");

  final ttf = pw.Font.ttf(font);

  pw.Widget cell(String text, bool header) => pw.Text(
        text,
        maxLines: 1,
        style: pw.TextStyle(
            fontSize: header ? 3.5 * PdfPageFormat.mm : 2.9 * PdfPageFormat.mm,
            fontWeight: header ? pw.FontWeight.bold : pw.FontWeight.normal,
            font: ttf),
        textDirection: pw.TextDirection.rtl,
      );
  doc.addPage(
    pw.Page(
      build: (pw.Context context) {
        return pw.ListView(
          children: [
            pw.Table(columnWidths: {
              0: const pw.FlexColumnWidth(1),
              1: const pw.FlexColumnWidth(1),
              2: const pw.FlexColumnWidth(2),
            }, children: [
              pw.TableRow(children: [
                cell("السعر", true),
                cell("الكمية", true),
                cell("اسم الصنف", true),
              ]),
            ]),
            pw.Divider(
                thickness: 0.5 * PdfPageFormat.mm,
                height: 0.5 * PdfPageFormat.mm),
            pw.Table(
              columnWidths: {
                0: const pw.FlexColumnWidth(1),
                1: const pw.FlexColumnWidth(1),
                2: const pw.FlexColumnWidth(2),
              },
              children: List.generate(
                sortedProducts.length,
                (index) => pw.TableRow(children: [
                  cell(
                      sortedProducts[index]['price'].toStringAsFixed(2), false),
                  cell(sortedProducts[index]['quantity'].toString(), false),
                  cell(sortedProducts[index]['name'], false),
                ]),
              ),
            ),
          ],
        );
      },
    ),
  );
}
