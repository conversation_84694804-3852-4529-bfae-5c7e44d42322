import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/providers/reports_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/extensions.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../models/company_info_model.dart';
import '../../../../models/invoice_model.dart';

Future printPdfInvoice(BuildContext context,
    {required CompanyInfoModel? info,
    required DateTime? fromDate,
    required DateTime? toDate,
    required bool isSales}) async {
  final receiptData = await context.read<ReportsProvider>().fetchReportInvoices(
        startDate: fromDate,
        endDate: toDate,
        isSales: isSales,
      );

  final doc = pw.Document();

  final font = await rootBundle.load("assets/fonts/cairo/Cairo-Regular.ttf");
  final fontBold = await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf");

  final ttf = pw.Font.ttf(font);
  final ttfBold = pw.Font.ttf(fontBold);

  await addPdfPage(context,
      doc: doc,
      ttf: ttf,
      ttfBold: ttfBold,
      info: info,
      fromDate: fromDate?.formatDateToString,
      toDate: toDate?.formatDateToString,
      receiptData: receiptData!,
      isSales: isSales);

  await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) => Stack(
            children: [
              SizedBox(
                height: 600,
                // width: 300,
                child: PdfPreview(
                  previewPageMargin: const EdgeInsets.all(0),
                  dynamicLayout: false,
                  allowPrinting: true,
                  canChangeOrientation: false,
                  canChangePageFormat: false,
                  canDebug: false,
                  initialPageFormat: PdfPageFormat.a4,
                  build: (format) => doc.save(),
                  useActions: true,
                ),
              ),

              //close circle avatar
              Positioned(
                top: 0,
                right: Localizations.localeOf(context).languageCode == 'en'
                    ? 0
                    : null,
                left: Localizations.localeOf(context).languageCode == 'en'
                    ? null
                    : 0,
                child: IconButton(
                  icon: const CircleAvatar(
                      radius: 18,
                      backgroundColor: ColorManager.lightFieldColor,
                      child: Icon(Icons.close)),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ],
          ));
}

pw.Widget getList(
    BuildContext context, List<InvoiceModel>? receiptData, ttf, bool isSales) {
  pw.Widget cell(String text, bool header) => pw.Text(
        text,
        maxLines: 1,
        style: pw.TextStyle(
            fontSize: header ? 3.5 * PdfPageFormat.mm : 2.9 * PdfPageFormat.mm,
            fontWeight: header ? pw.FontWeight.bold : pw.FontWeight.normal,
            font: ttf),
        textDirection: pw.TextDirection.rtl,
      );

  final _invoiceWidget = pw.ListView(children: [
    pw.Table(columnWidths: {
      0: const pw.FlexColumnWidth(0.41 * PdfPageFormat.mm),
      1: const pw.FlexColumnWidth(0.3 * PdfPageFormat.mm),
      2: const pw.FlexColumnWidth(0.27 * PdfPageFormat.mm),
      3: const pw.FlexColumnWidth(0.25 * PdfPageFormat.mm),
      4: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
    }, children: [
      pw.TableRow(children: [
        cell("الاجمالى", true),
        cell("الإجمالي قبل الضريبة", true),
        pw.Center(child: cell(isSales ? ("اسم العميل") : ("اسم المورد"), true)),
        cell("التاريخ والوقت", true),
      ]),
    ]),
    pw.Divider(
        thickness: 0.5 * PdfPageFormat.mm, height: 0.5 * PdfPageFormat.mm),
    pw.Table(
        columnWidths: {
          0: const pw.FlexColumnWidth(0.41 * PdfPageFormat.mm),
          1: const pw.FlexColumnWidth(0.3 * PdfPageFormat.mm),
          2: const pw.FlexColumnWidth(0.27 * PdfPageFormat.mm),
          3: const pw.FlexColumnWidth(0.25 * PdfPageFormat.mm),
          4: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
        },
        children: receiptData!
            .map((invoice) => pw.TableRow(children: [
                  cell(
                      '${invoice.totalPrice!.roundedPrecisionToString(5)}${context.currency}',
                      false),
                  cell(
                      '${invoice.totalWithoutTax!.roundedPrecisionToString(5)}${context.currency}',
                      false),
                  pw.Center(child: cell(invoice.customerName!, false)),
                  cell(
                      DateFormat("dd-MM-yyyy hh:mm:ss")
                          .format(DateTime.parse(invoice.createdAt!)),
                      false),
                ]))
            .toList())
  ]);
  return _invoiceWidget;
}

addPdfPage(BuildContext appContext,
    {doc,
    ttf,
    ttfBold,
    CompanyInfoModel? info,
    required List<InvoiceModel>? receiptData,
    String? fromDate,
    String? toDate,
    required bool isSales}) async {
  doc.addPage(pw.Page(
      pageTheme: pw.PageTheme(
          pageFormat: PdfPageFormat.a4,
          theme: pw.ThemeData(bulletStyle: pw.TextStyle(font: ttf))),
      // pageFormat: PdfPageFormat.roll80,
      build: (pw.Context context) {
        return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            mainAxisSize: pw.MainAxisSize.max,
            children: [
              //? Company Info ------------------------------
              pw.Text(info?.companyName.toString() ?? '',
                  style: pw.TextStyle(
                      fontSize: 2.5 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttfBold),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.right),
              pw.SizedBox(height: 5),
              pw.Text('VAT # ${info?.vatNumber ?? ''}',
                  style: pw.TextStyle(
                      fontSize: 2.8 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttfBold),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.left),

              pw.SizedBox(height: 10),
              pw.Divider(height: 5),
              pw.SizedBox(height: 10),
              pw.Center(
                  child: pw.Column(children: [
                pw.Text(
                  ' تقرير  ${isSales ? 'مبيعات' : 'مشتريات'} ',
                  style: pw.TextStyle(
                      fontSize: 3 * PdfPageFormat.mm,
                      fontWeight: pw.FontWeight.bold,
                      font: ttfBold),
                  textDirection: pw.TextDirection.rtl,
                ),
              ])),
              pw.SizedBox(height: 7),
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          //! Total of invoices price with tax & without tax
                          pw.Column(
                              crossAxisAlignment: pw.CrossAxisAlignment.end,
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              children: [
                                pw.Text(
                                  'الإجمالي قبل الضريبة: ${receiptData!.map((e) => e.totalWithoutTax?.toDouble()).fold(0.0, (value, element) => num.parse(value.toString() ?? '0') + element!).roundedPrecisionToString(5)}',
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                  textDirection: pw.TextDirection.rtl,
                                ),
                                pw.SizedBox(height: 10),
                                pw.Text(
                                  'الإجمالي بالضريبة: ${receiptData.map((e) => e.totalPrice?.toDouble()).fold(0.0, (value, element) => num.parse(value.toString() ?? '0') + element!).roundedPrecisionToString(5)}',
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                  textDirection: pw.TextDirection.rtl,
                                ),
                              ]),

                          pw.Column(
                            children: [
                              if (fromDate != 'null')
                                pw.Row(
                                    mainAxisAlignment: pw.MainAxisAlignment.end,
                                    children: [
                                      pw.Text(
                                        fromDate!,
                                        style: pw.TextStyle(
                                            fontSize: 3.5 * PdfPageFormat.mm,
                                            fontWeight: pw.FontWeight.bold,
                                            font: ttf),
                                        textDirection: pw.TextDirection.rtl,
                                      ),
                                      pw.SizedBox(width: 5),
                                      pw.Text(
                                        'من: ',
                                        style: pw.TextStyle(
                                            fontSize: 3.5 * PdfPageFormat.mm,
                                            fontWeight: pw.FontWeight.bold,
                                            font: ttf),
                                        textDirection: pw.TextDirection.rtl,
                                      ),
                                    ]),
                              if (toDate != 'null')
                                pw.Row(
                                    mainAxisAlignment: pw.MainAxisAlignment.end,
                                    children: [
                                      pw.Text(
                                        toDate!,
                                        style: pw.TextStyle(
                                            fontSize: 3.5 * PdfPageFormat.mm,
                                            fontWeight: pw.FontWeight.bold,
                                            font: ttf),
                                        textDirection: pw.TextDirection.rtl,
                                      ),
                                      pw.SizedBox(width: 5),
                                      pw.Text(
                                        'الى: ',
                                        style: pw.TextStyle(
                                            fontSize: 3.5 * PdfPageFormat.mm,
                                            fontWeight: pw.FontWeight.bold,
                                            font: ttf),
                                        textDirection: pw.TextDirection.rtl,
                                      ),
                                    ]),
                            ],
                          ),
                        ]),
                    pw.SizedBox(height: 10),
                    if (receiptData.length < 35) ...[
                      getList(appContext, receiptData, ttf, isSales),
                    ] else ...[
                      getList(appContext, receiptData.take(35).toList(), ttf,
                          isSales),
                    ],
                  ]),
            ]);
      }));

  for (int i = 0; i < receiptData!.length ~/ 45; i++) {
    log('i = $i');
    log('receiptData.length = ${receiptData.length}');
    log('receiptData.length ~/ 45 = ${receiptData.length ~/ 45}');

    var data = receiptData.skip(i * 45).take(45).toList();
    log('data = ${data.length}');

    if (data.isNotEmpty) {
      log('receiptData.length - i * 45 = ${receiptData.length - i * 45}');
      addNewPage(appContext,
          receiptData: receiptData,
          from: data.length + i * 45,
          to: receiptData.length - i * 45,
          ttf: ttf,
          isSales: isSales,
          doc: doc);
    }
  }
}

void addNewPage(BuildContext context,
    {required List<InvoiceModel>? receiptData,
    required int from,
    required int to,
    required ttf,
    required isSales,
    required doc}) {
  print('add new page ${receiptData!.length}');
  print('from $from to $to');
  if (receiptData.length > from && to < receiptData.length) {
    List<InvoiceModel> temp = receiptData.skip(from).take(45).toList();
    doc.addPage(pw.Page(build: (pw.Context _) {
      return getList(context, temp, ttf, isSales);
    }));
  } else {
    List<InvoiceModel> remaining = receiptData.skip(from).toList();
    List<InvoiceModel> temp = receiptData
        .skip(from)
        .take(receiptData.length - remaining.length)
        .toList();

    print('remaining: ${remaining.length}');
    print('temp: ${temp.length}');

    doc.addPage(pw.Page(build: (pw.Context _) {
      return getList(context, receiptData.skip(from).take(temp.length).toList(),
          ttf, isSales);
    }));
    return;
  }
}
