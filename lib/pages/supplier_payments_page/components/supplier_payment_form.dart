import 'package:flutter/material.dart';
import 'package:mandob/models/supplier_model.dart';
import 'package:mandob/models/supplier_payment_model.dart';
import 'package:mandob/providers/supplier_payments_provider.dart';
import 'package:mandob/providers/suppliers_provider.dart';
import 'package:persian_number_utility/persian_number_utility.dart';
import 'package:provider/provider.dart';
import 'package:mandob/core/extensions/context_extensions.dart';

class SupplierPaymentsForm extends StatefulWidget {
  const SupplierPaymentsForm({super.key});

  @override
  _SupplierPaymentsFormState createState() => _SupplierPaymentsFormState();
}

class _SupplierPaymentsFormState extends State<SupplierPaymentsForm> {
  String? payValue;
  final GlobalKey<FormState> _formKey = GlobalKey();

  Future _submit() async {
    await Provider.of<SupplierPaymentsProvider>(context, listen: false)
        .addPayment(SupplierPaymentModel.fromJson({
      "createdAt": DateTime.now().toIso8601String(),
      "supplierId": selectedSup!.id,
      "supplierName": selectedSup!.name,
      "value": double.parse(payValue!)
    }))
        .then((value) => Navigator.pop(context));
    await Provider.of<SupplierPaymentsProvider>(context, listen: false)
        .fetchPayments();
  }

  SupplierModel? selectedSup;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          child: ListView(
            shrinkWrap: true,
            children: [
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Consumer<SuppliersProvider>(
                      builder: (context, _suppliers, _) {
                        return DropdownButton<SupplierModel>(
                            onChanged: (val) {
                              setState(() {
                                selectedSup = val;
                              });
                            },
                            hint: Text(context.isEng ? 'Select Supplier' : 'اختر مورد'),
                            icon: const Icon(Icons.keyboard_arrow_down_outlined),
                            value: selectedSup,
                            items: _suppliers.suppliers
                                .map((sup) => DropdownMenuItem<SupplierModel>(
                                value: sup, child: Text(sup.name!)))
                                .toList());
                      }),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  const SizedBox(width: 3),
                  Text(context.isEng ? 'Paid Amount' : 'المبلغ المدفوع'),
                  Flexible(
                    flex: 2,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8.0, vertical: 18.0),
                      child: SizedBox(
                        height: 60,
                        child: TextFormField(
                          initialValue: payValue,
                          textAlign: TextAlign.center,
                          validator: (value) {
                            if (value == null || value.isEmpty)
                              return context.isEng ? 'Enter amount' : 'ادخل المبلغ';
                            if (double.tryParse(payValue!.toEnglishDigit()) ==
                                null) return context.isEng ? 'Enter valid amount' : 'ادخل المبلغ الصحيح';
                          },
                          onChanged: (value) => setState(() {
                            payValue = value;
                          }),
                          decoration: InputDecoration(
                              fillColor: Colors.grey.shade300,
                              hintText: context.isEng ? '00.0' : '٠٠.٠',
                              filled: true),
                        ),
                      ),
                    ),
                  )
                ],
              ),
              const SizedBox(
                height: 12,
              ),
              ElevatedButton(
                  onPressed: () {
                    if (!_formKey.currentState!.validate()) return;
                    if (selectedSup == null) return;

                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        content: Text(context.isEng ? 'Please confirm payment details before proceeding' : 'من فضلك تاكد من بيانات السداد قبل الموافقه'),
                        actions: [
                          TextButton(
                              onPressed: () {
                                _submit();
                                Navigator.pop(context);
                              },
                              child: Text(context.isEng ? 'Confirm' : 'تم')),
                          TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: Text(context.isEng ? 'Back' : 'عوده'))
                        ],
                      ),
                    );
                  },
                  child: Text(context.isEng ? 'Pay' : 'سداد'))
            ],
          ),
        ),
      ),
    );
  }
}