import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/supplier_model.dart';
import 'package:mandob/models/supplier_payment_model.dart';
import 'package:mandob/pages/supplier_payments_page/components/supplier_payment_form.dart';
import 'package:mandob/providers/supplier_payments_provider.dart';
import 'package:mandob/theme.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:mandob/widgets/date_widget.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../core/shared/drop_down_fields/suppliers_drop_down.dart';
import '../../utils/app_bar.dart';
import '../../utils/color_manager.dart';

class SupplierPaymentsPage extends StatefulWidget {
  @override
  _SupplierPaymentsPageState createState() => _SupplierPaymentsPageState();
}

class _SupplierPaymentsPageState extends State<SupplierPaymentsPage> {
  ValueNotifier<DateTime> sDate = ValueNotifier<DateTime>(
      DateTime.now().subtract(const Duration(days: 30)));
  ValueNotifier<DateTime> eDate = ValueNotifier<DateTime>(DateTime.now());
  Future? _fetchData;
  Future? fetchSuppliers;

  @override
  void initState() {
    _fetchData = fetchPayments();
    super.initState();
  }

  Future fetchPayments() async {
    await Provider.of<SupplierPaymentsProvider>(context, listen: false)
        .fetchPayments(
      startDate: sDate.value,
      endDate: eDate.value,
      supplierId: supFilter?.id,
    );
  }

  SupplierModel? supFilter;

  @override
  Widget build(BuildContext context) {
    List<SupplierPaymentModel> _payments =
        Provider.of<SupplierPaymentsProvider>(context).payments ??
            <SupplierPaymentModel>[];

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Column(
        children: [
          appBarWidget(
            context,
            title: context.isEng ? 'Supplier Payments' : 'سداد الموردين',
            subtitle: context.isEng ? 'Add Supplier Payment +' : 'سداد مورد +',
            onTap: () => showDialog(
              context: context,
              builder: (context) => const SupplierPaymentsForm(),
            ),
          ),
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 10.0, vertical: 18.0),
            child: Column(
              children: [
                SuppliersDropDownButton(
                  width: MediaQuery.of(context).size.width,
                  supplier: supFilter,
                  onChanged: (val) async {
                    setState(() {
                      supFilter = val;
                    });

                    await fetchPayments();

                    setState(() {});
                  },
                ),
                AppGaps.gap12,
                UiWidgets.datePicker(
                  context,
                  () async {
                    await fetchPayments();
                    setState(() {});
                  },
                  startDate: sDate,
                  endDate: eDate,
                ),
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15.0),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Table(columnWidths: const {
                      0: FlexColumnWidth(0.7),
                      1: FlexColumnWidth(0.7),
                      2: FlexColumnWidth(0.7),
                    }, children: [
                      TableRow(
                        children: [
                          Text(
                            context.isEng ? 'Date' : 'التاريخ',
                            style: Them.tableHeader,
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            context.isEng ? 'Supplier Name' : 'اسم المورد',
                            style: Them.tableHeader,
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            context.isEng ? 'Amount' : 'المبلغ',
                            style: Them.tableHeader,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ]),
                  ),
                  const Divider(
                    color: ColorManager.primaryColor,
                    thickness: 2,
                    height: 0,
                  ),
                  Expanded(
                    child: FutureBuilder<void>(
                        future: _fetchData,
                        builder: (context, AsyncSnapshot<void> snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return const Center(
                              child: SizedBox(
                                  width: 70,
                                  height: 70,
                                  child: LoadingWidget()),
                            );
                          }
                          if (snapshot.connectionState ==
                              ConnectionState.done) {
                            return _payments.isEmpty
                                ? Center(
                                    child: Text(context.isEng
                                        ? 'No payments made'
                                        : 'لم تقم باى سدادات'),
                                  )
                                : SingleChildScrollView(
                                    child: Table(
                                        defaultVerticalAlignment:
                                            TableCellVerticalAlignment.middle,
                                        columnWidths: const {
                                          0: FlexColumnWidth(0.7),
                                          1: FlexColumnWidth(0.7),
                                          2: FlexColumnWidth(0.7),
                                        },
                                        children: _payments.reversed
                                            .map((state) => TableRow(children: [
                                                  Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        vertical: 20.0),
                                                    child: Text(
                                                      state.createdAt!
                                                          .toString()
                                                          .split(" ")
                                                          .first,
                                                      style: Them.tableCell,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      textAlign:
                                                          TextAlign.center,
                                                    ),
                                                  ),
                                                  Text(
                                                    state.supplierName!,
                                                    style: Them.tableCell
                                                        .copyWith(
                                                            fontWeight:
                                                                FontWeight
                                                                    .normal),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                  Text(
                                                    state.value.toString(),
                                                    style: Them.tableCell,
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ]))
                                            .toList()),
                                  );
                          }

                          return Center(
                            child: Text(
                                context.isEng ? 'Empty Data' : 'بيانات فارغة'),
                          );
                        }),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
