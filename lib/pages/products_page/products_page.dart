import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:provider/provider.dart';

import 'add_edit_product/add_product_dialog.dart';
import 'components/products_list_view.dart';

class ProductsPage extends StatefulWidget {
  const ProductsPage({Key? key}) : super(key: key);

  @override
  State<ProductsPage> createState() => _ProductsPageState();
}

class _ProductsPageState extends State<ProductsPage> {
  @override
  void initState() {
    // getIsAdmin();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ProductsProvider>(builder: (context, prod, child) {
      var user = Provider.of<UserProvider>(context, listen: false);

      return Scaffold(
        backgroundColor: Colors.white,
        floatingActionButton: user.activeUser!.isAdmin!
            ? FloatingActionButton.extended(
                onPressed: () => Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const AddProductDialog(),
                )),
                label: Text(context.isEng ? 'Add Product' : 'إضافة منتج'),
                icon: const Icon(Icons.add),
              )
            : Container(),
        body: const ProductsListView(),
      );
    });
  }
}
