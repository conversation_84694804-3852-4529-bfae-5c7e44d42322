import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/utils/show_bar/messages.dart';
import 'package:mandob/utils/submit_button.dart';
import 'package:mandob/utils/text_field.dart';
import 'package:persian_number_utility/persian_number_utility.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../utils/loading_widget.dart';

class EditPriceDialog extends StatefulWidget {
  final String productId;
  final RepositoryModel repository;
  final double price;

  const EditPriceDialog(
      {super.key,
      required this.productId,
      required this.price,
      required this.repository});

  @override
  State<StatefulWidget> createState() => _EditPriceDialog();
}

class _EditPriceDialog extends State<EditPriceDialog> {
  @override
  void initState() {
    priceCtrl.text = widget.price.toString();

    super.initState();
  }

  bool loading = false;

  var priceCtrl = TextEditingController();

  var formKey = GlobalKey<FormState>();

  var priceFocus = FocusNode();
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;
    return WillPopScope(
      onWillPop: () async {
        if (MediaQuery.of(context).viewInsets.bottom != 0) {
          priceFocus.unfocus();
        } else {
          Navigator.pop(context);
        }
        return true;
      },
      child: Form(
        key: formKey,
        child: Stack(
          children: <Widget>[
            Center(
              child: Material(
                color: Colors.transparent,
                child: Container(
                    width: 300,
                    padding: const EdgeInsets.all(20),
                    decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15.0))),
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                        //price field
                        Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                isEng
                                    ? 'Edit Product Price'
                                    : 'تعديل سعر المنتج',
                                style: const TextStyle(
                                  fontSize: 18,
                                ),
                              ),
                              AppGaps.gap16,
                              TextFieldWidget(
                                label: isEng ? 'Product Price' : 'سعر المنتج',
                                hint: '0',
                                focusNode: priceFocus,
                                textInputType: TextInputType.number,
                                controller: priceCtrl,
                                // keyboardType: TextInputType.number,
                                // decoration: InputDecoration(
                                // ),
                                validator: (value) {
                                  if (double.tryParse(value!.toEnglishDigit())!
                                      .isNaN)
                                    return isEng
                                        ? 'Price must be a number'
                                        : 'السعر يتكون من أرقام فقط';
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 25,
                        ),
                        SubmitButton(
                          onPressed: () async {
                            try {
                              setState(() {
                                loading = true;
                              });
                              final repo = widget.repository;

                              repo.products![widget.productId] =
                                  double.parse(priceCtrl.text.toEnglishDigit());

                              await Provider.of<RepositoryProvider>(context,
                                      listen: false)
                                  .editRepository(repo.id!, repo);

                              if (mounted) {
                                setState(() {
                                  loading = false;
                                });
                                showDoneMessageAndClose(context, true);
                              }
                            } catch (e) {
                              loading = false;
                              ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(content: Text(e.toString())));
                            }
                          },
                          label: isEng ? 'Edit' : 'تعديل',
                        ),
                      ],
                    )),
              ),
            ),
            loading
                ? const Center(
                    child: LoadingWidget(),
                  )
                : Container()
          ],
        ),
      ),
    );
  }
}
