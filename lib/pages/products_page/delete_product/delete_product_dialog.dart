import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:provider/provider.dart';

class DeleteProductDialog extends StatefulWidget {
  final String? prodId;

  const DeleteProductDialog({Key? key, required this.prodId}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _DeleteProductDialog();
}

class _DeleteProductDialog extends State<DeleteProductDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  @override
  void initState() {
    Provider.of<RepositoryProvider>(context, listen: false).fetchRepositories();

    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  bool loading = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<RepositoryProvider>(builder: (context, repo, child) {
      if (repo.repositories.isEmpty) {
        return const Center(
          child: LoadingWidget(),
        );
      }
      return Stack(
        children: <Widget>[
          Center(
            child: Material(
              color: Colors.transparent,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: Container(
                    width: 250,
                    padding: const EdgeInsets.all(15),
                    decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15.0))),
                    child: SingleChildScrollView(
                      child: Wrap(
                        children: <Widget>[
                          Padding(
                            padding: const EdgeInsets.only(right: 4.0, left: 4),
                            child: Center(
                              child: Text(
                                context.isEng
                                    ? 'Are you sure you want to delete this product ?'
                                    : 'هل تريد حذف هذا المنتج !',
                                style: const TextStyle(
                                    color: Colors.black87,
                                    fontSize: 18,
                                    height: 1.5),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                                top: 25, bottom: 10, right: 10),
                            child: Row(
                              textDirection: TextDirection.rtl,
                              children: <Widget>[
                                const SizedBox(width: 10),
                                GestureDetector(
                                  onTap: () => Navigator.pop(context),
                                  child: Container(
                                    height: 35,
                                    width: 80,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(7),
                                        color: Colors.grey),
                                    child: Text(
                                      context.isEng ? 'Cancel' : 'إلغاء',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                GestureDetector(
                                  onTap: () async {
                                    setState(() {
                                      loading = true;
                                    });

                                    repo.repositories.forEach((element) async {
                                      if (element.products!
                                          .containsKey(widget.prodId)) {
                                        Map<String, dynamic>? oldProducts =
                                            element.products;

                                        oldProducts!.remove(widget.prodId);

                                        await Provider.of<RepositoryProvider>(
                                                context,
                                                listen: false)
                                            .editRepository(
                                                element.id.toString(),
                                                RepositoryModel.fromJson({
                                                  "name": element.name!,
                                                  "location": element.location!,
                                                  "createdAt":
                                                      element.createdAt!,
                                                  "products":
                                                      jsonEncode(oldProducts),
                                                }));
                                      }
                                    });

                                    await Provider.of<QuantitiesProvider>(
                                            context,
                                            listen: false)
                                        .deleteQuantity(
                                            widget.prodId.toString());

                                    await Provider.of<ProductsProvider>(context,
                                            listen: false)
                                        .deleteProduct(
                                            widget.prodId.toString());

                                    setState(() {
                                      loading = false;
                                    });

                                    Navigator.pop(context);

                                    showBar(
                                        context,
                                        context.isEng
                                            ? 'Product deleted !'
                                            : 'تم حذف المنتج !');
                                  },
                                  child: Container(
                                    height: 35,
                                    width: 80,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(7),
                                        color: Colors.red),
                                    child: Text(
                                      context.isEng ? 'Delete' : 'حذف',
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    )),
              ),
            ),
          ),
          loading
              ? const Center(
                  child: LoadingWidget(),
                )
              : Container()
        ],
      );
    });
  }
}
