import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:mandob/appwrite_db/appwrite.dart';
import 'package:mandob/appwrite_db/db_consts.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/product_model.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/pages/products_page/edit_product_price/edit_price_dialog.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:provider/provider.dart';

import '../add_edit_product/add_product_dialog.dart';
import '../delete_product/delete_product_dialog.dart';

class ProductWidget extends StatelessWidget {
  const ProductWidget({
    super.key,
    required this.productData,
    required this.prodQuants,
    required this.index,
    required this.user,
    required this.myRepo,
    required this.myProdIds,
  });

  final ProductModel productData;
  final Map<String, dynamic> prodQuants;
  final int index;
  final UserModel user;
  final RepositoryModel myRepo;
  final myProdIds;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: Stack(
        alignment: Alignment.topLeft,
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: Colors.grey.withOpacity(0.3)),
              // boxShadow: [
              //   BoxShadow(
              //     color: Colors.grey.withOpacity(0.3),
              //     spreadRadius: 2,
              //     blurRadius: 12,
              //     offset: const Offset(0, 2), // changes position of shadow
              //   ),
              // ],
            ),
            child: ListTile(
              isThreeLine: true,
              minVerticalPadding: 5,
              leading: _ProductImage(
                productData: productData,
              ),
              trailing: _ProductTrailing(
                  productData: productData, myProdIds: myProdIds, user: user),
              title: _ProductTitle(
                productData: productData,
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!user.isAdmin!) ...[
                    Divider(
                      color: Colors.blueGrey.shade100,
                    ),
                    Consumer<QuantitiesProvider>(
                        builder: (context, quantities, child) {
                      return Text(context.isEng
                          ? 'Quantity: ${(prodQuants[myRepo.id] ?? '0').toString()} ${productData.type ?? ''}'
                          : "الكمية: ${(prodQuants[myRepo.id] ?? '0').toString()} ${productData.type ?? ''}");
                    }),
                  ],
                ],
              ),
            ),
          ),
          _EditProductWidget(
              user: user,
              productData: productData,
              myRepo: myRepo,
              myProdIds: myProdIds),
          user.isAdmin!
              ? _DeleteProductWidget(
                  productData: productData,
                )
              : Container(),
        ],
      ),
    );
  }
}

class _EditProductWidget extends StatelessWidget {
  const _EditProductWidget({
    required this.user,
    required this.productData,
    required this.myRepo,
    required this.myProdIds,
  });

  final UserModel user;
  final ProductModel productData;
  final RepositoryModel myRepo;
  final Map<String, dynamic> myProdIds;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 10,
      left: context.isEng ? null : 10,
      right: context.isEng ? 10 : null,
      child: GestureDetector(
        onTap: () {
          if (user.isAdmin!) {
            showDialog(
                context: context,
                builder: (_) => AddProductDialog(
                      product: productData,
                    ));
          } else {
            showDialog(
                context: context,
                builder: (_) => EditPriceDialog(
                      price: myProdIds[productData.id],
                      productId: productData.id!,
                      repository: myRepo,
                    ));
          }
        },
        child: const CircleAvatar(
          maxRadius: 15,
          backgroundColor: ColorManager.primaryColor,
          child: Icon(
            Icons.edit,
            size: 20,
          ),
        ),
      ),
    );
  }
}

class _DeleteProductWidget extends StatelessWidget {
  final ProductModel productData;

  const _DeleteProductWidget({
    required this.productData,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 10,
      left: 10,
      child: GestureDetector(
        onTap: () => showDialog(
            context: context,
            builder: (_) => DeleteProductDialog(
                  prodId: productData.id!,
                )),
        child: const CircleAvatar(
          maxRadius: 15,
          backgroundColor: Colors.redAccent,
          child: Icon(
            Icons.delete,
            size: 20,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}

Future<Uint8List> _getProductImage(String? imageId) async {
  final unitFile = await AppwriteDB.getFile(fileId: imageId ?? '');

  return unitFile;
}

class _ProductImage extends StatelessWidget {
  final ProductModel productData;

  const _ProductImage({required this.productData});

  @override
  Widget build(BuildContext context) {
    final isThamraDB = AppwriteDB.databaseId == DbConsts.thamraDB;

    String placeHolderImage = isThamraDB
        ? 'https://sites.google.com/a/eksala.tzafonet.org.il/zauton/_/rsrc/1472864934146/home/<USER>'
        : 'https://cdn.britannica.com/94/151894-050-F72A5317/Brown-eggs.jpg';

    return Container(
      height: 60,
      width: 55,
      decoration: BoxDecoration(
        color: Colors.grey[500],
        shape: BoxShape.circle,
      ),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(5),
          child: FutureBuilder(
            future: _getProductImage(productData.image),
            builder: (context, snapshot) {
              if (snapshot.hasData &&
                  productData.image != null &&
                  productData.image != '') {
                final Uint8List image = snapshot.data as Uint8List;
                return Image.memory(
                  image,
                  fit: BoxFit.cover,
                );
              } else {
                return Image.network(
                  placeHolderImage,
                  fit: BoxFit.cover,
                );
              }
            },
          )),
    );
  }
}

class _ProductTitle extends StatelessWidget {
  final ProductModel productData;

  const _ProductTitle({required this.productData});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(productData.name?.toString() ?? ''),
    );
  }
}

class _ProductTrailing extends StatelessWidget {
  final ProductModel productData;
  final myProdIds;
  final UserModel user;

  const _ProductTrailing(
      {required this.productData, required this.myProdIds, required this.user});

  @override
  Widget build(BuildContext context) {
    final user = Provider.of<UserProvider>(context, listen: false).activeUser;

    return Padding(
      padding: EdgeInsets.only(
        left: context.isEng ? 0 : 30.0,
        right: context.isEng ? 30 : 0,
        top: 5,
      ),
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: '${context.currency} ',
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
            TextSpan(
              text: user!.isAdmin!
                  ? '${productData.price}'
                  : '${myProdIds[productData.id]}',
              style: const TextStyle(
                fontSize: 16,
                color: ColorManager.primaryColor,
              ),
            )
          ],
        ),
      ),
    );
  }
}
