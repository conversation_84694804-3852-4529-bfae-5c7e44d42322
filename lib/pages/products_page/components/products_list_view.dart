import 'package:flutter/material.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:provider/provider.dart';

import '../../../models/product_model.dart';
import '../../../models/quantity_model.dart';
import '../../../providers/quantities_provider.dart';
import '../../../utils/loading_widget.dart';
import 'product_widget.dart';

class ProductsListView extends StatefulWidget {
  const ProductsListView({
    super.key,
  });

  @override
  State<ProductsListView> createState() => _ProductsListViewState();
}

class _ProductsListViewState extends State<ProductsListView> {
  Map<String, dynamic> myProdIds = {};

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        _getProductsData();
      }
    });
  }

  bool loading = false;

  int? paginationLimit;

  Future<void> _getProductsData() async {
    final productsProvider = Provider.of<ProductsProvider>(
      context,
      listen: false,
    );

    setState(() {
      paginationLimit = 25;
      loading = true;
    });

    await productsProvider.fetchProducts(
      paginationLimit: paginationLimit,
    );

    setState(() {
      loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final user = Provider.of<UserProvider>(context, listen: false).activeUser;
    RepositoryModel myRepo = RepositoryModel();

    return Consumer<RepositoryProvider>(builder: (context, repos, child) {
      return FutureBuilder<void>(
          future: repos.fetchRepositories(),
          builder: (context, AsyncSnapshot<void> snapshot) {
            if (repos.repositories.isEmpty) {
              return const Center(
                child: LoadingWidget(),
              );
            }
            return Consumer<ProductsProvider>(
                builder: (context, products, child) {
              if (products.products == null) {
                return const Center(
                  child: Text('لا يوجد منتجات !'),
                );
              }
              return FutureBuilder<void>(
                  future: products.fetchProducts(
                    paginationLimit: paginationLimit,
                  ),
                  builder: (context, AsyncSnapshot<void> snapshot) {
                    List<ProductModel> myProducts = products.products!;

                    if (!user!.isAdmin!) {
                      myRepo = repos.repositories.firstWhere(
                          (element) => element.id == user.storeId,
                          orElse: () => RepositoryModel());
                      if (myRepo.products != null) {
                        myProdIds = myRepo.products!;

                        myProducts = products.products!
                            .where(
                              (element) => myProdIds.containsKey(element.id),
                            )
                            .toList();
                      }
                    }
                    return myRepo.products == null && !user.isAdmin!
                        ? const Center(
                            child: Text("انت غير منتمى لمخزن او مخزنك فارغ"),
                          )
                        : Consumer<QuantitiesProvider>(
                            builder: (context, quantities, child) {
                            return FutureBuilder(
                                future: quantities.fetchQuantities(),
                                builder: (context, snapshot) {
                                  return ListView.builder(
                                    shrinkWrap: true,
                                    controller: _scrollController,
                                    padding: const EdgeInsets.all(5),
                                    itemCount: myProducts.length,
                                    itemBuilder: (context, index) {
                                      ProductModel? productData =
                                          myProducts[index];

                                      final prodQuants = quantities.quantities
                                          .firstWhere(
                                            (element) =>
                                                element.productId ==
                                                productData.id,
                                            orElse: () => QuantityModel(),
                                          )
                                          .quantities;

                                      return ProductWidget(
                                          productData: productData,
                                          prodQuants: prodQuants!,
                                          index: index,
                                          user: user,
                                          myRepo: myRepo,
                                          myProdIds: myProdIds);
                                    },
                                  );
                                });
                          });
                  });
            });
          });
    });
  }
}
