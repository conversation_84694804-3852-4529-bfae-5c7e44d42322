import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mandob/appwrite_db/appwrite.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/product_model.dart';
import 'package:mandob/models/quantity_model.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:persian_number_utility/persian_number_utility.dart';
import 'package:provider/provider.dart';

import '../../../utils/show_bar/messages.dart';
import '../../../utils/xid.dart';

class AddProductDialog extends StatefulWidget {
  final ProductModel? product;

  const AddProductDialog({Key? key, this.product}) : super(key: key);
  @override
  State<StatefulWidget> createState() => _AddProductDialog();
}

class _AddProductDialog extends State<AddProductDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  @override
  void initState() {
    if (widget.product != null) {
      nameCtrl.text = widget.product?.name ?? '';
      priceCtrl.text = widget.product?.price.toString() ?? '';
      bulkPriceCtrl.text = widget.product?.bulkPrice.toString() ?? '';
      type = widget.product!.type.toString();
      imageId = widget.product!.image ?? "";
    }
    priceFocus.addListener(() {
      formKey.currentState!.validate();
    });
    Provider.of<RepositoryProvider>(context, listen: false).fetchRepositories();
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  bool loading = false;

  var nameCtrl = TextEditingController();
  var priceCtrl = TextEditingController();
  var bulkPriceCtrl = TextEditingController();
  var productsQuantityCtrl = TextEditingController();

  String productsUnit = 'بالحبة';
  String repository = '';

  var formKey = GlobalKey<FormState>();

  var nameFocus = FocusNode();
  var priceFocus = FocusNode();
  var bulkPriceFocus = FocusNode();
  var productsQuantityFocus = FocusNode();

  String imageId = '';

  late File? imageFile = File("");
  late String? fileName;

  Future pickImage() async {
    final imagePicker = ImagePicker();
    var imagePicked = await imagePicker.pickImage(
        source: ImageSource.gallery, maxHeight: 200, maxWidth: 200);

    if (imagePicked != null) {
      setState(() {
        imageFile = File(imagePicked.path);
        fileName = (imageFile!.path);
      });
    } else {
      print('No image selected!');
    }
  }

  Future<String?> uploadFile(File? file) async {
    if (file == null) {
      debugPrint('no image selected !');
      return null;
    }
    final resData = await AppwriteDB.uploadFile(file: file);

    final String fileId = resData.$id;

    setState(() {
      imageId = fileId;
    });
    return fileId;
  }

  Future<void> addProd() async {
    try {
      final String prodId = generateId();

      setState(() {
        loading = true;
      });

      if (!formKey.currentState!.validate() || _selectedStores.isEmpty) return;

      if (imageFile!.path.isNotEmpty) {
        await uploadFile(imageFile);
      }

      final prodPrice = double.tryParse(priceCtrl.text.toEnglishDigit());
      final prodBulkPrice =
          double.tryParse(bulkPriceCtrl.text.toEnglishDigit());

      final productModel = ProductModel.fromJson({
        'id': prodId,
        'name': nameCtrl.text,
        'type': type,
        'price': prodPrice,
        'bulkPrice': bulkPriceCtrl.text.isEmpty ? prodPrice : prodBulkPrice,
        'image': imageId,
      });

      await _addProductToDB(productModel);

      await _addQuantities(prodId);

      await _editRepositories(prodId: prodId, prodPrice: prodPrice!);

      setState(() {
        loading = false;
      });

      if (mounted) {
        showDoneMessageAndClose(context);
      }
    } catch (e) {
      setState(() {
        loading = false;
      });

      showErrorMessageAndClose(context);
    }
  }

  Future<void> _addProductToDB(ProductModel productModel) async {
    await Provider.of<ProductsProvider>(context, listen: false)
        .addProduct(productModel);
  }

  //! Set Product Quantity
  Future<void> _addQuantities(String prodId) async {
    final quant = QuantityModel.fromJson({
      "productId": prodId,
      "quantities": jsonEncode(
          _selectedStores.map((key, value) => MapEntry(key.id!, value)))
    });

    await Provider.of<QuantitiesProvider>(context, listen: false)
        .setQuantities(quant);
  }

  //! Edit Repository Product Price
  Future<void> _editRepositories(
      {required String prodId, required double prodPrice}) async {
    final List<RepositoryModel> repos = _selectedStores.keys.toList();

    for (final repo in repos) {
      final repoId = repo.id!;
      final Map<String, dynamic> products = repo.products ?? {};

      products.addAll({prodId: prodPrice});

      final modifiedRepo = RepositoryModel.fromJson({
        'id': repoId,
        "products": jsonEncode(products),
        "name": repo.name!,
        "location": repo.location!,
      });

      Provider.of<RepositoryProvider>(context, listen: false)
          .editRepository(repo.id!, modifiedRepo);
    }
  }

  Future editProduct() async {
    if (!formKey.currentState!.validate()) return;
    try {
      setState(() {
        loading = false;
      });
      if (imageFile!.path.isNotEmpty) {
        await uploadFile(imageFile);
      }
      final prodProv = Provider.of<ProductsProvider>(context, listen: false);

      final prodId = widget.product!.id;
      final prodPrice = double.tryParse(priceCtrl.text.toEnglishDigit());
      final prodBulkPrice =
          double.tryParse(bulkPriceCtrl.text.toEnglishDigit());

      final productModel = ProductModel.fromJson({
        'id': prodId,
        'name': nameCtrl.text,
        'type': type,
        'price': prodPrice,
        'bulkPrice': bulkPriceCtrl.text.isEmpty ? prodPrice : prodBulkPrice,
        'image': imageId,
      });

      await prodProv.editProduct(productModel);

      // await _addQuantities(prodId!);

      await _editRepositories(prodId: prodId!, prodPrice: prodPrice!);

      setState(() {
        loading = false;
      });

      if (mounted) {
        showDoneMessageAndClose(context);
      }
    } catch (e) {
      setState(() {
        loading = false;
      });

      showErrorMessageAndClose(context);
    }
  }

  void onSelect(value, bool add) {
    add
        ? _selectedStores.addEntries([MapEntry(value, 0)])
        : _selectedStores.remove(value);
  }

  String type = "بالحبة";
  Map<RepositoryModel, dynamic> _selectedStores = {};
  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;
    return WillPopScope(
      onWillPop: () async {
        if (MediaQuery.of(context).viewInsets.bottom != 0) {
          nameFocus.unfocus();
          priceFocus.unfocus();
          productsQuantityFocus.unfocus();
        } else
          Navigator.pop(context);
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          actions: [
            loading
                ? const LoadingWidget()
                : IconButton(
                    icon: const Icon(Icons.done_all),
                    onPressed: () async {
                      widget.product == null ? addProd() : editProduct();
                    })
          ],
        ),
        body: Form(
            key: formKey,
            child: ListView(
              shrinkWrap: true,
              children: [
                InkWell(
                  child: CircleAvatar(
                    radius: 70,
                    backgroundColor: Colors.grey[300],
                    child: Container(
                      height: 120,
                      width: 120,
                      decoration: BoxDecoration(
                        color: Colors.grey[500],
                        shape: BoxShape.circle,
                      ),
                      child: imageFile!.path == ''
                          ? widget.product != null
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(100),
                                  child: Image.network(
                                    widget.product!.image.toString(),
                                    fit: BoxFit.cover,
                                  ))
                              : const SizedBox()
                          : ClipRRect(
                              borderRadius: BorderRadius.circular(100),
                              child: Image.file(
                                imageFile!,
                                fit: BoxFit.cover,
                              )),
                    ),
                  ),
                  onTap: () {
                    pickImage();
                  },
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8.0, horizontal: 18),
                  child: TextFormField(
                    focusNode: nameFocus,
                    controller: nameCtrl,
                    decoration: InputDecoration(
                      hintText: isEng ? 'Product Name' : 'اسم المنتج',
                    ),
                    validator: (String? value) {
                      if (value!.isEmpty)
                        return isEng
                            ? 'Product name is empty'
                            : 'اسم المنتج فارغ';
                      return null;
                    },
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8.0, horizontal: 18),
                  child: TextFormField(
                    focusNode: priceFocus,
                    keyboardType: TextInputType.number,
                    controller: priceCtrl,
                    decoration: InputDecoration(
                      hintText: isEng ? 'Price' : 'السعر',
                    ),
                    validator: (value) {
                      if (value!.isEmpty)
                        return isEng ? 'Price is empty' : 'السعر فارغ';
                      if (double.tryParse(value.toEnglishDigit()) == null) {
                        return isEng
                            ? 'Enter a valid price'
                            : 'ادخل السعر صحيح';
                      }
                    },
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8.0, horizontal: 18),
                  child: TextFormField(
                    focusNode: bulkPriceFocus,
                    keyboardType: TextInputType.number,
                    controller: bulkPriceCtrl,
                    decoration: InputDecoration(
                      hintText: isEng ? 'Bulk Price' : 'سعر الجملة',
                    ),
                    // validator: (value) {
                    //   if (value!.isEmpty) return "السعر فارغ";
                    //   if (double.tryParse(value.toEnglishDigit()) == null) {
                    //     return "ادخل السعر صحيح";
                    //   }
                    // },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Card(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      DropdownButton<String>(
                          value: type,
                          hint: Text(type),
                          underline: Container(),
                          items: <String>[
                            isEng ? 'Per Piece' : 'بالحبة',
                            isEng ? 'Carton' : 'كارتون',
                          ].map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(
                                value,
                                style: const TextStyle(color: Colors.black),
                              ),
                            );
                          }).toList(),
                          onChanged: (val) => setState(() {
                                type = val!;
                              })),
                      if (widget.product == null)
                        TextButton(
                            onPressed: () => showDialog(
                                  context: context,
                                  builder: (context) => Dialog(
                                      child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.stretch,
                                    children: [
                                      Consumer<RepositoryProvider>(
                                          builder: (context, stores, child) {
                                        return Flexible(
                                            fit: FlexFit.tight,
                                            child: stores.repositories.isEmpty
                                                ? const Center(
                                                    child: LoadingWidget(),
                                                  )
                                                : ListView(
                                                    children: stores
                                                        .repositories
                                                        .map((store) => Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        3.0,
                                                                    vertical:
                                                                        3.0),
                                                            child: StoresLI(
                                                              isSelected:
                                                                  _selectedStores
                                                                      .containsKey(
                                                                          store),
                                                              store: store,
                                                              onSelect:
                                                                  onSelect,
                                                            )))
                                                        .toList()));
                                      }),
                                      ElevatedButton(
                                          onPressed: () {
                                            Navigator.pop(context);
                                            setState(() {});
                                          },
                                          child: Text(isEng ? 'Done' : 'تم'))
                                    ],
                                  )),
                                ),
                            child: Text(
                                isEng ? 'Select Repositories' : 'اختر المخازن'))
                    ],
                  )),
                ),
                if (_selectedStores.isEmpty && widget.product == null)
                  Center(
                    child: Text(isEng
                        ? 'You must select repositories'
                        : 'يجب اختيار مخازن'),
                  ),
                ..._selectedStores.entries
                    .map((item) => Card(
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 15.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Flexible(
                                  flex: 4,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0, vertical: 15),
                                    child: Text(item.key.name!),
                                  ),
                                ),
                                Flexible(
                                  flex: 2,
                                  child: TextField(
                                    textAlign: TextAlign.center,
                                    // keyboardType: TextInputType.number,
                                    decoration: InputDecoration(
                                        hintText: "0",
                                        border: InputBorder.none,
                                        fillColor: Colors.grey.shade300,
                                        filled: true),
                                    onChanged: (newValue) {
                                      final val = newValue.toEnglishDigit();
                                      _selectedStores.update(item.key,
                                          (value) => value = int.parse(val));
                                    },
                                  ),
                                )
                              ],
                            ),
                          ),
                        ))
                    .toList(),
              ],
            )),
      ),
    );
  }
}

class StoresLI extends StatefulWidget {
  final RepositoryModel store;
  final Function onSelect;
  final bool isSelected;

  const StoresLI({
    Key? key,
    required this.store,
    required this.onSelect,
    required this.isSelected,
  }) : super(key: key);

  @override
  State<StoresLI> createState() => _StoresLIState();
}

class _StoresLIState extends State<StoresLI> {
  @override
  void initState() {
    checkValue = widget.isSelected;
    super.initState();
  }

  bool checkValue = false;

  @override
  Widget build(BuildContext context) {
    return CheckboxListTile(
      key: ValueKey(widget.store.id),
      tileColor: Colors.grey.shade300,
      value: checkValue,
      onChanged: (value) {
        setState(() {
          checkValue = value!;
        });
        widget.onSelect(widget.store, value);
      },
      title: Text(widget.store.name!),
    );
  }
}
