import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mandob/appwrite_db/db_consts.dart';

final _inputDeco = InputDecoration(
  fillColor: Colors.transparent,
  contentPadding: const EdgeInsets.fromLTRB(10.0, 15.0, 10.0, 10.0),
  border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(32.0),
      borderSide: const BorderSide(color: Color(0xFFF9CC15))),
  filled: true,
);

// Form elements *********************
Widget inputDatabaseField(String? databaseId, onChanged) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 10),
    child: TextFormField(
      initialValue: kDebugMode ? DbConsts.mandobDB : null,
      onChanged: onChanged,
      onSaved: (value) => databaseId = value,
      validator: (value) {
        if (value!.isEmpty) {
          return "اسم الشركة غير صحيح";
        } else {
          return null;
        }
      },
      style: const TextStyle(color: Colors.blueGrey),
      decoration: _inputDeco.copyWith(
        prefixIcon: const Padding(
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Icon(Icons.account_tree_outlined),
        ),
        labelText: "اسم الشركة",
      ),
    ),
  );
}

Widget inputEmail(String? email, onChanged) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 10),
    child: TextFormField(
      initialValue: kDebugMode ? DbConsts.testUser : null,
      onChanged: onChanged,
      onSaved: (value) => email = value,
      validator: (value) {
        if (value!.isEmpty) {
          return "اسم المستخدم غير صحيح";
        } else {
          return null;
        }
      },
      style: const TextStyle(color: Colors.blueGrey),
      decoration: _inputDeco.copyWith(
          prefixIcon: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
            child: Image.asset(
              "assets/images/user-1.png",
              color: const Color(0xFFF9CC15),
              width: 30,
              height: 30,
            ),
          ),
          labelText: "اسم المستخدم"),
    ),
  );
}

Widget passwordInput(String? password, onChanged) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 10),
    child: TextFormField(
      initialValue: kDebugMode ? DbConsts.testPass : null,
      onChanged: onChanged,
      onSaved: (value) => password = value,
      validator: (value) {
        if (value!.length <= 2) {
          return "كلمة المرور قصيرة جدا";
        } else {
          return null;
        }
      },
      style: const TextStyle(
        color: Colors.blueGrey,
      ),
      obscureText: true,
      decoration: _inputDeco.copyWith(
          prefixIcon: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
            child: Image.asset(
              "assets/images/padlock.png",
              color: const Color(0xFFF9CC15),
              width: 30,
              height: 30,
              fit: BoxFit.contain,
            ),
          ),
          labelText: "كلمة المرور"),
    ),
  );
}

Widget buttonLogin(BuildContext context,
    {required void Function()? onPressed}) {
  return Container(
    alignment: Alignment.bottomRight,
    width: MediaQuery.of(context).size.width,
    margin: const EdgeInsets.only(bottom: 10),
    decoration: const BoxDecoration(
      shape: BoxShape.circle,
      boxShadow: [
        BoxShadow(
          color: Color(0xFFF9CC15),
          blurRadius: 10.0,
          spreadRadius: 1.0,
          offset: Offset(
            5.0,
            5.0,
          ),
        ),
      ],
      color: Colors.white,
    ),
    child: Center(
        child: InkWell(
            onTap: onPressed,
            child: const Center(
              child: Icon(
                Icons.keyboard_arrow_left_rounded,
                size: 50,
              ),
            ))),
  );
}
