import 'package:flutter/material.dart';
import 'package:mandob/pages/old_login_page/components/login_logo_text.dart';

class TopSectionLogin extends StatelessWidget {
  const TopSectionLogin({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final dSize = MediaQuery.of(context).size;

    return Column(
      children: [
        SizedBox(
          height: dSize.height * 0.45,
          width: double.infinity,
          child: Stack(
            children: [
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Image.asset(
                  'assets/images/sky.png',
                  isAntiAlias: true,
                  fit: BoxFit.fitWidth,
                ),
              ),
              Positioned(
                  top: 20,
                  left: 0,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        const Text(
                          'Developed by OptimumEgy',
                          style: TextStyle(
                              fontSize: 13, fontWeight: FontWeight.w900),
                        ),
                        Image.asset(
                          'assets/images/optimum_logo.png',
                          isAntiAlias: true,
                          height: 25,
                        ),
                      ],
                    ),
                  )),
              Image.asset(
                'assets/images/mandob_logo.png',
                isAntiAlias: true,
              ),
            ],
          ),
        ),
        const LoginLogoText(),
      ],
    );
  }
}
