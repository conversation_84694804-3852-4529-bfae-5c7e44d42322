// import 'package:adobe_xd/pinned.dart';
// import 'package:flutter/material.dart';
//
// //UI elements **************
// List<Widget> bg = [
//   Pinned.fromPins(
//     Pin(size: 99.0, start: 24.0),
//     Pin(size: 100.0, middle: 0.1831),
//     child: Container(
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(72.0),
//         gradient: const LinearGradient(
//           begin: Alignment(0.87, 0.0),
//           end: Alignment(-1.0, 0.0),
//           colors: [Color(0x0006c4f1), Color(0x30349fb9)],
//           stops: [0.0, 1.0],
//         ),
//       ),
//     ),
//   ),
//   Pinned.fromPins(
//     Pin(size: 35.9, middle: 0.6428),
//     Pin(size: 36.2, middle: 0.7821),
//     child: Transform.rotate(
//       angle: 3.159,
//       child: Container(
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(72.0),
//           gradient: const LinearGradient(
//             begin: Alignment(0.87, 0.0),
//             end: Alignment(-1.0, 0.0),
//             colors: [Color(0x0006c4f1), Color(0x30349fb9)],
//             stops: [0.0, 1.0],
//           ),
//         ),
//       ),
//     ),
//   ),
//   Pinned.fromPins(
//     Pin(size: 51.0, end: 49.5),
//     Pin(size: 51.6, middle: 0.258),
//     child: Transform.rotate(
//       angle: 3.159,
//       child: Container(
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(72.0),
//           gradient: const LinearGradient(
//             begin: Alignment(0.87, 0.0),
//             end: Alignment(-1.0, 0.0),
//             colors: [Color(0x0006c4f1), Color(0x30349fb9)],
//             stops: [0.0, 1.0],
//           ),
//         ),
//       ),
//     ),
//   ),
//   Pinned.fromPins(
//     Pin(size: 144.0, end: -32.0),
//     Pin(size: 146.0, start: -45.0),
//     child: Container(
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(72.0),
//         gradient: const LinearGradient(
//           begin: Alignment(0.87, 0.0),
//           end: Alignment(-1.0, 0.0),
//           colors: [Color(0xff06c4f1), Color(0xff349fb9)],
//           stops: [0.0, 1.0],
//         ),
//       ),
//     ),
//   ),
//   Pinned.fromPins(
//     Pin(size: 281.0, start: -67.0),
//     Pin(size: 266.0, end: -118.0),
//     child: Container(
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(133.0),
//         gradient: const LinearGradient(
//           begin: Alignment(0.87, 0.0),
//           end: Alignment(-1.0, 0.0),
//           colors: [Color(0xff06c4f1), Color(0xff349fb9)],
//           stops: [0.0, 1.0],
//         ),
//       ),
//     ),
//   )
// ];
