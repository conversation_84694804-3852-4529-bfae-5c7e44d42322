import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:flutter/material.dart';

class LoginLogoText extends StatelessWidget {
  const LoginLogoText({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Container(
          padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
          child: AnimatedTextKit(
            isRepeatingAnimation: true,
            animatedTexts: [
              ColorizeAnimatedText('مــنـدوب - Mandob',
                  colors: [
                    Color(0xFFEFAD19),
                    Color(0xFF72D5EF),
                    Color(0xFFEFAD19),
                    Color(0xFF72D5EF),
                    Color(0xFFEFAD19),
                    Color(0xFF72D5EF),
                    Color(0xFFEFAD19),
                  ],
                  textStyle: Theme.of(context)
                      .textTheme
                      .headlineLarge!
                      .copyWith(fontFamily: 'Cairo'),
                  speed: Duration(milliseconds: 600))
            ],
          ),
        )
      ],
    );
  }
}
