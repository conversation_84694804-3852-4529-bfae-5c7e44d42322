import 'package:flutter/material.dart';
import 'package:mandob/theme.dart';

import '../../../../utils/loading_widget.dart';
import 'components/reset_button.dart';
import 'components/reset_passwords_fields.dart';

class ResetPasswordPage extends StatefulWidget {
  static const nv = "/reset-password";
  @override
  _ResetPasswordPageState createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage> {
  Map<String, String> pwData = {"oldP": "", "newP": "", "confirmP": ""};
  bool _loading = false;
  GlobalKey<FormState> _formKey = GlobalKey();

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    _formKey.currentState!.save();
    setState(() {
      _loading = true;
    });

    var success;

    if (success) {
      _showErrorDialog("تاكد من كلمة السر الحاليه");
    } else {
      Navigator.pop(context);
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text("تم اعادة تعيين كلمة السر")));
    }
    setState(() {
      _loading = false;
    });
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text("تم اعادة تعيين كلمة السر"),
      duration: Duration(seconds: 1),
    ));
  }

  void _showErrorDialog(String errorMessage) async {
    showDialog(
        context: context,
        builder: (ctx) => AlertDialog(
              content: Text(errorMessage),
              actions: [
                TextButton(
                  child: Text("اعد المحاوله"),
                  onPressed: () => Navigator.of(ctx).pop(),
                )
              ],
            ));
  }

  @override
  Widget build(BuildContext context) {
    final _dSize = MediaQuery.of(context).size;
    return Container(
      padding: EdgeInsets.only(top: _dSize.height * 0.2),
      child: Material(
        animationDuration: Duration(seconds: 3),
        elevation: 10,
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(35), topRight: Radius.circular(35)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
                width: 100,
                height: 100,
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Image.asset(
                  'assets/images/logo-s.png',
                  fit: BoxFit.contain,
                )),
            Text(
              "اعادة تعيين كلمة المرور",
              style: Them.pageTitle,
            ),
            ResetPasswordFields(
              formKey: _formKey,
              pwData: {},
            ),
            _loading
                ? Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 30.0),
                    child: LoadingWidget(),
                  )
                : ResetButton(onPressed: () => _submit()),
          ],
        ),
      ),
    );
  }
}
