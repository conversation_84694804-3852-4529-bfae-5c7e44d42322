import 'package:flutter/material.dart';

class ResetButton extends StatelessWidget {
  final void Function()? onPressed;

  const ResetButton({Key? key, required this.onPressed}) : super(key: key);

  @override
  Widget build(BuildContext context) {
      return Padding(
        padding: const EdgeInsets.only(top: 10, right: 100, left: 100),
        child: Container(
          alignment: Alignment.bottomRight,
          height: 50,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.blue.shade300,
                blurRadius: 10.0,
                spreadRadius: 1.0,
                offset: Offset(
                  5.0,
                  5.0,
                ),
              ),
            ],
            color: Colors.white,
            borderRadius: BorderRadius.circular(30),
          ),
          child: TextButton(
            onPressed: onPressed,
            child: Center(child: Text("اعادة تعيين")),
          ),
        ),
      );
  }
}
