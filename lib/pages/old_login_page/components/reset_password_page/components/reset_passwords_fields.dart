import 'package:flutter/material.dart';

class ResetPasswordFields extends StatelessWidget {
  final formKey;
  final Map<String, String> pwData;
  ResetPasswordFields({Key? key, required this.formKey, required this.pwData})
      : super(key: key);

  TextEditingController _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final _dSize = MediaQuery.of(context).size;

    final _inputDeco = InputDecoration(
      fillColor: Colors.transparent,
      contentPadding: EdgeInsets.fromLTRB(10.0, 15.0, 10.0, 10.0),
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(10.0)),
      filled: true,
    );

    return Container(
      height: _dSize.height * 0.35,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Form(
          key: formKey,
          child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            TextFormField(
              textAlign: TextAlign.center,
              decoration: _inputDeco.copyWith(labelText: "كلمة السر الحاليه"),
              onSaved: (newValue) => pwData["oldP"] = newValue.toString(),
              validator: (value) {
                if (value!.length < 6) return "كلمة المرور قصيرة";

                return null;
              },
              textDirection: TextDirection.ltr,
              obscureText: true,
            ),
            const SizedBox(
              height: 5,
            ),
            TextFormField(
              decoration: _inputDeco.copyWith(labelText: "كلمة السر الجديده"),
              controller: _controller,
              onSaved: (newValue) => pwData["newP"] = newValue.toString(),
              validator: (value) {
                if (value!.length < 6) {
                  return "كلمة المرور قصيرة";
                } else {
                  return null;
                }
              },
              textAlign: TextAlign.center,
              textDirection: TextDirection.ltr,
              obscureText: true,
            ),
            const SizedBox(
              height: 5,
            ),
            TextFormField(
              decoration:
                  _inputDeco.copyWith(labelText: "تأكيد كلمة السر الجديده"),
              onSaved: (newValue) => pwData["confirmP"] = newValue.toString(),
              validator: (value) {
                if (value!.length < 6) {
                  return "كلمة المرور قصيرة";
                } else if (value != _controller.text) {
                  return "كلمة السر غير متطابقة";
                } else {
                  return null;
                }
              },
              textAlign: TextAlign.center,
              textDirection: TextDirection.ltr,
              obscureText: true,
            ),
          ])),
    );
  }
}
