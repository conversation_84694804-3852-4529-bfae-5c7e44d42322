import 'package:flutter/material.dart';
import 'package:mandob/appwrite_db/appwrite.dart';
import 'package:mandob/appwrite_db/db_consts.dart';
import 'package:mandob/pages/main_screen/main_screen.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/loading_indicator.dart';
import 'package:mandob/utils/shared_preferences.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:provider/provider.dart';

import 'components/login_widgets.dart';
import 'components/top_section.dart';

class OldLoginPage extends StatefulWidget {
  static const nv = "login-page";

  const OldLoginPage({Key? key}) : super(key: key);

  @override
  _OldLoginPageState createState() => _OldLoginPageState();
}

class _OldLoginPageState extends State<OldLoginPage> {
  bool _loading = false;
  final GlobalKey<FormState> _formKey = GlobalKey();

  String? email;
  String? password;
  String? databaseId;

  bool isDatabaseIdField = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: const Color(0xfffbfbfb),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                const TopSectionLogin(),
                if (databaseId != null && !isDatabaseIdField)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text('الشركة  $databaseId'),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              databaseId = null;
                              isDatabaseIdField = true;
                            });
                          },
                          icon: const CircleAvatar(
                              maxRadius: 12,
                              backgroundColor: ColorManager.primaryColor,
                              child: Icon(
                                Icons.edit,
                                size: 15,
                                color: Colors.white,
                              )),
                        )
                      ],
                    ),
                  ),
                Material(
                  shadowColor: const Color(0xFFF9CC15),
                  color: Colors.white,
                  borderRadius: const BorderRadius.only(
                      bottomRight: Radius.circular(150),
                      bottomLeft: Radius.circular(150)),
                  elevation: 5,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 5.0, horizontal: 30),
                    child: Form(
                      key: _formKey,
                      child: SingleChildScrollView(
                        child: Column(
                          children: <Widget>[
                            if (isDatabaseIdField) ...[
                              inputDatabaseField(databaseId, (val) {
                                setState(() {
                                  if (val.isNotEmpty) {
                                    databaseId = val;
                                  }
                                });
                              }),
                            ] else ...[
                              inputEmail(email, (val) {
                                setState(() {
                                  email = val;
                                });
                              }),
                              passwordInput(password, (val) {
                                setState(() {
                                  password = val;
                                });
                              }),
                            ],
                            loadingIndicator(_loading),
                            buttonLogin(
                              context,
                              onPressed: () => _submit(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  void _submit() async {
    if (isDatabaseIdField) {
      _saveDatabaseId();
    } else {
      _login();
    }
  }

  void _saveDatabaseId() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _loading = true;
      });
      await saveString(key: DbConsts.databaseIdPref, value: databaseId!);
      AppwriteDB.init();
      _formKey.currentState!.reset();
      setState(() {
        isDatabaseIdField = false;
        _loading = false;
      });
    }
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState!.save();
    setState(() {
      _loading = true;
    });

    try {
      await Provider.of<UserProvider>(context, listen: false)
          .loginUser(email: email, password: password);

      Navigator.push(
          context, MaterialPageRoute(builder: (context) => const MainScreen()));
    } catch (e) {
      showBar(context, 'حدث خطأ، برجاء التأكد ان البيانات المدخلة صحيحه');
    }
    setState(() {
      _loading = false;
    });
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Center(
          child: Text(message),
        ),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text("اعد المحاوله"))
        ],
      ),
    );
  }
}
