import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mandob/pages/login_page/login_page.dart';
import 'package:mandob/pages/main_screen/main_screen.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

class SplashScreen extends HookWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        final loggedIn = userProvider.activeUser != null;

        return HookBuilder(builder: (context) {
          Future<void> getData() async {
            if (loggedIn) {
              await Future.wait([
                Provider.of<RepositoryProvider>(context, listen: false)
                    .fetchRepositories(),
                Provider.of<ProductsProvider>(context, listen: false)
                    .fetchProducts(),
                Provider.of<QuantitiesProvider>(context, listen: false)
                    .fetchQuantities(),
                Provider.of<UserProvider>(context, listen: false)
                    .getCurrentUser(),
              ]);
            } else {
              await Future.delayed(const Duration(seconds: 3));
            }
            // await Provider.of<CustomersProvider>(context, listen: false)
            //     .fetchAllCustomers();
          }

          useEffect(() {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              getData().then(
                (value) {
                  final navigateWidget =
                      loggedIn ? const MainScreen() : const LoginPage();

                  // Future.delayed(const Duration(seconds: 3), () {
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => navigateWidget,
                    ),
                  );
                  // });
                },
              );
            });

            return () {};
          }, []);

          return Material(
            child: Image.asset(
              "assets/images/splash.gif",
              height: context.height,
              width: context.width,
              fit: BoxFit.cover,
            ),
          );
        });
      },
    );
  }
}
