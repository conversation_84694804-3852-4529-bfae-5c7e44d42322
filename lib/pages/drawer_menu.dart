import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/core/shared/language/change_language.widget.dart';
import 'package:mandob/pages/collections_page/collections_page.dart';
import 'package:mandob/pages/company_page/company_info_page.dart';
import 'package:mandob/pages/customer_page/customer_page.dart';
import 'package:mandob/pages/delivery_page/delivery_page.dart';
import 'package:mandob/pages/expenses_page/expenses_page.dart';
import 'package:mandob/pages/login_page/login_page.dart';
import 'package:mandob/pages/receipt_page/components/invoice_table/purchases_table/main_purchase_table.dart';
import 'package:mandob/pages/reports_page/reports_page.dart';
import 'package:mandob/pages/supplier_page/supplier_page.dart';
import 'package:mandob/pages/supplier_payments_page/supplier_payments_page.dart';
import 'package:mandob/providers/company_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:provider/provider.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:xr_helper/xr_helper.dart';

import 'cashier_page/cashier_page.dart';
import 'customer_statement_page/customer_statement_page.dart';
import 'receipt_page/components/invoice_table/sales_table/main_sales_table.dart';

class DrawerMenu extends StatelessWidget {
  const DrawerMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<CompanyInfoProvider, UserProvider>(
        builder: (context, companyInfoProvider, userProvider, child) {
      final companyInfo = companyInfoProvider.companyInfo;
      final imageId = companyInfo?.logoUrl ?? '';

      return HookBuilder(builder: (context) {
        useEffect(() {
          companyInfoProvider.fetchCompanyInfo();
          return () {};
        }, const []);

        final controllers = List.generate(5, (_) => ExpansionTileController());

        return Drawer(
          child: Stack(
            alignment: context.isEng ? Alignment.topRight : Alignment.topLeft,
            children: [
              Column(
                children: [
                  // * Header Section,
                  UserAccountsDrawerHeader(
                    decoration:
                        const BoxDecoration(color: ColorManager.primaryColor),
                    currentAccountPicture: FutureBuilder(
                        future: getLogo(imageId),
                        builder: (context, snapshot) {
                          if (snapshot.hasData && imageId != '') {
                            final Uint8List image = snapshot.data as Uint8List;
                            return ClipRRect(
                              borderRadius: BorderRadius.circular(100),
                              child: Image.memory(
                                image,
                                fit: BoxFit.cover,
                              ),
                            );
                          } else {
                            return const SizedBox();
                          }
                        }),
                    accountName: Text(
                      userProvider.activeUser?.name ?? '',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    accountEmail: Text(
                      userProvider.activeUser?.email ?? '',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  // Menu Items
                  Expanded(
                    child: ListView(
                      children: [
                        _buildExpansionTile(
                          context,
                          index: 0,
                          controllers: controllers,
                          title: context.isEng
                              ? "Basic Data"
                              : "البيانات الأساسية",
                          icon: Icons.home,
                          children: [
                            if (userProvider.activeUser?.isAdmin == true)
                              _buildListTile(
                                  context,
                                  context.isEng
                                      ? "Company Info"
                                      : "بيانات الشركه",
                                  Icons.settings,
                                  CompanyInfoPage()),
                            _buildListTile(
                                context,
                                context.isEng ? "Suppliers" : "الموردين",
                                Icons.storefront,
                                const SupplierPage()),
                            _buildListTile(
                                context,
                                context.isEng ? "Customers" : "العملاء",
                                Icons.supervised_user_circle_sharp,
                                const CustomerPage()),
                            if (userProvider.activeUser?.isAdmin == true)
                              _buildListTile(
                                  context,
                                  context.isEng ? "Deliveries" : "المندوبين",
                                  Icons.delivery_dining,
                                  const DeliveryPage()),
                          ],
                        ),
                        _buildExpansionTile(
                          context,
                          index: 1,
                          controllers: controllers,
                          title: context.isEng ? "Purchases" : "المشتريات",
                          icon: CupertinoIcons.cart_badge_plus,
                          children: [
                            _buildListTile(
                              context,
                              context.isEng
                                  ? "Purchase Invoices"
                                  : "فواتير المشتريات",
                              Icons.storefront,
                              PurchaseInvoiceTable(
                                isReturned: false,
                                title: context.isEng
                                    ? "Purchase Invoices"
                                    : "فواتير المشتريات",
                                // fromHome: true,
                              ),
                            ),
                            _buildListTile(
                                context,
                                context.isEng
                                    ? "Purchase Return Invoices"
                                    : "فواتير مرتجع المشتريات",
                                Icons.supervised_user_circle_sharp,
                                PurchaseInvoiceTable(
                                  isReturned: true,
                                  title: context.isEng
                                      ? "Purchase Return Invoices"
                                      : "فواتير مرتجع المشتريات",
                                  // fromHome: true,
                                )),
                            _buildListTile(
                                context,
                                context.isEng
                                    ? "Supplier Payments"
                                    : "سداد الموردين",
                                Icons.attach_money,
                                SupplierPaymentsPage()),
                          ],
                        ),
                        _buildExpansionTile(
                          context,
                          index: 2,
                          controllers: controllers,
                          title: context.isEng ? "Sales" : "المبيعات",
                          icon: CupertinoIcons.money_dollar_circle,
                          children: [
                            _buildListTile(
                              context,
                              context.isEng
                                  ? "Sales Invoices"
                                  : "فواتير المبيعات",
                              Icons.storefront,
                              SalesInvoicesTable(
                                isReturned: false,
                                title: context.isEng
                                    ? "Sales Invoices"
                                    : "فواتير المبيعات",
                              ),
                            ),
                            _buildListTile(
                              context,
                              context.isEng
                                  ? "Return Invoices"
                                  : "فواتير المرتجعات",
                              Icons.supervised_user_circle_sharp,
                              SalesInvoicesTable(
                                isReturned: true,
                                title: context.isEng
                                    ? "Return Invoices"
                                    : "فواتير المرتجعات",
                              ),
                            ),
                            _buildListTile(
                                context,
                                context.isEng
                                    ? "Customer Payments"
                                    : "سداد العملاء",
                                Icons.storage,
                                const CollectionsPage()),
                          ],
                        ),
                        _buildExpansionTile(
                          context,
                          index: 3,
                          controllers: controllers,
                          title: context.isEng ? "Finance" : "مالية",
                          icon: Icons.account_balance_wallet,
                          children: [
                            _buildListTile(
                                context,
                                context.isEng ? "Expenses" : "المصروفات",
                                Icons.payments_outlined,
                                ExpensesPage()),
                            _buildListTile(
                                context,
                                context.isEng ? "Cashier" : "الخزينه",
                                Icons.payment,
                                CashierPage()),
                          ],
                        ),
                        _buildExpansionTile(
                          context,
                          index: 4,
                          controllers: controllers,
                          title: context.isEng ? "Reports" : "تقارير",
                          icon: Icons.bar_chart,
                          children: [
                            _buildListTile(
                                context,
                                context.isEng
                                    ? "Sales Reports"
                                    : "تقارير المبيعات",
                                Icons.view_agenda_rounded,
                                const ReportsPage(isSales: true)),
                            _buildListTile(
                                context,
                                context.isEng
                                    ? "Purchase Reports"
                                    : "تقارير المشتريات",
                                Icons.view_agenda_rounded,
                                const ReportsPage(isSales: false)),
                            _buildListTile(
                                context,
                                context.isEng
                                    ? "Customer Statements"
                                    : "كشف حساب العملاء",
                                Icons.perm_contact_cal_outlined,
                                const CustomerPageStatement()),
                            _buildListTile(
                                context,
                                context.isEng
                                    ? "Supplier Statements"
                                    : "كشف حساب الموردين",
                                Icons.perm_contact_cal_outlined,
                                const CustomerPageStatement(
                                  isPurchase: true,
                                )),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Logout Button
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10.0, horizontal: 20.0),
                    child: ElevatedButton.icon(
                      onPressed: () => _logout(context),
                      icon: const Icon(
                        Icons.logout,
                        color: Colors.white,
                      ),
                      label: Text(
                        context.isEng ? "Logout" : "تسجيل الخروج",
                        style: const TextStyle(
                          color: Colors.white,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorManager.errorColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 30,
                ),
                child: IconButton(
                  onPressed: () {
                    showChangeLanguageDialog(context);
                  },
                  icon: const Icon(
                    CupertinoIcons.globe,
                    size: 35,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        );
      });
    });
  }

  Widget _buildExpansionTile(
    BuildContext context, {
    required int index,
    required List<ExpansionTileController> controllers,
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return ExpansionTile(
      leading: Icon(icon, color: ColorManager.primaryColor),
      title:
          Text(title, style: const TextStyle(color: ColorManager.primaryColor)),
      iconColor: ColorManager.secondaryColor,
      controller: controllers[index],
      onExpansionChanged: (isOpen) {
        if (isOpen) {
          for (int i = 0; i < controllers.length; i++) {
            if (i != index) {
              controllers[i].collapse();
            }
          }
        }
      },
      children: children,
    );
  }

  Widget _buildListTile(
      BuildContext context, String title, IconData icon, Widget page) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InkWell(
          onTap: () => Navigator.of(context)
              .push(MaterialPageRoute(builder: (context) => page)),
          child: Row(
            children: [
              const Icon(
                Icons.lens,
                color: ColorManager.secondaryColor,
                size: 12,
              ),
              AppGaps.gap8,
              Text(
                title,
                style: const TextStyle(fontSize: 14),
              ).paddingSymmetric(vertical: 10),
            ],
          ).paddingSymmetric(horizontal: 35),
        ),
        Divider(color: Colors.blueGrey.shade100, indent: 16, endIndent: 16),
      ],
    );
  }

  void _logout(BuildContext context) {
    QuickAlert.show(
      context: context,
      confirmBtnColor: ColorManager.errorColor,
      widget: Text(context.isEng
              ? "Are you sure you want to logout?"
              : "هل انت متأكد أنك تريد تسجيل الخروج؟")
          .paddingOnly(top: 15),
      type: QuickAlertType.error,
      showCancelBtn: true,
      title: context.isEng ? "Logout" : "تسجيل الخروج",
      titleAlignment: TextAlign.right,
      cancelBtnText: context.isEng ? "Cancel" : "إلغاء",
      confirmBtnText: context.isEng ? "Confirm" : "تأكيد",
      onConfirmBtnTap: () async {
        Provider.of<UserProvider>(context, listen: false).logOut();
        navService.back();
        const LoginPage(
          isLicenseView: false,
        ).navigateReplacement;
      },
    );
  }
}
