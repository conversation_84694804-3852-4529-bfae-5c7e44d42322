import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/invoice_model.dart';
import 'package:mandob/pages/receipt_page/components/invoice_table/purchases_table/main_purchase_table.dart';
import 'package:mandob/pages/receipt_page/components/invoice_table/sales_table/main_sales_table.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:provider/provider.dart';

import '../../../models/quantity_model.dart';
import '../../../providers/invoices_provider.dart';
import '../../../utils/loading_widget.dart';

class DeleteInvoiceDialog extends StatefulWidget {
  final InvoiceModel? invoice;

  DeleteInvoiceDialog({required this.invoice});

  @override
  State<StatefulWidget> createState() => _DeleteRepositoryDialog();
}

class _DeleteRepositoryDialog extends State<DeleteInvoiceDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  @override
  void initState() {
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  bool loading = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<QuantitiesProvider>(builder: (context, quat, child) {
      return FutureBuilder(
          future: quat.fetchQuantities(),
          builder: (context, snapshot) {
            return Stack(
              children: <Widget>[
                Center(
                  child: Material(
                    color: Colors.transparent,
                    child: ScaleTransition(
                      scale: scaleAnimation,
                      child: Container(
                          width: 250,
                          padding: const EdgeInsets.all(15),
                          decoration: ShapeDecoration(
                              color: Colors.white,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(15.0))),
                          child: SingleChildScrollView(
                            child: Wrap(
                              children: <Widget>[
                                Padding(
                                  padding: const EdgeInsets.only(
                                      right: 4.0, left: 4),
                                  child: Center(
                                    child: Text(
                                      context.isEng
                                          ? 'Delete Invoice'
                                          : 'هل تريد حذف هذه الفاتورة !',
                                      style: const TextStyle(
                                          color: Colors.black87,
                                          fontSize: 18,
                                          height: 1.5),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: 25, bottom: 10, right: 10),
                                  child: Row(
                                    textDirection: TextDirection.rtl,
                                    children: <Widget>[
                                      const SizedBox(width: 10),
                                      GestureDetector(
                                        onTap: () => Navigator.pop(context),
                                        child: Container(
                                          height: 35,
                                          width: 80,
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(7),
                                              color: Colors.grey),
                                          child: const Text(
                                            'إلغاء',
                                            style:
                                                TextStyle(color: Colors.white),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 15),
                                      GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            loading = true;
                                          });
                                          Map<String, dynamic>? addedMap = {};

                                          widget.invoice!.products!
                                              .forEach((element) {
                                            addedMap.addAll({
                                              element!['id'].toString():
                                                  int.tryParse(
                                                      element['quantity']
                                                          .toString())
                                            });
                                          });

                                          print('aa $addedMap');

                                          List<QuantityModel>
                                              receiverQuatitiesValue = [];
                                          int oldReceiverValue = 0;

                                          addedMap.forEach((key, value) async {
                                            receiverQuatitiesValue = quat
                                                .quantities
                                                .where((element) =>
                                                    element.productId == key)
                                                .toList();

                                            if (receiverQuatitiesValue
                                                .isNotEmpty) {
                                              receiverQuatitiesValue[0]
                                                  .quantities!
                                                  .forEach((key, val) {
                                                if (key ==
                                                    widget.invoice!.storeId!) {
                                                  setState(() {
                                                    oldReceiverValue = val;
                                                  });
                                                }
                                              });
                                            }

                                            if (widget.invoice!.isSales!) {
                                              await Provider.of<
                                                          QuantitiesProvider>(
                                                      context,
                                                      listen: false)
                                                  .modifyQuantity(
                                                      productId: key,
                                                      storeId: widget
                                                          .invoice!.storeId,
                                                      quantity:
                                                          oldReceiverValue +
                                                              value);
                                            } else {
                                              await Provider.of<
                                                          QuantitiesProvider>(
                                                      context,
                                                      listen: false)
                                                  .modifyQuantity(
                                                      productId: key,
                                                      storeId: widget
                                                          .invoice!.storeId,
                                                      quantity:
                                                          oldReceiverValue -
                                                              value);
                                            }
                                          });

                                          Provider.of<InvoicesProvider>(context,
                                                  listen: false)
                                              .deleteInvoice(widget
                                                  .invoice!.invoiceId
                                                  .toString());

                                          setState(() {
                                            loading = false;
                                            scrollingSalesInvoices = false;
                                            scrollingPurchaseInvoices = false;
                                          });

                                          Navigator.pop(context);

                                          showBar(
                                              context,
                                              context.isEng
                                                  ? 'Invoice Deleted !'
                                                  : 'تم حذف الفانورة !');
                                        },
                                        child: Container(
                                          height: 35,
                                          width: 80,
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(7),
                                              color: Colors.red),
                                          child: Text(
                                            context.isEng ? 'Delete' : 'حذف',
                                            style: const TextStyle(
                                                color: Colors.white),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            ),
                          )),
                    ),
                  ),
                ),
                loading
                    ? const Center(
                        child: LoadingWidget(),
                      )
                    : Container()
              ],
            );
          });
    });
  }
}
