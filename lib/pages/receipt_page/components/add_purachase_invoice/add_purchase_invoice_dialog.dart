import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/company_info_model.dart';
import 'package:mandob/models/operation_model.dart';
import 'package:mandob/models/quantity_model.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/models/supplier_model.dart';
import 'package:mandob/models/supplier_payment_model.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/pages/receipt_page/components/invoice_table/purchases_table/main_purchase_table.dart';
import 'package:mandob/pages/repositories_page/transfer_repository_items/transfer_table/main_transfer_table.dart';
import 'package:mandob/providers/cashier_provider.dart';
import 'package:mandob/providers/collections_provider.dart';
import 'package:mandob/providers/company_provider.dart';
import 'package:mandob/providers/invoices_provider.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/providers/supplier_payments_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/extensions.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:mandob/utils/submit_button.dart';
import 'package:mandob/utils/text_field.dart';
import 'package:persian_number_utility/src/extensions.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../models/invoice_model.dart';
import '../../../../providers/language.dart';
import '../../../../utils/color_manager.dart';
import '../../../../utils/loading_widget.dart';
import '../../../../utils/show_bar/messages.dart';

class AddPurchaseInvoiceDialog extends StatefulWidget {
  final bool isReturned;
  final bool byProductPrice;
  final String? senderRepo;
  final InvoiceModel? invoiceData;
  final SupplierModel supplier;

  const AddPurchaseInvoiceDialog({
    super.key,
    required this.isReturned,
    required this.byProductPrice,
    this.senderRepo,
    required this.supplier,
    this.invoiceData,
  });

  @override
  State<StatefulWidget> createState() => _AddPurchaseInvoiceDialog();
}

class _AddPurchaseInvoiceDialog extends State<AddPurchaseInvoiceDialog> {
  // late double taxValue = 1.15;
  late double totalItemsPrice = 0.0;
  late double totalValue = 0.0;
  late String supplierName = '';

  List<String> itemsStringController = [];
  List<int?> quantities = [];

  var randomNumber = Random.secure();
  late double invoiceNumber;

  List get items => widget.invoiceData?.products ?? [];

  @override
  void initState() {
    invoiceNumber = randomNumber.nextDouble() * 1000000;
    while (invoiceNumber < 100000) {
      invoiceNumber *= 10;
    }
    Provider.of<CompanyInfoProvider>(context, listen: false).fetchCompanyInfo();
    Provider.of<CollectionsProvider>(context, listen: false).fetchCollections();

    if (widget.isReturned) {
      for (var element in items) {
        totalItemsPrice += element['price'] * element['quantity'];
        itemsStringController.add(items.length.toString());

        quantities.add(int.tryParse(element['quantity'].toString()));

        addedMap.addAll({
          element['id'].toString(): int.tryParse(element['quantity'].toString())
        });
      }
    } else {
      for (var element in addedItems) {
        totalItemsPrice += element['price'] * element['quantity'];
        if (widget.byProductPrice == false) {
          totalValue += element['paidPrice'];
        }
      }
      if (widget.byProductPrice == false) {
        priceCtrl.text = totalValue.roundedPrecisionToString(5);
        taxVal = totalValue - totalItemsPrice;
      }
    }

    super.initState();
  }

  bool loading = false;

  var customerNameCtrl = TextEditingController();
  var priceCtrl = TextEditingController();
  var productsQuantityCtrl = TextEditingController();

  var formKey = GlobalKey<FormState>();

  var nameFocus = FocusNode();
  var priceFocus = FocusNode();
  var phoneFocus = FocusNode();
  var customerNameFocus = FocusNode();
  var productsQuantityFocus = FocusNode();

  List<String> productsValue = [];

  bool moreThanQuantity = false;

  bool invalid = false;

  var remainingCash = 0.0;

  double? taxVal = 0;

  @override
  Widget build(BuildContext context) {
    final lang = Provider.of<LangProvider>(context, listen: false);

    return WillPopScope(
      onWillPop: () async {
        if (MediaQuery.of(context).viewInsets.bottom != 0) {
          nameFocus.unfocus();
          priceFocus.unfocus();
          customerNameFocus.unfocus();
          productsQuantityFocus.unfocus();
          addedItems.clear();
          addedMap.clear();
        } else
          Navigator.pop(context);
        addedItems.clear();
        addedMap.clear();
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          title: Stack(
            alignment: Alignment.center,
            children: [
              GestureDetector(
                onTap: () {
                  addedItems.clear();
                  addedMap.clear();
                  Navigator.pop(context);
                },
                child: Align(
                  alignment: context.isEng
                      ? Alignment.centerLeft
                      : Alignment.centerRight,
                  child: const CircleAvatar(
                    backgroundColor: ColorManager.primaryColor,
                    radius: 20,
                    child: Icon(
                      Icons.arrow_back_ios_outlined,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              Center(
                child: Text(
                  widget.isReturned
                      ? (context.isEng ? 'Return Invoice' : 'فاتورة مرتجعات')
                      : (context.isEng ? 'New Invoice' : 'فاتورة جديدة'),
                  style: const TextStyle(
                      color: Colors.black,
                      fontSize: 20,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          automaticallyImplyLeading: false,
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body:
            Consumer<CompanyInfoProvider>(builder: (context, infoData, child) {
          return FutureBuilder(
              future: infoData.fetchCompanyInfo(),
              builder: (context, snapshot) {
                // if (snapshot.connectionState == ConnectionState.waiting) {
                //   return const LoadingWidget();
                // }

                final info = infoData.companyInfo;

                if (info != null && info.tax != null && info.tax != 0) {
                  if (totalValue == 0.0) {
                    if (widget.byProductPrice) {
                      taxVal = (info.tax! / 100) * totalItemsPrice;
                      totalValue = totalItemsPrice + taxVal!;
                      priceCtrl.text = totalValue.roundedPrecisionToString(5);
                    }
                  }
                } else {
                  if (totalValue == 0.0) {
                    if (widget.byProductPrice) {
                      taxVal = (15 / 100) * totalItemsPrice;
                      totalValue = totalItemsPrice + taxVal!;
                      priceCtrl.text = totalValue.roundedPrecisionToString(5);
                    }
                  }
                }

                return Consumer<RepositoryProvider>(
                  builder: (context, repo, child) {
                    return FutureBuilder(
                      future: infoData.fetchCompanyInfo(),
                      builder: (context, snapshot) {
                        final info = infoData.companyInfo;

                        if (info != null && info.tax != null && info.tax != 0) {
                          if (totalValue == 0.0) {
                            if (widget.byProductPrice) {
                              taxVal = (info.tax! / 100) * totalItemsPrice;
                              totalValue = totalItemsPrice + taxVal!;
                              priceCtrl.text =
                                  totalValue.roundedPrecisionToString(5);
                            }
                          }
                        } else {
                          if (totalValue == 0.0) {
                            if (widget.byProductPrice) {
                              taxVal = (15 / 100) * totalItemsPrice;
                              totalValue = totalItemsPrice + taxVal!;
                              priceCtrl.text =
                                  totalValue.roundedPrecisionToString(5);
                            }
                          }
                        }

                        return Consumer<QuantitiesProvider>(
                          builder: (context, quantatiesData, child) {
                            return Consumer<UserProvider>(
                              builder: (context, userData, child) {
                                //? If return invoice ------------------------------
                                // Widget returnedWidget() {
                                //   return ListView.builder(
                                //     shrinkWrap: true,
                                //     itemCount: items.length,
                                //     scrollDirection: Axis.vertical,
                                //     itemBuilder: (context, index) {
                                //       var itemList = items;
                                //       var quantity = quantities[index];
                                //
                                //       return Row(
                                //         crossAxisAlignment:
                                //             CrossAxisAlignment.center,
                                //         children: [
                                //           Expanded(
                                //             child: TextFieldWidget(
                                //               initialValue: itemList[index]
                                //                       ['quantity']
                                //                   .toString(),
                                //               onChanged: (val) {
                                //                 setState(() {
                                //                   if (val != '' && val != '0') {
                                //                     if (int.tryParse(val)! >
                                //                             quantity! ||
                                //                         int.tryParse(val) ==
                                //                             null) {
                                //                       setState(() {
                                //                         moreThanQuantity = true;
                                //                       });
                                //                       showBar(
                                //                           context,
                                //                           context.isEng
                                //                               ? 'Please choose a quantity less than or equal to the quantity of the product!'
                                //                               : 'برجاء اختيار كمية مرتجع أقل من أو تساوي كمية المنتج!');
                                //                       return;
                                //                     }
                                //                     setState(() {
                                //                       moreThanQuantity = false;
                                //                     });
                                //
                                //                     itemsStringController[
                                //                         index] = val;
                                //
                                //                     addedMap.addAll({
                                //                       itemList[index]['id']
                                //                               .toString():
                                //                           int.tryParse(val)
                                //                     });
                                //
                                //                     items.forEach((element) {
                                //                       if (element['id'] ==
                                //                           itemList[index]
                                //                               ['id']) {
                                //                         element['quantity'] =
                                //                             int.tryParse(val);
                                //                       }
                                //                     });
                                //
                                //                     totalItemsPrice = 0.0;
                                //
                                //                     items.forEach((element) {
                                //                       totalItemsPrice +=
                                //                           element['price'] *
                                //                               element[
                                //                                   'quantity'];
                                //                     });
                                //
                                //                     var taxVal = ((info ==
                                //                                         null ||
                                //                                     info.tax ==
                                //                                         null
                                //                                 ? 15
                                //                                 : info.tax)! /
                                //                             100) *
                                //                         totalItemsPrice;
                                //                     totalValue =
                                //                         totalItemsPrice +
                                //                             taxVal;
                                //                   }
                                //                 });
                                //               },
                                //               label: context.isEng
                                //                   ? 'Return Quantity ${itemList[index]['name']}'
                                //                   : 'كمية مرتجع ${itemList[index]['name']}',
                                //             ),
                                //           ),
                                //           const SizedBox(width: 5),
                                //           Padding(
                                //             padding: const EdgeInsets.only(
                                //                 top: 14.0),
                                //             child: Row(
                                //               children: [
                                //                 Text(context.isEng
                                //                     ? 'Unit Price'
                                //                     : 'سعر الحبة'),
                                //                 Text(
                                //                     'RS${(itemList[index]!['price'] as num).roundedPrecisionToString(5)}'),
                                //               ],
                                //             ),
                                //           ),
                                //         ],
                                //       );
                                //     },
                                //   );
                                // }
                                Widget returnedWidget() {
                                  return ListView.separated(
                                    shrinkWrap: true,
                                    itemCount: items.length,
                                    separatorBuilder: (context, index) {
                                      return AppGaps.gap16;
                                    },
                                    scrollDirection: Axis.vertical,
                                    itemBuilder: (context, index) {
                                      var itemList = items;
                                      var quantity = quantities[index];

                                      return Container(
                                        padding: const EdgeInsets.all(16),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          color: ColorManager.lightFieldColor
                                              .withOpacity(0.3),
                                        ),
                                        child: Column(
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  itemList![index]!['name'],
                                                  style: const TextStyle(
                                                    fontSize: 16,
                                                  ),
                                                ),
                                                Row(
                                                  children: [
                                                    const Text(
                                                      'كمية المنتج: ',
                                                      style: TextStyle(
                                                        fontSize: 13,
                                                      ),
                                                    ),
                                                    Text(
                                                      '${quantity}',
                                                      style: const TextStyle(
                                                        fontSize: 16,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                            AppGaps.gap12,
                                            const Divider(
                                              thickness: 1,
                                              color:
                                                  ColorManager.lightFieldColor,
                                            ),
                                            AppGaps.gap12,
                                            Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                const Text(
                                                  'الكمية',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                  ),
                                                ),
                                                AppGaps.gap12,
                                                SizedBox(
                                                  height: 45,
                                                  width: MediaQuery.of(context)
                                                          .size
                                                          .width *
                                                      0.3,
                                                  child: TextFieldWidget(
                                                    initialValue: itemList![
                                                            index]!['quantity']
                                                        .toString(),
                                                    onChanged: (val) {
                                                      setState(() {
                                                        if (val != '' &&
                                                            val != '0') {
                                                          if (int.tryParse(val
                                                                  .toString()
                                                                  .toEnglishDigit())! >
                                                              quantity!) {
                                                            moreThanQuantity =
                                                                true;
                                                            showBar(
                                                                context,
                                                                context.isEng
                                                                    ? 'Please choose a quantity less than or equal to the quantity of the product !'
                                                                    : 'برجاء اختيار كمية مرتجع أقل من أو تساوي كمية المنتج !');
                                                            return;
                                                          }
                                                          moreThanQuantity =
                                                              false;
                                                          itemsStringController[
                                                              index] = val;

                                                          addedMap.addAll({
                                                            itemList[index]![
                                                                        'id']
                                                                    .toString():
                                                                int.tryParse(val
                                                                    .toString()
                                                                    .toEnglishDigit())
                                                          });

                                                          for (var element
                                                              in items!) {
                                                            if (element![
                                                                    'id'] ==
                                                                itemList[
                                                                        index]![
                                                                    'id']) {
                                                              element['quantity'] =
                                                                  int.tryParse(val
                                                                      .toString()
                                                                      .toEnglishDigit());
                                                            }
                                                          }

                                                          totalItemsPrice = 0.0;

                                                          for (var element
                                                              in items!) {
                                                            totalItemsPrice +=
                                                                element![
                                                                        'price'] *
                                                                    element[
                                                                        'quantity'];
                                                          }

                                                          var taxVal =
                                                              ((info?.tax ??
                                                                          15)! /
                                                                      100) *
                                                                  totalItemsPrice;
                                                          totalValue =
                                                              totalItemsPrice +
                                                                  taxVal;
                                                        }
                                                      });
                                                    },
                                                    label: "الكمية",
                                                    // label: 'كمية مرتجع ${itemList[index]!['name']}',
                                                  ),
                                                ),
                                                // AppGaps.gap8,
                                                // const Text('للحبة'),
                                                const Spacer(),
                                                Row(
                                                  children: [
                                                    Text(
                                                      (itemList[index]!['price']
                                                              as num)
                                                          .roundedPrecisionToString(
                                                              5),
                                                      style: const TextStyle(
                                                        fontSize: 16,
                                                        color: ColorManager
                                                            .primaryColor,
                                                      ),
                                                    ),
                                                    Text(context.currency),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                }
                                // Widget invoiceItemsWidget() {
                                //   return ListView.builder(
                                //     shrinkWrap: true,
                                //     itemCount: addedItems.length,
                                //     scrollDirection: Axis.vertical,
                                //     itemBuilder: (context, index) {
                                //       var itemList = addedItems;
                                //       return Row(
                                //         children: [
                                //           Text(
                                //               'x${itemList[index]['quantity']}'),
                                //           const SizedBox(width: 5),
                                //           Expanded(
                                //               child: Text(
                                //                   itemList[index]['name'])),
                                //           const SizedBox(width: 15),
                                //           Expanded(
                                //               child: Text(
                                //                   'RS${(itemList[index]['price'] as num).roundedPrecisionToString(5)}')),
                                //           const SizedBox(width: 30),
                                //           Text(context.isEng
                                //               ? 'Total'
                                //               : 'إجمالي'),
                                //           const SizedBox(width: 10),
                                //           Text(
                                //               'RS${((itemList[index]['price'] * itemList[index]['quantity']) as num).roundedPrecisionToString(5)}'),
                                //         ],
                                //       );
                                //     },
                                //   );
                                // }

                                Widget invoiceItemsWidget() {
                                  return SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: DataTable(
                                      headingRowHeight: 35,
                                      columnSpacing: 55,
                                      border: TableBorder.all(),
                                      columns: [
                                        DataColumn(
                                            label: Padding(
                                          padding: const EdgeInsets.only(
                                              right: 15.0),
                                          child: Text(context.isEng
                                              ? 'Products'
                                              : 'المنتجات'),
                                        )),
                                        DataColumn(
                                            label: Text(context.isEng
                                                ? 'Price'
                                                : 'السعر')),
                                        DataColumn(
                                            label: Text(context.isEng
                                                ? 'Total'
                                                : 'الإجمالي')),
                                      ],
                                      rows: addedItems.map((item) {
                                        return DataRow(cells: [
                                          DataCell(
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  item['name'],
                                                  style: const TextStyle(
                                                    fontSize: 15,
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Text(
                                                      'الكمية: ${item['quantity']}',
                                                      style: const TextStyle(
                                                        fontSize: 13,
                                                        color: ColorManager
                                                            .primaryColor,
                                                      ),
                                                    ),
                                                    if (item['weight'] !=
                                                            null &&
                                                        item['weight'] !=
                                                            0) ...[
                                                      const SizedBox(width: 8),
                                                      Text(
                                                        'الوزن: ${item['weight']}',
                                                        style: const TextStyle(
                                                          fontSize: 13,
                                                          color: ColorManager
                                                              .secondaryColor,
                                                        ),
                                                      ),
                                                    ],
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          DataCell(Padding(
                                            padding: const EdgeInsets.only(
                                                right: 3.0),
                                            child: Row(
                                              children: [
                                                Text((item['price'] as num)
                                                    .roundedPrecisionToString(
                                                        5)),
                                                Text(
                                                  context.currency,
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )),
                                          DataCell(Padding(
                                            padding: const EdgeInsets.only(
                                                right: 10.0),
                                            child: Row(
                                              children: [
                                                Text(
                                                  ((item['price'] *
                                                              item['quantity'])
                                                          as num)
                                                      .roundedPrecisionToString(
                                                          5),
                                                ),
                                                Text(
                                                  context.currency,
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )),
                                        ]);
                                      }).toList(),
                                    ),
                                  );
                                }

                                return Center(
                                  child: Form(
                                    key: formKey,
                                    child: Column(
                                      children: [
                                        Expanded(
                                          child: SingleChildScrollView(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 16.0),
                                            child: Column(
                                              children: [
                                                Column(
                                                  children: [
                                                    if (widget.isReturned)
                                                      Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          Row(
                                                            children: [
                                                              Text(
                                                                context.isEng
                                                                    ? 'Invoice: '
                                                                    : 'فاتورة: ',
                                                                style:
                                                                    const TextStyle(
                                                                        fontSize:
                                                                            16),
                                                              ),
                                                              Text(
                                                                widget.invoiceData
                                                                        ?.invoiceNumber ??
                                                                    '',
                                                                style:
                                                                    const TextStyle(
                                                                  fontSize: 16,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                          Row(
                                                            children: [
                                                              Text(
                                                                context.isEng
                                                                    ? 'Date: '
                                                                    : 'التاريخ: ',
                                                                style:
                                                                    const TextStyle(
                                                                        fontSize:
                                                                            16),
                                                              ),
                                                              Text(
                                                                widget.invoiceData
                                                                        ?.createdAt
                                                                        .toString()
                                                                        .split(
                                                                            ' ')[0] ??
                                                                    '',
                                                                style:
                                                                    const TextStyle(
                                                                  fontSize: 16,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ],
                                                      ),
                                                    AppGaps.gap12,
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Row(
                                                              children: [
                                                                Text(
                                                                  context.isEng
                                                                      ? 'Supplier: '
                                                                      : 'المورد: ',
                                                                  style: const TextStyle(
                                                                      fontSize:
                                                                          16),
                                                                ),
                                                                Text(
                                                                  widget.supplier
                                                                          .name ??
                                                                      '',
                                                                  style:
                                                                      const TextStyle(
                                                                    fontSize:
                                                                        16,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                            AppGaps.gap8,
                                                          ],
                                                        ),
                                                        if (!widget.isReturned)
                                                          Row(
                                                            children: [
                                                              Text(
                                                                context.isEng
                                                                    ? 'Date: '
                                                                    : 'التاريخ: ',
                                                                style:
                                                                    const TextStyle(
                                                                        fontSize:
                                                                            16),
                                                              ),
                                                              Text(
                                                                DateTime.now()
                                                                    .toString()
                                                                    .split(
                                                                        ' ')[0],
                                                                style:
                                                                    const TextStyle(
                                                                  fontSize: 16,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                      ],
                                                    ),
                                                    AppGaps.gap12,
                                                    const Divider(
                                                      thickness: 1,
                                                      color: ColorManager
                                                          .lightFieldColor,
                                                    ),
                                                    AppGaps.gap12,
                                                    Align(
                                                      alignment: context.isEng
                                                          ? Alignment.centerLeft
                                                          : Alignment
                                                              .centerRight,
                                                      child: Text(
                                                        context.isEng
                                                            ? 'Products'
                                                            : 'المنتجات',
                                                        style: const TextStyle(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                      ),
                                                    ),
                                                    AppGaps.gap24,
                                                    widget.isReturned
                                                        ? returnedWidget()
                                                        : invoiceItemsWidget(),
                                                    AppGaps.gap12,
                                                    const Divider(
                                                      thickness: 1,
                                                      color: ColorManager
                                                          .lightFieldColor,
                                                    ),
                                                    const SizedBox(height: 20),
                                                    totalAndTaxWidget(
                                                        info: info),
                                                    AppGaps.gap12,
                                                    if (!widget.isReturned)
                                                      cashPaidField(lang: lang),
                                                    // const SizedBox(height: 20),
                                                    // if (!widget.isReturned)
                                                    //   Column(
                                                    //     children: [
                                                    //       const SizedBox(
                                                    //           height: 15),
                                                    //       TextFieldWidget(
                                                    //         controller:
                                                    //             priceCtrl,
                                                    //         label: context.isEng
                                                    //             ? 'Cash Paid'
                                                    //             : 'المدفوع نقدي',
                                                    //         textInputType:
                                                    //             TextInputType
                                                    //                 .number,
                                                    //         onChanged: (val) {
                                                    //           if (val == null ||
                                                    //               val == '0' ||
                                                    //               val == '') {
                                                    //             val = '0.0';
                                                    //           }
                                                    //           if (double.tryParse(
                                                    //                       val) ==
                                                    //                   null ||
                                                    //               double.tryParse(
                                                    //                       val)! >
                                                    //                   totalValue) {
                                                    //             setState(() {
                                                    //               invalid =
                                                    //                   true;
                                                    //               showBar(
                                                    //                   context,
                                                    //                   context.isEng
                                                    //                       ? 'Please enter a valid amount'
                                                    //                       : 'برجاء إدخال مبلغ صحيح');
                                                    //             });
                                                    //             return;
                                                    //           }
                                                    //           setState(() {
                                                    //             invalid = false;
                                                    //             double?
                                                    //                 cashVal =
                                                    //                 double
                                                    //                     .tryParse(
                                                    //                         val);
                                                    //
                                                    //             remainingCash =
                                                    //                 totalValue -
                                                    //                     cashVal!;
                                                    //           });
                                                    //         },
                                                    //       ),
                                                    //       const SizedBox(
                                                    //           height: 15),
                                                    //       Align(
                                                    //         alignment: context
                                                    //                 .isEng
                                                    //             ? Alignment
                                                    //                 .centerLeft
                                                    //             : Alignment
                                                    //                 .centerRight,
                                                    //         child: Text(
                                                    //             '${context.isEng ? 'Remaining' : "المتبقي"}: ${remainingCash.roundedPrecisionToString(5)}'),
                                                    //       ),
                                                    //     ],
                                                    //   ),
                                                    // const SizedBox(height: 20),
                                                    // Row(
                                                    //   children: [
                                                    //     Text(
                                                    //         '${context.isEng ? "Total Before Tax" : 'إجمالي قبل الضريبة'}: '),
                                                    //     Text(
                                                    //         '${context.currency} ${totalItemsPrice.roundedPrecisionToString(5)}',
                                                    //         style:
                                                    //             const TextStyle(
                                                    //                 fontFamily:
                                                    //                     'Droid',
                                                    //                 fontSize:
                                                    //                     13)),
                                                    //   ],
                                                    // ),
                                                    // const SizedBox(height: 5),
                                                    // Row(
                                                    //   children: [
                                                    //     Text(context.isEng
                                                    //         ? 'Tax'
                                                    //         : "الضريبة"),
                                                    //     Text(
                                                    //         "${(info == null || info.tax == null ? 15 : info.tax)!.toInt()}%: ",
                                                    //         style:
                                                    //             const TextStyle(
                                                    //                 fontFamily:
                                                    //                     'Droid',
                                                    //                 fontSize:
                                                    //                     13)),
                                                    //     Text(
                                                    //         "RS${taxVal!.roundedPrecisionToString(5)}",
                                                    //         style:
                                                    //             const TextStyle(
                                                    //                 fontFamily:
                                                    //                     'Droid',
                                                    //                 fontSize:
                                                    //                     13)),
                                                    //   ],
                                                    // ),
                                                    // const SizedBox(height: 5),
                                                    // Row(
                                                    //   children: [
                                                    //     Text(
                                                    //         '${context.isEng ? "Total" : 'الإ��مالي'}: '),
                                                    //     Text(
                                                    //         'RS${totalValue.roundedPrecisionToString(5)}',
                                                    //         style:
                                                    //             const TextStyle(
                                                    //                 fontFamily:
                                                    //                     'Droid',
                                                    //                 fontSize:
                                                    //                     13)),
                                                    //   ],
                                                    // ),
                                                    // const SizedBox(height: 50),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.all(20),
                                          child: loading
                                              ? const LoadingWidget()
                                              : SubmitButton(
                                                  width: context.width,
                                                  label: context.isEng
                                                      ? 'New Invoice'
                                                      : "فاتورة جديدة",
                                                  onPressed: loading
                                                      ? null
                                                      : () async {
                                                          if (addedMap
                                                              .isNotEmpty) {
                                                            if (invalid) {
                                                              showBar(
                                                                  context,
                                                                  context.isEng
                                                                      ? 'Please enter a valid amount'
                                                                      : 'برجاء إدخال مبلغ صحيح');
                                                              return;
                                                            }

                                                            if (moreThanQuantity) {
                                                              showBar(
                                                                  context,
                                                                  context.isEng
                                                                      ? 'Please choose a quantity less than or equal to the quantity of the product!'
                                                                      : 'برجاء اختيار كمية مرتجع أقل من أو تساوي كمية المنتج!');
                                                              return;
                                                            } else {
                                                              setState(() {
                                                                loading = true;
                                                              });
                                                              addPurchaseInvoice();
                                                            }
                                                          } else {
                                                            showBar(
                                                                context,
                                                                context.isEng
                                                                    ? 'Please check the invoice details!'
                                                                    : 'برجاء التأكد من بيانات الفاتورة!');
                                                          }
                                                        },
                                                ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        );
                      },
                    );
                  },
                );
              });
        }),
      ),
    );
  }

  Widget cashPaidField({
    required LangProvider lang,
  }) {
    return Column(
      children: [
        const SizedBox(
          height: 15,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: TextFieldWidget(
                controller: priceCtrl,
                withoutEnter: true,
                hint: '0.0',
                label: context.isEng ? "Cash Paid" : 'المدفوع نقدي',
                textInputType: TextInputType.number,
                onChanged: (val) async {
                  await Future.delayed(const Duration(milliseconds: 100));

                  if (val == null ||
                      val.toString().toEnglishDigit() == '0' ||
                      val.toString().toEnglishDigit() == '') {
                    val = '0.0';
                    // priceCtrl.text = '0.0';
                  }

                  if (double.tryParse(val.toString().toEnglishDigit()) ==
                          null ||
                      double.tryParse(val.toString().toEnglishDigit())! >
                          totalValue) {
                    setState(() {
                      invalid = true;
                      showBar(context, 'برجاء إدخال مبلغ صحيح');
                    });
                    return;
                  }

                  setState(() {
                    invalid = false;
                    double? cashVal =
                        double.tryParse(val.toString().toEnglishDigit());

                    remainingCash = totalValue - cashVal!;
                  });
                },
              ),
            ),
            AppGaps.gap12,
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(context.isEng ? 'Remaining' : "المتبقي"),
                Row(
                  children: [
                    Text(
                      remainingCash.roundedPrecisionToString(5),
                      style: const TextStyle(
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      context.currency,
                      style: TextStyle(
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget totalAndTaxWidget({
    required CompanyInfoModel? info,
  }) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
                '${context.isEng ? "Total before VAT" : 'إجمالي قبل الضريبة'} '),
            Row(
              children: [
                Text(
                  (totalItemsPrice).roundedPrecisionToString(5),
                  style: const TextStyle(
                    fontSize: 18,
                  ),
                ),
                Text(
                  context.currency,
                  style: const TextStyle(
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(
          height: 5,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Text(
                  context.isEng ? 'VAT' : 'ضريبة القيمة المضافة',
                ),
                Text(
                  " (${(info == null || info.tax == null ? 15 : info.tax)!.toInt()}%) ",
                  style: const TextStyle(
                    color: ColorManager.primaryColor,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Text(
                  taxVal!.roundedPrecisionToString(5),
                  style: const TextStyle(
                    color: ColorManager.primaryColor,
                    fontSize: 18,
                  ),
                ),
                Text(
                  context.currency,
                  style: const TextStyle(
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(
          height: 5,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('${context.isEng ? "Total" : 'الإجمالي'} '),
            Row(
              children: [
                Text(
                  totalValue.roundedPrecisionToString(5),
                  style: const TextStyle(
                    fontSize: 18,
                  ),
                ),
                Text(
                  context.currency,
                  style: const TextStyle(
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  void addPurchaseInvoice() async {
    try {
      final repo = Provider.of<RepositoryProvider>(context, listen: false);
      final quantatiesData =
          Provider.of<QuantitiesProvider>(context, listen: false);
      final user =
          Provider.of<UserProvider>(context, listen: false).activeUser!;

      var info =
          Provider.of<CompanyInfoProvider>(context, listen: false).companyInfo;

      List<RepositoryModel> receriverRepo = repo.repositories
          .where((element) => element.id == widget.senderRepo)
          .toList();

      Map<String, dynamic>? products = {};

      if (receriverRepo.isNotEmpty &&
          receriverRepo[0].products != null &&
          receriverRepo[0].products!.isNotEmpty &&
          receriverRepo[0].products!.isNotEmpty) {
        products = receriverRepo[0].products;
      }

      await Future.wait([
        _modifyQuantity(
            receiverRepo: receriverRepo,
            products: products!,
            quantitiesData: quantatiesData),
      ]);

      await Future.wait([
        addPayment(
          user: user,
        ),
      ]);

      await Future.wait([
        addOperation(
          user: user,
        ),
      ]);

      await Future.wait([
        _modifyRepo(
          receiverRepo: receriverRepo,
          products: products,
        ),
      ]);

      await Future.wait([
        _addInvoice(
          user: user,
          info: info,
        ),
      ]);

      if (mounted) {
        setState(() {
          scrollingPurchaseInvoices = false;
        });
        showDoneInvoiceMessageAndClose(context);
      }
    } catch (e) {
      debugPrint('addPurchaseError: $e');

      if (mounted) {
        showInvoiceErrorMessage(context);
      }
    }
  }

  Future<void> _modifyRepo(
      {required List<RepositoryModel> receiverRepo,
      required Map<String, dynamic> products}) async {
    await Provider.of<RepositoryProvider>(context, listen: false)
        .editRepository(
            widget.senderRepo ?? '',
            RepositoryModel.fromJson({
              "name": receiverRepo.isNotEmpty ? receiverRepo[0].name! : '',
              "location":
                  receiverRepo.isNotEmpty ? receiverRepo[0].location! : '',
              "createdAt":
                  receiverRepo.isNotEmpty ? receiverRepo[0].createdAt! : '',
              "products": jsonEncode(products)
            }));
  }

  Future<void> _modifyQuantity({
    required Map<String, dynamic> products,
    required List<RepositoryModel> receiverRepo,
    required QuantitiesProvider quantitiesData,
  }) async {
    List<QuantityModel> receiverQuantitiesValue = [];
    int oldReceiverValue = 0;

    addedMap.forEach((key, value) async {
      for (var element in addedItems) {
        if (!products.containsKey(key)) {
          if (element['id'] == key) {
            products.addAll({key: element['price']});
          }
        }
      }

      receiverQuantitiesValue = quantitiesData.quantities
          .where((element) => element.productId == key)
          .toList();

      debugPrint(
          'receiverQuatitiesValue: ${receiverQuantitiesValue.map((e) => e.toJson())}');

      if (receiverQuantitiesValue.isNotEmpty) {
        receiverQuantitiesValue[0].quantities!.forEach((key, val) {
          if (receiverRepo.isNotEmpty && key == receiverRepo[0].id!) {
            setState(() {
              oldReceiverValue = val;
              debugPrint('oldReceiverValue2wwewewe $oldReceiverValue');
            });
          }
        });
      }

      if (widget.isReturned) {
        debugPrint('oldSenderValue: $oldReceiverValue');
        debugPrint('value: ${oldReceiverValue - value}');
        Provider.of<QuantitiesProvider>(context, listen: false).modifyQuantity(
            productId: key,
            storeId: receiverRepo.isNotEmpty ? receiverRepo[0].id! : '',
            quantity: oldReceiverValue - value);
      } else {
        debugPrint('value: ${oldReceiverValue + value}');

        await Provider.of<QuantitiesProvider>(context, listen: false)
            .modifyQuantity(
                productId: key,
                storeId: receiverRepo[0].id,
                quantity: oldReceiverValue + value);
      }
    });
  }

  Future<void> addPayment({required UserModel user}) async {
    if (!widget.isReturned &&
        double.tryParse(priceCtrl.text.toEnglishDigit()) != 0 &&
        double.tryParse(priceCtrl.text.toEnglishDigit()) != null) {
      await Provider.of<SupplierPaymentsProvider>(context, listen: false)
          .addPayment(SupplierPaymentModel.fromJson({
        'value': double.tryParse(priceCtrl.text.toEnglishDigit()),
        'source': 'purchase',
        'userId': user.uid,
        'supplierId': widget.supplier.id,
        'supplierName': widget.supplier?.name,
        'createdAt': DateTime.now().toString(),
      }));
    }
  }

  Future<void> addOperation({required UserModel user}) async {
    if (widget.isReturned) {
      await Provider.of<CashierProvider>(context, listen: false)
          .createOperation(OperationModel.fromJson({
        "isIncome": true,
        "value": double.tryParse(priceCtrl.text.toEnglishDigit()),
        "source": "purchase-return",
        "userId": user.uid,
        "opTime": DateTime.now().toIso8601String(),
      }));
    }
  }

  Future<void> _addInvoice({required UserModel user, required info}) async {
    debugPrint('addingInvoice');
    await Provider.of<InvoicesProvider>(context, listen: false)
        .addInvoice(InvoiceModel.fromJson({
      'customerName': widget.supplier.name,
      'createdAt': DateTime.now().toString(),
      'products': jsonEncode(widget.isReturned ? items : addedItems),
      'totalPrice': totalValue,
      'taxValPercent': info == null || info.tax == null ? 15 : info.tax!,
      'paidCash': widget.isReturned
          ? double.tryParse(widget.invoiceData?.paidCash.toString() ?? '0')
          : double.tryParse(priceCtrl.text.toEnglishDigit()),
      'isReturned': widget.isReturned,
      'isSales': false,
      'totalWithoutTax': totalItemsPrice,
      'storeId': widget.senderRepo,
      'customerId': widget.supplier.id,
      'customerVatNumber': '',
      'invoiceNumber': invoiceNumber.toStringAsFixed(0),
      'mainInvoiceId': widget.isReturned
          ? (widget.invoiceData?.invoiceNumber ?? widget.invoiceData?.invoiceId)
          : '',
      'mandobId': user.uid,
      'invoiceType': widget.isReturned
          ? widget.invoiceData?.invoiceType
          : double.tryParse(priceCtrl.text.toEnglishDigit()) == totalValue
              ? 'cash'
              : 'later',
    }));
  }
}
