import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:intl/intl.dart';
import 'package:mandob/appwrite_db/appwrite.dart';
import 'package:mandob/appwrite_db/db_consts.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/company_info_model.dart';
import 'package:mandob/models/invoice_model.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/extensions.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:screenshot/screenshot.dart';

import 'en_pdf_invoice_print.dart';

Future printPdfInvoice(
  InvoiceModel receiptData,
  bool isReturned,
  bool isPurchase,
  CompanyInfoModel? info,
  BuildContext context, {
  bool isEng = false,
}) async {
  final doc = pw.Document();

  final font = await rootBundle.load("assets/fonts/cairo/Cairo-Regular.ttf");
  final fontBold = await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf");

  final ttf = pw.Font.ttf(font);
  final ttfBold = pw.Font.ttf(fontBold);

  // final ttf = await PdfGoogleFonts.balooBhaijaan2Regular();
  //
  // final ttfBold = await PdfGoogleFonts.balooBhaijaan2Bold();

  var taxVal = (info!.tax! / 100) * receiptData.totalWithoutTax!;

  if (isEng) {
    await addEnPdfPage(
        doc, ttf, ttfBold, receiptData, isReturned, isPurchase, info, taxVal);
  } else {
    await addPdfPage(
        doc, ttf, ttfBold, receiptData, isReturned, isPurchase, info, taxVal);
  }

  await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) => Stack(
            children: [
              SizedBox(
                height: 600,
                child: HookBuilder(builder: (context) {
                  final screenshotController = useState(ScreenshotController());

                  return Stack(
                    children: [
                      PdfPreview(
                        previewPageMargin: const EdgeInsets.all(0),
                        dynamicLayout: false,
                        allowPrinting: true,
                        canChangeOrientation: false,
                        canChangePageFormat: false,
                        canDebug: false,
                        initialPageFormat: PdfPageFormat.a4,
                        build: (format) => doc.save(),
                        useActions: true,
                      ),
                    ],
                  );
                }),
              ),

              //close circle avatar
              Positioned(
                top: 0,
                right: context.isEng ? 0 : null,
                left: context.isEng ? null : 0,
                child: IconButton(
                  icon: const CircleAvatar(
                      radius: 18,
                      backgroundColor: ColorManager.lightFieldColor,
                      child: Icon(Icons.close)),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ],
          ));
}

pw.Widget getList(InvoiceModel receiptData, ttf) {
  pw.Widget cell(String text, bool header) => pw.Text(
        text,
        maxLines: 1,
        style: pw.TextStyle(
            fontSize: header ? 3.5 * PdfPageFormat.mm : 2.9 * PdfPageFormat.mm,
            fontWeight: header ? pw.FontWeight.bold : pw.FontWeight.normal,
            font: ttf),
        textDirection: pw.TextDirection.rtl,
      );

  final _productsWidget = pw.ListView(children: [
    pw.Table(columnWidths: {
      0: const pw.FlexColumnWidth(0.41 * PdfPageFormat.mm),
      1: const pw.FlexColumnWidth(0.3 * PdfPageFormat.mm),
      2: const pw.FlexColumnWidth(0.27 * PdfPageFormat.mm),
      3: const pw.FlexColumnWidth(0.27 * PdfPageFormat.mm),
      4: const pw.FlexColumnWidth(0.25 * PdfPageFormat.mm),
      5: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
    }, children: [
      pw.TableRow(children: [
        cell("الاجمالى", true),
        cell("سعر", true),
        pw.Center(child: cell("وزن", true)),
        pw.Center(child: cell("كمية", true)),
        cell("وحدة", true),
        cell("الصنف", true),
      ]),
    ]),
    pw.Divider(
        thickness: 0.5 * PdfPageFormat.mm, height: 0.5 * PdfPageFormat.mm),
    pw.Table(
        columnWidths: {
          0: const pw.FlexColumnWidth(0.41 * PdfPageFormat.mm),
          1: const pw.FlexColumnWidth(0.3 * PdfPageFormat.mm),
          2: const pw.FlexColumnWidth(0.27 * PdfPageFormat.mm),
          3: const pw.FlexColumnWidth(0.27 * PdfPageFormat.mm),
          4: const pw.FlexColumnWidth(0.25 * PdfPageFormat.mm),
          5: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
        },
        children: receiptData.products!.map((product) {
          final num total = product!["price"] * product["quantity"];
          return pw.TableRow(children: [
            cell(total.roundedPrecisionToString(5), false),
            cell((product["price"] as num).roundedPrecisionToString(5), false),
            pw.Center(
                child: cell(
                    product["weight"] == null || product["weight"] == 0
                        ? "-"
                        : product["weight"].toString(),
                    false)),
            pw.Center(child: cell(product["quantity"].toString(), false)),
            cell(product['type'].toString(), false),
            cell(product["name"].toString(), false),
          ]);
        }).toList())
  ]);
  return _productsWidget;
}

addPdfPage(doc, ttf, ttfBold, InvoiceModel receiptData, bool isReturned,
    bool isPurchase, CompanyInfoModel? info, taxVal) {
  final isThamraDB = AppwriteDB.databaseId == DbConsts.thamraDB;

  doc.addPage(pw.Page(
      pageTheme: pw.PageTheme(
          pageFormat: PdfPageFormat.a4,
          theme: pw.ThemeData(bulletStyle: pw.TextStyle(font: ttf))),
      // pageFormat: PdfPageFormat.roll80,
      build: (pw.Context context) {
        return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            mainAxisSize: pw.MainAxisSize.max,
            children: [
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.BarcodeWidget(
                        width: 16.5 * PdfPageFormat.mm,
                        height: 16.5 * PdfPageFormat.mm,
                        data: generateQr(receiptData, info!),
                        barcode: pw.Barcode.qrCode()),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.end,
                        mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                        mainAxisSize: pw.MainAxisSize.max,
                        children: [
                          pw.Text(info.companyName.toString(),
                              style: pw.TextStyle(
                                  fontSize: 2.5 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttfBold),
                              textDirection: pw.TextDirection.rtl,
                              textAlign: pw.TextAlign.right),
                          pw.SizedBox(height: 5),
                          pw.Text('VAT # ${info.vatNumber}',
                              style: pw.TextStyle(
                                  fontSize: 3 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttfBold),
                              textDirection: pw.TextDirection.rtl,
                              textAlign: pw.TextAlign.left),
                          pw.SizedBox(height: 5),
                          pw.Row(
                              mainAxisAlignment: pw.MainAxisAlignment.end,
                              children: [
                                pw.Text(
                                  info.address == null
                                      ? 'سوق الخضار المركزي أمام بدر الدين'
                                      : info.address!,
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttf),
                                  textDirection: pw.TextDirection.rtl,
                                ),
                                pw.SizedBox(width: 5),
                                pw.Text(
                                  'العنوان : ',
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttf),
                                  textDirection: pw.TextDirection.rtl,
                                ),
                              ]),
                        ]),
                  ]),
              pw.SizedBox(height: 10),
              pw.Divider(height: 5),
              pw.Center(
                  child: pw.Column(children: [
                if (isPurchase == false && isReturned == true) ...[
                  pw.Text(
                    'إشعار دائن للفاتورة الضريبية المبسطة',
                    style: pw.TextStyle(
                        fontSize: 3 * PdfPageFormat.mm,
                        fontWeight: pw.FontWeight.bold,
                        font: ttfBold),
                    textDirection: pw.TextDirection.rtl,
                  ),
                ] else if (isPurchase == true && isReturned == true) ...[
                  pw.Text(
                    'إشعار مدين للفاتورة الضريبية المبسطة',
                    style: pw.TextStyle(
                        fontSize: 3 * PdfPageFormat.mm,
                        fontWeight: pw.FontWeight.bold,
                        font: ttfBold),
                    textDirection: pw.TextDirection.rtl,
                  ),
                ] else ...[
                  pw.Text(
                    'فاتورة ضريبية مبسطة',
                    style: pw.TextStyle(
                        fontSize: 3 * PdfPageFormat.mm,
                        fontWeight: pw.FontWeight.bold,
                        font: ttfBold),
                    textDirection: pw.TextDirection.rtl,
                  ),
                ]
              ])),
              pw.SizedBox(height: 7),
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    if (isReturned) ...[
                      pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.end,
                          children: [
                            pw.Text(
                              receiptData.invoiceNumber == null
                                  ? receiptData.invoiceId
                                      .toString()
                                      .substring(0, 7)
                                  : receiptData.invoiceNumber!,
                              style: pw.TextStyle(
                                  fontSize: 3 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttf),
                              textDirection: pw.TextDirection.ltr,
                            ),
                            pw.SizedBox(width: 5),
                            pw.Text(
                              'رقم الإشعار : ',
                              style: pw.TextStyle(
                                  fontSize: 3.5 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttf),
                              textDirection: pw.TextDirection.rtl,
                            ),
                          ]),
                      pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.end,
                          children: [
                            pw.Text(
                              receiptData.mainInvoiceId!,
                              style: pw.TextStyle(
                                  fontSize: 3 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttf),
                              textDirection: pw.TextDirection.ltr,
                            ),
                            pw.SizedBox(width: 5),
                            pw.Text(
                              'رقم الفاتورة : ',
                              style: pw.TextStyle(
                                  fontSize: 3.5 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttf),
                              textDirection: pw.TextDirection.rtl,
                            ),
                          ]),
                    ] else ...[
                      pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.end,
                          children: [
                            pw.Text(
                              receiptData.invoiceNumber == null
                                  ? receiptData.invoiceId
                                      .toString()
                                      .substring(0, 7)
                                  : receiptData.invoiceNumber!,
                              style: pw.TextStyle(
                                  fontSize: 3 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttf),
                              textDirection: pw.TextDirection.ltr,
                            ),
                            pw.SizedBox(width: 5),
                            pw.Text(
                              'رقم الفاتورة : ',
                              style: pw.TextStyle(
                                  fontSize: 3.5 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttf),
                              textDirection: pw.TextDirection.rtl,
                            ),
                          ]),
                    ],
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.end,
                        children: [
                          pw.Text(
                            DateFormat("dd-MM-yyyy hh:mm:ss")
                                .format(DateTime.parse(receiptData.createdAt!)),
                            style: pw.TextStyle(
                                fontSize: 3.5 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttf),
                            textDirection: pw.TextDirection.rtl,
                          ),
                          pw.SizedBox(width: 5),
                          pw.Text(
                            'التاريخ : ',
                            style: pw.TextStyle(
                                fontSize: 3.5 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttf),
                            textDirection: pw.TextDirection.rtl,
                          ),
                        ]),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.end,
                        children: [
                          pw.Text(
                            receiptData.customerName.toString(),
                            style: pw.TextStyle(
                                fontSize: 3.5 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttf),
                            textDirection: pw.TextDirection.rtl,
                          ),
                          pw.SizedBox(width: 5),
                          pw.Text(
                            'اسم العميل : ',
                            style: pw.TextStyle(
                                fontSize: 3.5 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttf),
                            textDirection: pw.TextDirection.rtl,
                          ),
                        ]),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.end,
                        children: [
                          pw.Text(
                            '#${receiptData.customerVatNumber == null ? '' : receiptData.customerVatNumber!}',
                            style: pw.TextStyle(
                                fontSize: 3.5 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttf),
                            textDirection: pw.TextDirection.rtl,
                          ),
                          pw.SizedBox(width: 5),
                          pw.Text(
                            'الرقم الضريبي : ',
                            style: pw.TextStyle(
                                fontSize: 3.5 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttf),
                            textDirection: pw.TextDirection.rtl,
                          ),
                        ]),
                    pw.SizedBox(height: 7),
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.end,
                        children: [
                          getList(receiptData, ttf),
                          pw.Divider(height: 5),
                          pw.Row(
                              mainAxisAlignment:
                                  pw.MainAxisAlignment.spaceBetween,
                              children: [
                                pw.SizedBox(height: 15),
                                pw.Text(
                                  'رس',
                                  style: pw.TextStyle(
                                      fontSize: 3 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                  textDirection: pw.TextDirection.rtl,
                                ),
                                pw.SizedBox(width: 10),
                                pw.Text(
                                  receiptData.totalWithoutTax!
                                      .roundedPrecisionToString(5),
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      font: ttfBold),
                                ),
                                pw.Spacer(),
                                pw.Text(
                                  'المبلغ قبل الضريبة',
                                  style: pw.TextStyle(
                                      fontSize: 3 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                  textDirection: pw.TextDirection.rtl,
                                ),
                              ]),
                          pw.Row(
                              mainAxisAlignment:
                                  pw.MainAxisAlignment.spaceBetween,
                              children: [
                                pw.SizedBox(height: 15),
                                pw.Text(
                                  'رس',
                                  style: pw.TextStyle(
                                      fontSize: 3 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                  textDirection: pw.TextDirection.rtl,
                                ),
                                pw.SizedBox(width: 10),
                                pw.Text(
                                  (taxVal as num).roundedPrecisionToString(5),
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      // fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                ),
                                pw.Spacer(),
                                pw.Row(children: [
                                  pw.Text(
                                    '${receiptData.taxValPercent!.toInt()}%',
                                    style: pw.TextStyle(
                                        fontSize: 3.5 * PdfPageFormat.mm,
                                        fontWeight: pw.FontWeight.bold,
                                        font: ttfBold),
                                    textDirection: pw.TextDirection.rtl,
                                  ),
                                  pw.SizedBox(width: 2),
                                  pw.Text(
                                    'نسبة الضريبة',
                                    style: pw.TextStyle(
                                        fontSize: 3 * PdfPageFormat.mm,
                                        fontWeight: pw.FontWeight.bold,
                                        font: ttfBold),
                                    textDirection: pw.TextDirection.rtl,
                                  ),
                                ]),
                              ]),
                          if (!isPurchase) ...[
                            pw.Row(
                                // mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                children: [
                                  pw.Text(
                                    'رس',
                                    style: pw.TextStyle(
                                        fontSize: 3 * PdfPageFormat.mm,
                                        fontWeight: pw.FontWeight.bold,
                                        font: ttfBold),
                                    textDirection: pw.TextDirection.rtl,
                                  ),
                                  pw.SizedBox(width: 2 * PdfPageFormat.mm),
                                  pw.Text(
                                    receiptData.discount!
                                        .roundedPrecisionToString(5),
                                    style: pw.TextStyle(
                                        fontSize: 3.5 * PdfPageFormat.mm,
                                        font: ttfBold),
                                  ),
                                  pw.Spacer(),
                                  pw.Text(
                                    'الخصم',
                                    style: pw.TextStyle(
                                        fontSize: 3 * PdfPageFormat.mm,
                                        fontWeight: pw.FontWeight.bold,
                                        font: ttfBold),
                                    textDirection: pw.TextDirection.rtl,
                                  ),
                                ]),
                          ],
                          pw.Row(
                              // mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                              children: [
                                pw.SizedBox(height: 15),
                                pw.Text(
                                  'رس',
                                  style: pw.TextStyle(
                                      fontSize: 3 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                  textDirection: pw.TextDirection.rtl,
                                ),
                                pw.SizedBox(width: 2 * PdfPageFormat.mm),
                                pw.Text(
                                  receiptData.totalPrice!
                                      .roundedPrecisionToString(5),
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      font: ttfBold),
                                ),
                                pw.Spacer(),
                                pw.Text(
                                  'المبلغ بعد الضريبة',
                                  style: pw.TextStyle(
                                      fontSize: 3 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                  textDirection: pw.TextDirection.rtl,
                                ),
                              ]),
                          pw.SizedBox(height: 15),
                          pw.Divider(
                              color: PdfColor.fromHex("#DDDEDE"),
                              height: 3 * PdfPageFormat.mm,
                              thickness: 1 * PdfPageFormat.mm),
                          pw.Padding(
                            padding: const pw.EdgeInsets.only(right: 5),
                            child: pw.Row(
                                mainAxisAlignment: pw.MainAxisAlignment.end,
                                children: [
                                  pw.Text(
                                    'رس',
                                    style: pw.TextStyle(
                                        fontSize: 3 * PdfPageFormat.mm,
                                        fontWeight: pw.FontWeight.bold,
                                        font: ttfBold),
                                    textDirection: pw.TextDirection.rtl,
                                  ),
                                  pw.SizedBox(width: 5),
                                  pw.Text(
                                    receiptData.paidCash!
                                        .roundedPrecisionToString(5),
                                    style: pw.TextStyle(
                                        fontSize: 3.5 * PdfPageFormat.mm,
                                        fontWeight: pw.FontWeight.bold,
                                        font: ttfBold),
                                  ),
                                  pw.SizedBox(width: 2),
                                  pw.Text(
                                    'مدفوع نقدي: ',
                                    style: pw.TextStyle(
                                        fontSize: 3.5 * PdfPageFormat.mm,
                                        fontWeight: pw.FontWeight.bold,
                                        font: ttfBold),
                                    textAlign: pw.TextAlign.right,
                                    textDirection: pw.TextDirection.rtl,
                                  ),
                                ]),
                          ),
                          if (receiptData.totalPrice !=
                              receiptData.paidCash) ...[
                            pw.Padding(
                              padding: const pw.EdgeInsets.only(right: 5),
                              child: pw.Row(
                                  mainAxisAlignment: pw.MainAxisAlignment.end,
                                  children: [
                                    pw.Text(
                                      'رس',
                                      style: pw.TextStyle(
                                          fontSize: 3 * PdfPageFormat.mm,
                                          fontWeight: pw.FontWeight.bold,
                                          font: ttfBold),
                                      textDirection: pw.TextDirection.rtl,
                                    ),
                                    pw.SizedBox(width: 5),
                                    pw.Text(
                                      (receiptData.totalPrice! -
                                              receiptData.paidCash!)
                                          .roundedPrecisionToString(5),
                                      style: pw.TextStyle(
                                          fontSize: 3.5 * PdfPageFormat.mm,
                                          fontWeight: pw.FontWeight.bold,
                                          font: ttfBold),
                                    ),
                                    pw.SizedBox(width: 10),
                                    pw.Text(
                                      'آجل : ',
                                      style: pw.TextStyle(
                                          fontSize: 3.5 * PdfPageFormat.mm,
                                          fontWeight: pw.FontWeight.bold,
                                          font: ttfBold),
                                      textDirection: pw.TextDirection.rtl,
                                    ),
                                  ]),
                            ),
                            pw.Divider(
                                height: 1.5 * PdfPageFormat.mm,
                                thickness: 1,
                                indent: 3 * PdfPageFormat.mm,
                                endIndent: 3 * PdfPageFormat.mm),
                          ],
                          //  if (receiptData.invoiceType != null) {
                          //     await SunmiPrinter.printText(
                          //         'نوع الفاتورة: ${receiptData.invoiceType.toString() == 'cash' ? "نقدا" : "آجل"}',
                          //         style: SunmiStyle(
                          //             fontSize: SunmiFontSize.MD,
                          //             align: SunmiPrintAlign.RIGHT,
                          //             bold: true));
                          //   }

                          if (receiptData.invoiceType != null) ...[
                            pw.Padding(
                              padding: const pw.EdgeInsets.only(right: 5),
                              child: pw.Row(
                                  mainAxisAlignment: pw.MainAxisAlignment.end,
                                  children: [
                                    pw.Text(
                                      receiptData.invoiceType.toString() ==
                                              'cash'
                                          ? "نقدا"
                                          : "آجل",
                                      style: pw.TextStyle(
                                          fontSize: 3.5 * PdfPageFormat.mm,
                                          fontWeight: pw.FontWeight.bold,
                                          font: ttfBold),
                                      textDirection: pw.TextDirection.rtl,
                                    ),
                                    pw.SizedBox(width: 2),
                                    pw.Text(
                                      'نوع الفاتورة: ',
                                      style: pw.TextStyle(
                                          fontSize: 3.5 * PdfPageFormat.mm,
                                          fontWeight: pw.FontWeight.bold,
                                          font: ttfBold),
                                      textDirection: pw.TextDirection.rtl,
                                    ),
                                  ]),
                            ),
                          ],

                          if (!isPurchase) ...[
                            if (info.bankName != null &&
                                info.bankNumber != null) ...[
                              pw.Divider(
                                  color: PdfColor.fromHex("#DDDEDE"),
                                  height: 3 * PdfPageFormat.mm,
                                  thickness: 1 * PdfPageFormat.mm),
                              pw.SizedBox(height: 10),
                            ],
                            // if (isThamraDB) {
                            // await SunmiPrinter.printText(
                            // "‏نشكر لكم تعاملكم وتعاونكم\n"
                            // "‏وللملاحظات والاستفسارات التواصل مع خدمة العملاء\n"
                            // "+************",
                            // style: SunmiStyle(
                            // fontSize: SunmiFontSize.MD,
                            // align: SunmiPrintAlign.CENTER,
                            // bold: true));
                            // }

                            if (isThamraDB) ...[
                              pw.SizedBox(height: 10),
                              pw.Center(
                                  child: pw.Text(
                                      "‏نشكر لكم تعاملكم وتعاونكم\n"
                                      "‏وللملاحظات والاستفسارات التواصل مع خدمة العملاء\n"
                                      "+************",
                                      style: pw.TextStyle(
                                          fontSize: 2.8 * PdfPageFormat.mm,
                                          fontWeight: pw.FontWeight.bold,
                                          font: ttfBold),
                                      textDirection: pw.TextDirection.rtl,
                                      textAlign: pw.TextAlign.center)),
                            ],

                            if (info.bankName != null &&
                                info.bankNumber != null &&
                                !isThamraDB) ...[
                              pw.SizedBox(height: 10),
                              pw.Center(
                                  child: pw.Text(
                                      """نشكر لكم تعاملكم مع ${info.companyName} ويسرنا تعاونكم 
كما يمكنكم السداد على حسابنا في  ${info.bankName} رقم""",
                                      style: pw.TextStyle(
                                          fontSize: 2.8 * PdfPageFormat.mm,
                                          fontWeight: pw.FontWeight.bold,
                                          font: ttfBold),
                                      textDirection: pw.TextDirection.rtl,
                                      textAlign: pw.TextAlign.center)),
                              pw.Center(
                                  child: pw.Text('${info.bankNumber}',
                                      style: pw.TextStyle(
                                          fontSize: 2.8 * PdfPageFormat.mm,
                                          fontWeight: pw.FontWeight.bold,
                                          font: ttfBold),
                                      textDirection: pw.TextDirection.rtl,
                                      textAlign: pw.TextAlign.center)),
                            ],
                          ],
                        ]),
                  ]),
            ]);
      }));
}

generateQr(InvoiceModel receiptData, CompanyInfoModel info) {
  BytesBuilder bytesBuilder = BytesBuilder();

  // var taxVal = (info.tax! / 100) * receiptData.totalPrice!;
  var taxVal = receiptData.totalPrice! - receiptData.totalWithoutTax!;
  var taxNumber = taxVal / receiptData.totalWithoutTax!;
  var taxPercent = taxNumber * 100;

  //Seller
  bytesBuilder.addByte(1);
  List<int> sellerNameBytes =
      utf8.encode(info.companyName.toString()); //Seller name
  bytesBuilder.addByte(sellerNameBytes.length);
  bytesBuilder.add(sellerNameBytes);

  //Vat
  bytesBuilder.addByte(2);
  List<int> vatBytes = utf8.encode(info.vatNumber.toString());
  bytesBuilder.addByte(vatBytes.length);
  bytesBuilder.add(vatBytes);

  //Date
  bytesBuilder.addByte(3);
  List<int> dateBytes =
      utf8.encode(DateTime.parse(receiptData.createdAt!).toIso8601String());
  bytesBuilder.addByte(dateBytes.length);
  bytesBuilder.add(dateBytes);

  //Total
  bytesBuilder.addByte(4);
  List<int> totalPrice =
      utf8.encode(receiptData.totalPrice!.roundedPrecisionToString(5));
  bytesBuilder.addByte(totalPrice.length);
  bytesBuilder.add(totalPrice);

  //Tax
  bytesBuilder.addByte(5);
  List<int> taxBytes = utf8.encode(taxVal.roundedPrecisionToString(5));
  bytesBuilder.addByte(taxBytes.length);
  bytesBuilder.add(taxBytes);

  Uint8List qrCodeAsBytes = bytesBuilder.toBytes();

  const Base64Encoder b64Encoder = Base64Encoder();

  return b64Encoder.convert(qrCodeAsBytes);
}
