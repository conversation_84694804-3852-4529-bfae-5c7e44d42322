import 'dart:io';

import 'package:barcode_widget/barcode_widget.dart';
import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/company_info_model.dart';
import 'package:mandob/models/invoice_model.dart';
import 'package:mandob/pages/receipt_page/components/print_invoices/print_pdf_invoice.dart';
import 'package:mandob/services/print_service.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/extensions.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:screenshot/screenshot.dart';
import 'package:xr_helper/xr_helper.dart';

const _fontFamily = 'CairoBold';

const _baseTextStyle = TextStyle(
  color: Colors.black,
  fontSize: 18,
  fontFamily: _fontFamily,
  fontWeight: FontWeight.bold,
);

String currency(bool isEng) => isEng ? 'SAR' : 'ريال';

Future<void> printThermalInvoice(
  BuildContext context, {
  required Widget printedWidget,
  required ScreenshotController screenshotController,
}) async {
  // var testPrint = PrintInvoiceService();

  final image = await screenshotController.captureFromLongWidget(printedWidget,
      constraints: const BoxConstraints(
        maxWidth: 280,
      ));

  final path = await saveImage(image);

  context.back(path);

  showModalBottomSheet(
    context: context,
    builder: (context) {
      return Dialog(
        child: Image.file(
          File(path),
          fit: BoxFit.fill,
          // height: 300,
        ),
      );
    },
  );

  // await testPrint.printImageInvoice(path);

  showBar(
    context,
    context.isEng ? 'Printed Successfully' : 'تم الطباعة بنجاح',
    backgroundColor: ColorManager.primaryColor,
  );
}

class ThermalInvoicePrintWidget extends StatelessWidget {
  final InvoiceModel receiptData;
  final bool isReturned;
  final bool isPurchase;
  final CompanyInfoModel? info;
  final BuildContext context;
  final bool isEng;

  const ThermalInvoicePrintWidget({
    super.key,
    required this.receiptData,
    required this.isReturned,
    required this.isPurchase,
    required this.info,
    required this.context,
    this.isEng = false,
  });

  @override
  Widget build(BuildContext _) {
    return Directionality(
      textDirection: isEng ? TextDirection.ltr : TextDirection.rtl,
      child: Container(
        width: 280,
        height: 750,
        color: Colors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            _TopSection(
              receiptData: receiptData,
              info: info,
              isEng: isEng,
            ),
            const Divider(
              thickness: 2,
              color: Colors.black,
            ).paddingOnly(top: 4, bottom: 8),
            _InvoiceCustomerDetails(
              receiptData: receiptData,
              isPurchase: isPurchase,
              isReturned: isReturned,
              isEng: isEng,
            ),
            const SizedBox(height: 20),
            _ProductsWidget(
              receiptData: receiptData,
              context: context,
              isEng: isEng,
            ),
            const Divider(
              thickness: 2,
              color: Colors.black,
            ).paddingOnly(top: 4, bottom: 8),
            _BottomSection(
              receiptData: receiptData,
              isEng: isEng,
            ),
          ],
        ),
      ),
    );
  }
}

class _TopSection extends StatelessWidget {
  final InvoiceModel receiptData;
  final CompanyInfoModel? info;
  final bool isEng;

  const _TopSection({
    required this.receiptData,
    required this.info,
    required this.isEng,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  info?.companyName ?? (isEng ? 'Company Name' : 'اسم الشركة'),
                  style: _baseTextStyle.copyWith(fontWeight: FontWeight.bold),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 5),
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  info?.address ?? (isEng ? 'Address' : 'العنوان'),
                  style: _baseTextStyle.copyWith(fontSize: 12),
                ),
              ),
              const SizedBox(height: 12),
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  'VAT # ${info?.vatNumber ?? ''}',
                  style: _baseTextStyle,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 20),
        BarcodeWidget(
          width: 75,
          height: 75,
          data: generateQr(receiptData, info!),
          barcode: Barcode.qrCode(),
        ),
      ],
    );
  }
}

class _InvoiceCustomerDetails extends StatelessWidget {
  final InvoiceModel receiptData;
  final bool isPurchase;
  final bool isReturned;
  final bool isEng;

  const _InvoiceCustomerDetails({
    required this.receiptData,
    required this.isPurchase,
    required this.isReturned,
    required this.isEng,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Center(
          child: Text(
            isPurchase == false && isReturned == true
                ? (isEng
                    ? 'Credit Note for Simplified Tax Invoice'
                    : 'إشعار دائن للفاتورة الضريبية المبسطة')
                : isPurchase == true && isReturned == true
                    ? (isEng
                        ? 'Debit Note for Simplified Tax Invoice'
                        : 'إشعار مدين للفاتورة الضريبية المبسطة')
                    : (isEng
                        ? 'Simplified Tax Invoice'
                        : 'فاتورة ضريبية مبسطة'),
            style: _baseTextStyle.copyWith(
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
        const SizedBox(height: 16),
        Text(
          (isEng ? 'Invoice Number: ' : 'رقم الفاتورة: ') +
              (receiptData.invoiceNumber ??
                  receiptData.invoiceId.toString().substring(0, 7)),
          style: _baseTextStyle,
        ),
        const SizedBox(height: 5),
        Text(
          (isPurchase
                  ? (isEng ? 'Supplier Name: ' : 'اسم المورد: ')
                  : (isEng ? 'Customer Name: ' : 'اسم العميل: ')) +
              (receiptData.customerName ?? ''),
          style: _baseTextStyle,
        ),
        if (receiptData.customerVatNumber != null &&
            receiptData.customerVatNumber!.isNotEmpty) ...[
          const SizedBox(height: 5),
          Text(
            (isEng ? 'Customer VAT Number: ' : 'الرقم الضريبي: ') +
                (receiptData.customerVatNumber ?? ''),
            style: _baseTextStyle,
          ),
        ],
      ],
    );
  }
}

class _ProductsWidget extends StatelessWidget {
  final InvoiceModel receiptData;
  final BuildContext context;
  final bool isEng;

  const _ProductsWidget({
    required this.receiptData,
    required this.context,
    this.isEng = false,
  });

  @override
  Widget build(BuildContext _) {
    return Table(
      columnWidths: const {
        0: FlexColumnWidth(2),
        1: FlexColumnWidth(1),
        2: FlexColumnWidth(1),
        3: FlexColumnWidth(1),
        4: FlexColumnWidth(1),
      },
      children: [
        TableRow(
          children: [
            Text(
              isEng ? 'Product' : 'المنتج',
              style: _baseTextStyle.copyWith(fontSize: 16),
            ).paddingOnly(right: 12),
            Text(
              isEng ? 'Unit' : 'وحدة',
              style: _baseTextStyle.copyWith(fontSize: 16),
            ),
            Text(
              isEng ? 'Qty' : 'كمية',
              style: _baseTextStyle.copyWith(fontSize: 16),
            ),
            Text(
              isEng ? 'Price' : 'سعر',
              style: _baseTextStyle.copyWith(fontSize: 16),
            ),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                isEng ? 'Total' : 'إجمالي',
                style: _baseTextStyle.copyWith(fontSize: 16),
              ),
            ),
          ],
        ),
        for (var product in receiptData.products!)
          TableRow(
            children: [
              Text(
                product!["name"].toString(),
                style: _baseTextStyle.copyWith(fontSize: 16),
              ),
              Text(
                product["type"].toString(),
                style: _baseTextStyle.copyWith(fontSize: 16),
              ),
              Text(
                product["quantity"].toString(),
                style: _baseTextStyle.copyWith(fontSize: 16),
              ).paddingOnly(right: isEng ? 0 : 10, left: isEng ? 10 : 0),
              Text(
                '${product["price"]}',
                style: _baseTextStyle.copyWith(fontSize: 16),
              ).paddingOnly(right: isEng ? 0 : 5, left: isEng ? 5 : 0),
              Text(
                '${product["price"] * product["quantity"]}',
                style: _baseTextStyle.copyWith(fontSize: 16),
              ).paddingOnly(right: isEng ? 0 : 10, left: isEng ? 10 : 0),
            ],
          ),
      ],
    );
  }
}

class _BottomSection extends StatelessWidget {
  final InvoiceModel receiptData;
  final bool isEng;

  const _BottomSection({
    required this.receiptData,
    this.isEng = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              (isEng ? 'Amount Before Tax: ' : 'المبلغ قبل الضريبة: '),
              style: _baseTextStyle,
            ),
            Text(
              '${receiptData.totalWithoutTax!.roundedPrecisionToString(5)} ${currency(isEng)}',
              style: _baseTextStyle,
            ),
          ],
        ),
        if (receiptData.discount != null && receiptData.discount! > 0) ...[
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                (isEng ? 'Discount: ' : 'الخصم: '),
                style: _baseTextStyle,
              ),
              Text(
                '${receiptData.discount!.roundedPrecisionToString(5)}${currency(isEng)}',
                style: _baseTextStyle,
              ),
            ],
          ),
        ],
        const SizedBox(height: 4),
        // الضريبة
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${isEng ? 'Tax: ' : 'الضريبة: '}${receiptData.taxValPercent}%',
              style: _baseTextStyle,
            ),
            Text(
              '${((receiptData.totalPrice ?? 0) - (receiptData.totalWithoutTax ?? 0)).abs().roundedPrecisionToString(5)} ${currency(isEng)}',
              style: _baseTextStyle,
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              (isEng ? 'Amount After Tax: ' : 'المبلغ بعد الضريبة: '),
              style: _baseTextStyle,
            ),
            Text(
              '${receiptData.totalPrice!.roundedPrecisionToString(5)} ${currency(isEng)}',
              style: _baseTextStyle,
            ),
          ],
        ),
        const SizedBox(height: 15),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              (isEng ? 'Paid Cash: ' : 'مدفوع نقدي: '),
              style: _baseTextStyle,
            ),
            const SizedBox(
              width: 2,
            ),
            Text(
              '${receiptData.paidCash!.roundedPrecisionToString(5)} ${currency(isEng)}',
              style: _baseTextStyle,
            ),
          ],
        ),
        if (receiptData.totalPrice != receiptData.paidCash)
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                (isEng ? 'Credit: ' : 'آجل: '),
                style: _baseTextStyle,
              ),
              const SizedBox(
                width: 2,
              ),
              Text(
                '${(receiptData.totalPrice! - receiptData.paidCash!).roundedPrecisionToString(5)} ${currency(isEng)}',
                style: _baseTextStyle,
              ),
            ],
          ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              (isEng ? 'Invoice Type: ' : 'نوع الفاتورة: '),
              style: _baseTextStyle,
            ),
            const SizedBox(
              width: 2,
            ),
            Text(
              receiptData.invoiceType == 'cash'
                  ? (isEng ? 'Cash' : 'نقدا')
                  : (isEng ? 'Credit' : 'آجل'),
              style: _baseTextStyle,
            ),
          ],
        ),
      ],
    );
  }
}

//import 'package:flutter/material.dart';
// import 'package:intl/intl.dart' show DateFormat;
// import 'package:mandob/core/extensions/context_extensions.dart';
// import 'package:mandob/models/company_info_model.dart';
// import 'package:mandob/models/invoice_model.dart';
//
// class ThermalInvoicePrintWidget extends StatelessWidget {
//   final InvoiceModel receiptData;
//   final bool isReturned;
//   final bool isPurchase;
//   final CompanyInfoModel? info;
//   final BuildContext context;
//
//   const ThermalInvoicePrintWidget({
//     super.key,
//     required this.receiptData,
//     required this.isReturned,
//     required this.isPurchase,
//     required this.info,
//     required this.context,
//   });
//
//   @override
//   Widget build(BuildContext _) {
//     const fontFamily = 'Cairo';
//
//     const baseTextStyle = TextStyle(
//       color: Colors.black,
//       fontSize: 16,
//       fontFamily: fontFamily,
//     );
//
//     return Container(
//       width: 550,
//       height: 750,
//       color: Colors.white,
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.end,
//         children: [
//           Text(info?.companyName ?? 'Company Name',
//               style: baseTextStyle.copyWith(fontWeight: FontWeight.bold)),
//           Text('VAT # ${info?.taxNumber ?? ''}', style: baseTextStyle),
//           Text(info?.address ?? 'Address', style: baseTextStyle),
//           const SizedBox(height: 8),
//           const Divider(),
//           const SizedBox(height: 8),
//           Center(
//             child: Text(
//               isPurchase == false && isReturned == true
//                   ? 'إشعار دائن للفاتورة الضريبية المبسطة'
//                   : isPurchase == true && isReturned == true
//                       ? 'إشعار مدين للفاتورة الضريبية المبسطة'
//                       : 'فاتورة ضريبية مبسطة',
//               style: baseTextStyle.copyWith(
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//           ),
//           const SizedBox(height: 8),
//           const Divider(),
//           const SizedBox(height: 8),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Column(
//                 crossAxisAlignment: CrossAxisAlignment.end,
//                 children: [
//                   Text(
//                     'رقم الفاتورة: ${receiptData.invoiceNumber ?? receiptData.invoiceId.toString().substring(0, 7)}',
//                     style: baseTextStyle,
//                   ),
//                   Text(
//                     'التاريخ: ${DateFormat("dd-MM-yyyy").format(DateTime.parse(receiptData.createdAt!))}',
//                     style: baseTextStyle,
//                   ),
//                 ],
//               ),
//               Column(
//                 crossAxisAlignment: CrossAxisAlignment.end,
//                 children: [
//                   Text(
//                     'اسم العميل: ${receiptData.customerName}',
//                     style: baseTextStyle,
//                   ),
//                   Text(
//                     'الرقم الضريبي: ${receiptData.customerVatNumber ?? ''}',
//                     style: baseTextStyle,
//                   ),
//                 ],
//               ),
//             ],
//           ),
//           const SizedBox(height: 8),
//           const Divider(),
//           const SizedBox(height: 8),
//           // Products list as table
//           Table(
//             border: TableBorder.all(),
//             columnWidths: const {
//               0: FlexColumnWidth(2),
//               1: FlexColumnWidth(2),
//               2: FlexColumnWidth(1),
//               3: FlexColumnWidth(1),
//               4: FlexColumnWidth(1),
//               5: FlexColumnWidth(2),
//             },
//             children: [
//               TableRow(
//                 decoration: BoxDecoration(color: Colors.grey[300]),
//                 children: const [
//                   Text('الاجمالى',
//                       style: baseTextStyle, textAlign: TextAlign.center),
//                   Text('سعر',
//                       style: baseTextStyle, textAlign: TextAlign.center),
//                   Text('وزن',
//                       style: baseTextStyle, textAlign: TextAlign.center),
//                   Text('كمية',
//                       style: baseTextStyle, textAlign: TextAlign.center),
//                   Text('وحدة',
//                       style: baseTextStyle, textAlign: TextAlign.center),
//                   Text('الصنف',
//                       style: baseTextStyle, textAlign: TextAlign.center),
//                 ],
//               ),
//               ...receiptData.products!.map((product) {
//                 final num total = product!["price"] * product["quantity"];
//                 return TableRow(
//                   children: [
//                     Text('${total .roundedPrecisionToString(5)}${currency(isEng)}',
//                         style: baseTextStyle.copyWith(
//                           fontSize: 14,
//                         ),
//                         textDirection: TextDirection.rtl,
//                         textAlign: TextAlign.center),
//                     Text(
//                         '${product["price"] .roundedPrecisionToString(5)}${currency(isEng)}',
//                         style: baseTextStyle.copyWith(
//                           fontSize: 14,
//                         ),
//                         textDirection: TextDirection.rtl,
//                         textAlign: TextAlign.center),
//                     Text(product["weight"]?.toString() ?? '-',
//                         style: baseTextStyle.copyWith(
//                           fontSize: 14,
//                         ),
//                         textAlign: TextAlign.center),
//                     Text(product["quantity"].toString(),
//                         style: baseTextStyle.copyWith(
//                           fontSize: 14,
//                         ),
//                         textAlign: TextAlign.center),
//                     Text(product['type'].toString(),
//                         style: baseTextStyle.copyWith(
//                           fontSize: 14,
//                         ),
//                         textAlign: TextAlign.center),
//                     Text(product["name"].toString(),
//                         style: baseTextStyle.copyWith(
//                           fontSize: 14,
//                         ),
//                         textAlign: TextAlign.center),
//                   ],
//                 );
//               }),
//             ],
//           ),
//           const SizedBox(height: 8),
//           const Divider(),
//           const SizedBox(height: 8),
//           Column(
//             crossAxisAlignment: CrossAxisAlignment.end,
//             children: [
//               Text(
//                 'المبلغ قبل الضريبة: ${receiptData.totalWithoutTax! .roundedPrecisionToString(5)}${currency(isEng)}',
//                 style: baseTextStyle,
//               ),
//               const SizedBox(height: 4),
//               Text(
//                 'الخصم: ${receiptData.discount! .roundedPrecisionToString(5)}${currency(isEng)}',
//                 style: baseTextStyle,
//               ),
//               const SizedBox(height: 4),
//               Text(
//                 'المبلغ بعد الضريبة: ${receiptData.totalPrice! .roundedPrecisionToString(5)}${currency(isEng)}',
//                 style: baseTextStyle,
//               ),
//               const SizedBox(height: 8),
//               const Divider(),
//               const SizedBox(height: 8),
//               Text(
//                 'مدفوع نقدي: ${receiptData.paidCash! .roundedPrecisionToString(5)}${currency(isEng)}',
//                 style: baseTextStyle,
//               ),
//               if (receiptData.totalPrice != receiptData.paidCash)
//                 Text(
//                   'آجل: ${(receiptData.totalPrice! - receiptData.paidCash!) .roundedPrecisionToString(5)}${currency(isEng)}',
//                   style: baseTextStyle,
//                 ),
//               Text(
//                 'نوع الفاتورة: ${receiptData.invoiceType == 'cash' ? "نقدا" : "آجل"}',
//                 style: baseTextStyle,
//               ),
//             ],
//           )
//         ],
//       ),
//     );
//   }
// }
