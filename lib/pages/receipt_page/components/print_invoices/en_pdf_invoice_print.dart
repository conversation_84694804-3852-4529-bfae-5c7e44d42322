import 'package:intl/intl.dart';
import 'package:mandob/appwrite_db/appwrite.dart';
import 'package:mandob/appwrite_db/db_consts.dart';
import 'package:mandob/models/company_info_model.dart';
import 'package:mandob/models/invoice_model.dart';
import 'package:mandob/utils/extensions.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

import 'print_pdf_invoice.dart';

pw.Widget getEnList(InvoiceModel receiptData, ttf) {
  pw.Widget cell(String text, bool header) => pw.Text(
        text,
        maxLines: 1,
        style: pw.TextStyle(
            fontSize: header ? 3.5 * PdfPageFormat.mm : 2.9 * PdfPageFormat.mm,
            fontWeight: header ? pw.FontWeight.bold : pw.FontWeight.normal,
            font: ttf),
        textDirection: pw.TextDirection.rtl,
      );

  final _productsWidget = pw.ListView(children: [
    pw.Table(columnWidths: {
      0: const pw.FlexColumnWidth(0.41 * PdfPageFormat.mm),
      1: const pw.FlexColumnWidth(0.3 * PdfPageFormat.mm),
      2: const pw.FlexColumnWidth(0.27 * PdfPageFormat.mm),
      3: const pw.FlexColumnWidth(0.27 * PdfPageFormat.mm),
      4: const pw.FlexColumnWidth(0.25 * PdfPageFormat.mm),
      5: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
    }, children: [
      pw.TableRow(children: [
        cell("Item", true),
        cell("Unit", true),
        pw.Center(child: cell("Quantity", true)),
        pw.Center(child: cell("Weight", true)),
        cell("Price", true),
        cell("Total", true),
      ]),
    ]),
    pw.Divider(
        thickness: 0.5 * PdfPageFormat.mm, height: 0.5 * PdfPageFormat.mm),
    pw.Table(
        columnWidths: {
          5: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
          4: const pw.FlexColumnWidth(0.25 * PdfPageFormat.mm),
          3: const pw.FlexColumnWidth(0.27 * PdfPageFormat.mm),
          2: const pw.FlexColumnWidth(0.27 * PdfPageFormat.mm),
          1: const pw.FlexColumnWidth(0.3 * PdfPageFormat.mm),
          0: const pw.FlexColumnWidth(0.41 * PdfPageFormat.mm),
        },
        children: receiptData.products!.map((product) {
          final num total = product!["price"] * product["quantity"];
          return pw.TableRow(children: [
            cell(product["name"].toString(), false),
            cell(product['type'].toString(), false),
            pw.Center(child: cell(product["quantity"].toString(), false)),
            pw.Center(
                child: cell(
                    product["weight"] == null || product["weight"] == 0
                        ? "-"
                        : product["weight"].toString(),
                    false)),
            cell((product["price"] as num).roundedPrecisionToString(5), false),
            cell(total.roundedPrecisionToString(5), false),
          ]);
        }).toList())
  ]);
  return _productsWidget;
}

addEnPdfPage(doc, ttf, ttfBold, InvoiceModel receiptData, bool isReturned,
    bool isPurchase, CompanyInfoModel? info, taxVal) {
  final isThamraDB = AppwriteDB.databaseId == DbConsts.thamraDB;

  doc.addPage(pw.Page(
      pageTheme: pw.PageTheme(
          pageFormat: PdfPageFormat.a4,
          theme: pw.ThemeData(bulletStyle: pw.TextStyle(font: ttf))),
      build: (pw.Context context) {
        return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            mainAxisSize: pw.MainAxisSize.max,
            children: [
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                        mainAxisSize: pw.MainAxisSize.max,
                        children: [
                          pw.Text(info!.companyName.toString(),
                              style: pw.TextStyle(
                                  fontSize: 2.5 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttfBold),
                              textDirection: pw.TextDirection.rtl,
                              textAlign: pw.TextAlign.left),
                          pw.SizedBox(height: 5),
                          pw.Text('VAT # ${info.vatNumber}',
                              style: pw.TextStyle(
                                  fontSize: 3 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttfBold),
                              textDirection: pw.TextDirection.ltr,
                              textAlign: pw.TextAlign.left),
                          pw.SizedBox(height: 5),
                          pw.Row(
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              children: [
                                pw.Text(
                                  'Address: ',
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttf),
                                  textDirection: pw.TextDirection.ltr,
                                ),
                                pw.SizedBox(width: 5),
                                pw.Text(
                                  info.address == null
                                      ? 'Central Vegetable Market in front of Badr Al-Din'
                                      : info.address!,
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttf),
                                  textDirection: pw.TextDirection.rtl,
                                ),
                              ]),
                        ]),
                    pw.BarcodeWidget(
                        width: 16.5 * PdfPageFormat.mm,
                        height: 16.5 * PdfPageFormat.mm,
                        data: generateQr(receiptData, info!),
                        barcode: pw.Barcode.qrCode()),
                  ]),
              pw.SizedBox(height: 10),
              pw.Divider(height: 5),
              pw.Center(
                  child: pw.Column(children: [
                if (isPurchase == false && isReturned == true) ...[
                  pw.Text(
                    'Credit Note for Simplified Tax Invoice',
                    style: pw.TextStyle(
                        fontSize: 3 * PdfPageFormat.mm,
                        fontWeight: pw.FontWeight.bold,
                        font: ttfBold),
                    textDirection: pw.TextDirection.ltr,
                  ),
                ] else if (isPurchase == true && isReturned == true) ...[
                  pw.Text(
                    'Debit Note for Simplified Tax Invoice',
                    style: pw.TextStyle(
                        fontSize: 3 * PdfPageFormat.mm,
                        fontWeight: pw.FontWeight.bold,
                        font: ttfBold),
                    textDirection: pw.TextDirection.ltr,
                  ),
                ] else ...[
                  pw.Text(
                    'Simplified Tax Invoice',
                    style: pw.TextStyle(
                        fontSize: 3 * PdfPageFormat.mm,
                        fontWeight: pw.FontWeight.bold,
                        font: ttfBold),
                    textDirection: pw.TextDirection.ltr,
                  ),
                ]
              ])),
              pw.SizedBox(height: 7),
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    if (isReturned) ...[
                      pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.start,
                          children: [
                            pw.Text(
                              '#: ',
                              style: pw.TextStyle(
                                  fontSize: 3.5 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttf),
                              textDirection: pw.TextDirection.ltr,
                            ),
                            pw.SizedBox(width: 5),
                            pw.Text(
                              receiptData.invoiceNumber == null
                                  ? receiptData.invoiceId
                                      .toString()
                                      .substring(0, 7)
                                  : receiptData.invoiceNumber!,
                              style: pw.TextStyle(
                                  fontSize: 3 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttf),
                              textDirection: pw.TextDirection.ltr,
                            ),
                          ]),
                      pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.start,
                          children: [
                            pw.Text(
                              'Invoice Number: ',
                              style: pw.TextStyle(
                                  fontSize: 3.5 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttf),
                              textDirection: pw.TextDirection.ltr,
                            ),
                            pw.SizedBox(width: 5),
                            pw.Text(
                              receiptData.mainInvoiceId!,
                              style: pw.TextStyle(
                                  fontSize: 3 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttf),
                              textDirection: pw.TextDirection.ltr,
                            ),
                          ]),
                    ] else ...[
                      pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.start,
                          children: [
                            pw.Text(
                              'Invoice Number: ',
                              style: pw.TextStyle(
                                  fontSize: 3.5 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttf),
                              textDirection: pw.TextDirection.ltr,
                            ),
                            pw.SizedBox(width: 5),
                            pw.Text(
                              receiptData.invoiceNumber == null
                                  ? receiptData.invoiceId
                                      .toString()
                                      .substring(0, 7)
                                  : receiptData.invoiceNumber!,
                              style: pw.TextStyle(
                                  fontSize: 3 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttf),
                              textDirection: pw.TextDirection.ltr,
                            ),
                          ]),
                    ],
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.start,
                        children: [
                          pw.Text(
                            'Date: ',
                            style: pw.TextStyle(
                                fontSize: 3.5 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttf),
                            textDirection: pw.TextDirection.ltr,
                          ),
                          pw.SizedBox(width: 5),
                          pw.Text(
                            DateFormat("dd-MM-yyyy hh:mm:ss")
                                .format(DateTime.parse(receiptData.createdAt!)),
                            style: pw.TextStyle(
                                fontSize: 3.5 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttf),
                            textDirection: pw.TextDirection.ltr,
                          ),
                        ]),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.start,
                        children: [
                          pw.Text(
                            'Customer Name: ',
                            style: pw.TextStyle(
                                fontSize: 3.5 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttf),
                            textDirection: pw.TextDirection.ltr,
                          ),
                          pw.SizedBox(width: 5),
                          pw.Text(
                            receiptData.customerName.toString(),
                            style: pw.TextStyle(
                                fontSize: 3.5 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttf),
                            textDirection: pw.TextDirection.rtl,
                          ),
                        ]),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.start,
                        children: [
                          pw.Text(
                            'VAT Number: ',
                            style: pw.TextStyle(
                                fontSize: 3.5 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttf),
                            textDirection: pw.TextDirection.ltr,
                          ),
                          pw.SizedBox(width: 5),
                          pw.Text(
                            '#${receiptData.customerVatNumber == null ? '' : receiptData.customerVatNumber!}',
                            style: pw.TextStyle(
                                fontSize: 3.5 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttf),
                            textDirection: pw.TextDirection.ltr,
                          ),
                        ]),
                    pw.SizedBox(height: 7),
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        getEnList(receiptData, ttf),
                        pw.Divider(height: 5),
                        pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Text(
                              'Amount Before Tax',
                              style: pw.TextStyle(
                                  fontSize: 3 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttfBold),
                              textDirection: pw.TextDirection.ltr,
                            ),
                            pw.Spacer(),
                            pw.Text(
                              receiptData.totalWithoutTax!
                                  .roundedPrecisionToString(5),
                              style: pw.TextStyle(
                                  fontSize: 3.5 * PdfPageFormat.mm,
                                  font: ttfBold),
                            ),
                            pw.SizedBox(width: 10),
                            pw.Text(
                              'SAR',
                              style: pw.TextStyle(
                                  fontSize: 3 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttfBold),
                              textDirection: pw.TextDirection.ltr,
                            ),
                          ],
                        ),
                        pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Text(
                              'Tax Percentage',
                              style: pw.TextStyle(
                                  fontSize: 3 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttfBold),
                              textDirection: pw.TextDirection.ltr,
                            ),
                            pw.Spacer(),
                            pw.Row(
                              children: [
                                pw.Text(
                                  '${receiptData.taxValPercent!.toInt()}%',
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                  textDirection: pw.TextDirection.ltr,
                                ),
                                pw.SizedBox(width: 2),
                                pw.Text(
                                  (taxVal as num).roundedPrecisionToString(5),
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      font: ttfBold),
                                ),
                                pw.Text(
                                  'SAR',
                                  style: pw.TextStyle(
                                      fontSize: 3 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                  textDirection: pw.TextDirection.ltr,
                                ),
                              ],
                            ),
                          ],
                        ),
                        if (!isPurchase) ...[
                          pw.Row(
                            children: [
                              pw.Text(
                                'Discount',
                                style: pw.TextStyle(
                                    fontSize: 3 * PdfPageFormat.mm,
                                    fontWeight: pw.FontWeight.bold,
                                    font: ttfBold),
                                textDirection: pw.TextDirection.ltr,
                              ),
                              pw.Spacer(),
                              pw.Text(
                                receiptData.discount!
                                    .roundedPrecisionToString(5),
                                style: pw.TextStyle(
                                    fontSize: 3.5 * PdfPageFormat.mm,
                                    font: ttfBold),
                              ),
                              pw.Text(
                                'SAR',
                                style: pw.TextStyle(
                                    fontSize: 3 * PdfPageFormat.mm,
                                    fontWeight: pw.FontWeight.bold,
                                    font: ttfBold),
                                textDirection: pw.TextDirection.ltr,
                              ),
                            ],
                          ),
                        ],
                        pw.Row(
                          children: [
                            pw.Text(
                              'Amount After Tax',
                              style: pw.TextStyle(
                                  fontSize: 3 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttfBold),
                              textDirection: pw.TextDirection.ltr,
                            ),
                            pw.Spacer(),
                            pw.Text(
                              receiptData.totalPrice!
                                  .roundedPrecisionToString(5),
                              style: pw.TextStyle(
                                  fontSize: 3.5 * PdfPageFormat.mm,
                                  font: ttfBold),
                            ),
                            pw.Text(
                              'SAR',
                              style: pw.TextStyle(
                                  fontSize: 3 * PdfPageFormat.mm,
                                  fontWeight: pw.FontWeight.bold,
                                  font: ttfBold),
                              textDirection: pw.TextDirection.ltr,
                            ),
                          ],
                        ),
                        pw.SizedBox(height: 15),
                        pw.Divider(
                          color: PdfColor.fromHex("#DDDEDE"),
                          height: 3 * PdfPageFormat.mm,
                          thickness: 1 * PdfPageFormat.mm,
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.only(left: 5),
                          child: pw.Row(
                            mainAxisAlignment: pw.MainAxisAlignment.start,
                            children: [
                              pw.Text(
                                'Paid Cash: ',
                                style: pw.TextStyle(
                                    fontSize: 3.5 * PdfPageFormat.mm,
                                    fontWeight: pw.FontWeight.bold,
                                    font: ttfBold),
                                textAlign: pw.TextAlign.left,
                                textDirection: pw.TextDirection.ltr,
                              ),
                              pw.SizedBox(width: 2),
                              pw.Text(
                                receiptData.paidCash!
                                    .roundedPrecisionToString(5),
                                style: pw.TextStyle(
                                    fontSize: 3.5 * PdfPageFormat.mm,
                                    fontWeight: pw.FontWeight.bold,
                                    font: ttfBold),
                              ),
                              pw.SizedBox(width: 5),
                              pw.Text(
                                'SAR',
                                style: pw.TextStyle(
                                    fontSize: 3 * PdfPageFormat.mm,
                                    fontWeight: pw.FontWeight.bold,
                                    font: ttfBold),
                                textDirection: pw.TextDirection.ltr,
                              ),
                            ],
                          ),
                        ),
                        if (receiptData.totalPrice != receiptData.paidCash) ...[
                          pw.Padding(
                            padding: const pw.EdgeInsets.only(left: 5),
                            child: pw.Row(
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              children: [
                                pw.Text(
                                  'Credit: ',
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                  textDirection: pw.TextDirection.ltr,
                                ),
                                pw.SizedBox(width: 2),
                                pw.Text(
                                  (receiptData.totalPrice! -
                                          receiptData.paidCash!)
                                      .roundedPrecisionToString(5),
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                ),
                                pw.SizedBox(width: 5),
                                pw.Text(
                                  'SAR',
                                  style: pw.TextStyle(
                                      fontSize: 3 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                  textDirection: pw.TextDirection.ltr,
                                ),
                              ],
                            ),
                          ),
                          pw.Divider(
                            height: 1.5 * PdfPageFormat.mm,
                            thickness: 1,
                            indent: 3 * PdfPageFormat.mm,
                            endIndent: 3 * PdfPageFormat.mm,
                          ),
                        ],
                        if (receiptData.invoiceType != null) ...[
                          pw.Padding(
                            padding: const pw.EdgeInsets.only(left: 5),
                            child: pw.Row(
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              children: [
                                pw.Text(
                                  'Invoice Type: ',
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                  textDirection: pw.TextDirection.ltr,
                                ),
                                pw.SizedBox(width: 2),
                                pw.Text(
                                  receiptData.invoiceType.toString() == 'cash'
                                      ? "Cash"
                                      : "Credit",
                                  style: pw.TextStyle(
                                      fontSize: 3.5 * PdfPageFormat.mm,
                                      fontWeight: pw.FontWeight.bold,
                                      font: ttfBold),
                                  textDirection: pw.TextDirection.ltr,
                                ),
                              ],
                            ),
                          ),
                        ],
                        if (!isPurchase) ...[
                          if (info.bankName != null &&
                              info.bankNumber != null) ...[
                            pw.Divider(
                              color: PdfColor.fromHex("#DDDEDE"),
                              height: 3 * PdfPageFormat.mm,
                              thickness: 1 * PdfPageFormat.mm,
                            ),
                            pw.SizedBox(height: 10),
                          ],
                          if (isThamraDB) ...[
                            pw.SizedBox(height: 10),
                            pw.Center(
                              child: pw.Text(
                                "Thank you for your business and cooperation\n"
                                "For inquiries and comments, please contact customer service\n"
                                "+************",
                                style: pw.TextStyle(
                                    fontSize: 2.8 * PdfPageFormat.mm,
                                    fontWeight: pw.FontWeight.bold,
                                    font: ttfBold),
                                textDirection: pw.TextDirection.ltr,
                                textAlign: pw.TextAlign.center,
                              ),
                            ),
                          ],
                          if (info.bankName != null &&
                              info.bankNumber != null &&
                              !isThamraDB) ...[
                            pw.SizedBox(height: 10),
                            pw.Center(
                              child: pw.Text(
                                """Thank you for doing business with ${info.companyName}. We appreciate your cooperation.
You can also pay to our account at ${info.bankName} number""",
                                style: pw.TextStyle(
                                    fontSize: 2.8 * PdfPageFormat.mm,
                                    fontWeight: pw.FontWeight.bold,
                                    font: ttfBold),
                                textDirection: pw.TextDirection.ltr,
                                textAlign: pw.TextAlign.center,
                              ),
                            ),
                            pw.Center(
                              child: pw.Text(
                                '${info.bankNumber}',
                                style: pw.TextStyle(
                                    fontSize: 2.8 * PdfPageFormat.mm,
                                    fontWeight: pw.FontWeight.bold,
                                    font: ttfBold),
                                textDirection: pw.TextDirection.ltr,
                                textAlign: pw.TextAlign.center,
                              ),
                            ),
                          ],
                        ],
                      ],
                    )
                  ]),
            ]);
      }));
}
