import 'package:intl/intl.dart';
import 'package:mandob/appwrite_db/appwrite.dart';
import 'package:mandob/appwrite_db/db_consts.dart';
import 'package:mandob/models/company_info_model.dart';
import 'package:mandob/models/invoice_model.dart';
import 'package:mandob/pages/receipt_page/components/print_invoices/print_pdf_invoice.dart';
import 'package:mandob/utils/extensions.dart';
import 'package:sunmi_printer_plus/enums.dart';
import 'package:sunmi_printer_plus/sunmi_printer_plus.dart';
import 'package:sunmi_printer_plus/sunmi_style.dart';
import 'package:sunmi_printer_service/sunmi_printer_service.dart' as s;

Future printInvoice(InvoiceModel receiptData, bool isReturned, bool isPurchase,
    CompanyInfoModel info) async {
  var taxVal = receiptData.totalPrice! - receiptData.totalWithoutTax!;
  final isThamraDB = AppwriteDB.databaseId == DbConsts.thamraDB;

  await SunmiPrinter.initPrinter();
  await s.SunmiPrinterService.init();
  await SunmiPrinter.startTransactionPrint(true);

  await SunmiPrinter.setAlignment(SunmiPrintAlign.CENTER);
  await SunmiPrinter.printText(info.companyName.toString(),
      style: SunmiStyle(
          fontSize: SunmiFontSize.LG,
          align: SunmiPrintAlign.RIGHT,
          bold: true));

  await SunmiPrinter.printText('VAT # ${info.vatNumber.toString()}',
      style: SunmiStyle(
          fontSize: SunmiFontSize.MD,
          align: SunmiPrintAlign.RIGHT,
          bold: true));

  await SunmiPrinter.printText(
      'العنوان ${info.address == null ? 'سوق الخضار المركزي أمام بدر الدين' : info.address.toString()}',
      style: SunmiStyle(
          fontSize: SunmiFontSize.MD,
          align: SunmiPrintAlign.RIGHT,
          bold: true));

  await SunmiPrinter.line();
  await SunmiPrinter.lineWrap(1);

  if (isPurchase == false && isReturned == true) {
    await SunmiPrinter.printText('إشعار دائن للفاتورة الضريبية المبسطة',
        style: SunmiStyle(
            fontSize: SunmiFontSize.LG,
            align: SunmiPrintAlign.CENTER,
            bold: true));
  } else if (isPurchase == true && isReturned == true) {
    await SunmiPrinter.printText('إشعار مدين للفاتورة الضريبية المبسطة',
        style: SunmiStyle(
            fontSize: SunmiFontSize.LG,
            align: SunmiPrintAlign.CENTER,
            bold: true));
  } else {
    await SunmiPrinter.printText('فاتورة ضريبية مبسطة',
        style: SunmiStyle(
            fontSize: SunmiFontSize.LG,
            align: SunmiPrintAlign.CENTER,
            bold: true));
  }

  await SunmiPrinter.lineWrap(1);

  if (isReturned == true) {
    await SunmiPrinter.printText(
        'رقم الإشعار ${receiptData.invoiceNumber == null ? receiptData.invoiceId.toString().substring(1, 7) : receiptData.invoiceNumber.toString()}',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD, align: SunmiPrintAlign.RIGHT));

    await SunmiPrinter.printText(
        'رقم الفاتورة ${receiptData.mainInvoiceId.toString()}',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD, align: SunmiPrintAlign.RIGHT));
  } else {
    await SunmiPrinter.printText(
        'رقم الفاتورة ${receiptData.invoiceNumber == null ? receiptData.invoiceId.toString().substring(1, 7) : receiptData.invoiceNumber.toString()}',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD, align: SunmiPrintAlign.RIGHT));
  }

  await SunmiPrinter.printText(
      'التاريخ ${DateFormat("dd-MM-yyyy hh:mm").format(DateTime.parse(receiptData.createdAt!))}',
      style:
          SunmiStyle(fontSize: SunmiFontSize.MD, align: SunmiPrintAlign.RIGHT));

  if (isPurchase) {
    await SunmiPrinter.printText(
        'المورد: ${receiptData.customerName.toString()}',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD, align: SunmiPrintAlign.RIGHT));
  } else {
    await SunmiPrinter.printText(
        'العميل: ${receiptData.customerName.toString()}',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD, align: SunmiPrintAlign.RIGHT));
    await SunmiPrinter.printText(
        'الرقم الضريبي: ${receiptData.customerVatNumber == null ? '' : receiptData.customerVatNumber.toString() + '#'}',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD, align: SunmiPrintAlign.RIGHT));
  }

  await SunmiPrinter.line();
  await SunmiPrinter.lineWrap(1);

  await SunmiPrinter.setFontSize(SunmiFontSize.MD);

  await SunmiPrinter.bold();

  await s.SPrinter.columnsString(
    ["إجمالي", "سعر", "وزن", "كمية", "الصنف"],
    width: [5, 4, 5, 3, 6],
    align: [1, 1, 1, 1, 2],
  );

  await SunmiPrinter.setFontSize(SunmiFontSize.MD);
  await s.SPrinter.setAlign(s.Align.right);

  await SunmiPrinter.resetBold();

  receiptData.products!.forEach((item) async {
    await s.SPrinter.columnsString(
      [
        ((item!['price'] * item['quantity']) as num)
            .roundedPrecisionToString(5),
        (item['price'] as num).roundedPrecisionToString(5),
        item['weight'] == null || item['weight'].toString() == '0'
            ? '-'
            : item['weight'].toString(),
        // item['type'].toString(),
        item['quantity'].toString(),
        item['name'].toString()
      ],
      width: [5, 4, 5, 3, 6],
      align: [1, 2, 1, 1, 2],
    );
  });

  await SunmiPrinter.line();
  await SunmiPrinter.setFontSize(SunmiFontSize.MD);
  await SunmiPrinter.setAlignment(SunmiPrintAlign.RIGHT);

  await SunmiPrinter.printText(
      'المبلغ قبل الضريبة      ${receiptData.totalWithoutTax!.roundedPrecisionToString(5)} ر.س',
      style: SunmiStyle(
          fontSize: SunmiFontSize.MD,
          align: SunmiPrintAlign.RIGHT,
          bold: true));

  if (!isPurchase) {
    await SunmiPrinter.printText(
        'الخصم      ${receiptData.discount!.roundedPrecisionToString(5)} ر.س',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD,
            align: SunmiPrintAlign.RIGHT,
            bold: true));
  }

  await SunmiPrinter.printText(
      'ضريبة ${int.tryParse((receiptData.taxValPercent ?? 15).toStringAsFixed(0))}%            ${taxVal.roundedPrecisionToString(5)} ر.س ',
      style: SunmiStyle(
          fontSize: SunmiFontSize.MD,
          align: SunmiPrintAlign.RIGHT,
          bold: true));

  await SunmiPrinter.printText(
      'المبلغ بعد الضريبة      ${receiptData.totalPrice!.roundedPrecisionToString(5)} ر.س',
      style: SunmiStyle(
          fontSize: SunmiFontSize.MD,
          align: SunmiPrintAlign.RIGHT,
          bold: true));

  await SunmiPrinter.line();

  await SunmiPrinter.bold();
  //Footer
  await SunmiPrinter.printText(
      'مدفوع نقدي: ${receiptData.paidCash!.roundedPrecisionToString(5)} ر.س',
      style: SunmiStyle(
          fontSize: SunmiFontSize.MD,
          align: SunmiPrintAlign.RIGHT,
          bold: true));

  if (receiptData.paidCash != receiptData.totalPrice) {
    await SunmiPrinter.printText(
        'آجل: ${(receiptData.totalPrice! - receiptData.paidCash!).roundedPrecisionToString(5)} ر.س',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD,
            align: SunmiPrintAlign.RIGHT,
            bold: true));
  }

  // inoice type (نقدي - اجل)
  if (receiptData.invoiceType != null) {
    await SunmiPrinter.printText(
        'نوع الفاتورة: ${receiptData.invoiceType.toString() == 'cash' ? "نقدا" : "آجل"}',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD,
            align: SunmiPrintAlign.RIGHT,
            bold: true));
  }

  await SunmiPrinter.line();
  await SunmiPrinter.setFontSize(SunmiFontSize.MD);
  await SunmiPrinter.setAlignment(SunmiPrintAlign.CENTER);

  if (isThamraDB) {
    await SunmiPrinter.printText(
        "‏نشكر لكم تعاملكم وتعاونكم\n"
        "‏وللملاحظات والاستفسارات التواصل مع خدمة العملاء\n"
        "+************",
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD,
            align: SunmiPrintAlign.CENTER,
            bold: true));
  }

  //? Bank Info
  if (info.bankName != null && info.bankNumber != null && !isThamraDB) {
    await SunmiPrinter.printText(
        """نشكر لكم تعاملكم مع ${info.companyName} ويسرنا تعاونكم 
كما يمكنكم السداد على حسابنا في  ${info.bankName} رقم""",
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD,
            align: SunmiPrintAlign.CENTER,
            bold: true));

    await SunmiPrinter.printText('${info.bankNumber}',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD,
            align: SunmiPrintAlign.CENTER,
            bold: true));
  }

  await SunmiPrinter.line();
  await SunmiPrinter.lineWrap(1);
  await SunmiPrinter.setAlignment(SunmiPrintAlign.CENTER);

  await s.SPrinter.qrCode(generateQr(receiptData, info));

  await SunmiPrinter.lineWrap(4);

  await SunmiPrinter.exitTransactionPrint(true);
  await SunmiPrinter.cut();
}
