import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/core/shared/language/change_language.widget.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/models/invoice_model.dart';
import 'package:mandob/models/supplier_model.dart';
import 'package:mandob/pages/receipt_page/components/print_invoices/print_invoice.dart';
import 'package:mandob/pages/receipt_page/components/print_invoices/print_pdf_invoice.dart';
import 'package:mandob/pages/receipt_page/components/print_invoices/print_thermal_invoice.dart';
import 'package:mandob/providers/company_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/extensions.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:sunmi_printer_plus/sunmi_printer_plus.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../utils/color_manager.dart';
import 'add_purachase_invoice/add_purchase_invoice_dialog.dart';
import 'add_sales_invoice/add_sales_invoice_dialog.dart';
import 'delete_invoice_dialog.dart';

class RowInvoiceWidget extends StatefulWidget {
  final InvoiceModel invoice;
  final bool isReturned;
  final bool isPurchase;

  const RowInvoiceWidget({
    Key? key,
    required this.invoice,
    required this.isReturned,
    required this.isPurchase,
  }) : super(key: key);

  @override
  State<RowInvoiceWidget> createState() => _RowInvoiceWidgetState();
}

class _RowInvoiceWidgetState extends State<RowInvoiceWidget> {
  bool printBinded = false;
  int paperSize = 0;
  String serialNumber = "";
  String printerVersion = "";

  @override
  void initState() {
    _bindingPrinter().then((bool? isBind) async {
      SunmiPrinter.paperSize().then((int size) {
        setState(() {
          paperSize = size;
        });
      });

      SunmiPrinter.printerVersion().then((String version) {
        setState(() {
          printerVersion = version;
        });
      });

      SunmiPrinter.serialNumber().then((String serial) {
        setState(() {
          serialNumber = serial;
        });
      });

      setState(() {
        printBinded = isBind!;
      });
    });
    super.initState();
    Provider.of<CompanyInfoProvider>(context, listen: false).fetchCompanyInfo();
  }

  Future<bool?> _bindingPrinter() async {
    final bool? result = await SunmiPrinter.bindingPrinter();
    return result;
  }

  @override
  Widget build(BuildContext context) {
    InvoiceModel receiptData = widget.invoice;

    var infoData = Provider.of<CompanyInfoProvider>(context, listen: false);
    var user = Provider.of<UserProvider>(context, listen: false).activeUser;

    return ExpansionTile(
      backgroundColor: Colors.transparent,
      showTrailingIcon: false,
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Text(
              receiptData.invoiceNumber == null
                  ? receiptData.invoiceId.toString().substring(1, 7)
                  : receiptData.invoiceNumber.toString(),
              style: const TextStyle(
                  fontFamily: 'Droid',
                  fontSize: 13,
                  fontWeight: FontWeight.bold),
            ),
          ),
          AppGaps.gap8,
          Expanded(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                receiptData.createdAt.toString().substring(0, 10),
                style: const TextStyle(
                    fontFamily: 'Droid',
                    fontSize: 13,
                    fontWeight: FontWeight.bold),
              ),
            ),
          ),
          AppGaps.gap12,
          Expanded(
            // width: widget.isPurchase ? 80 : 70,
            child: Text(
              receiptData.customerName ?? '',
              style: const TextStyle(
                  fontFamily: 'Droid',
                  fontSize: 13,
                  fontWeight: FontWeight.bold),
              maxLines: 3,
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (!widget.isPurchase)
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                    left: context.isEng ? 30 : 0,
                    right: context.isEng ? 0 : 15),
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    '${(receiptData.discount?.roundedPrecisionToString(5).toString())!}${context.currency}',
                    style: const TextStyle(
                      fontFamily: 'Droid',
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                      color: ColorManager.errorColor,
                    ),
                  ),
                ),
              ),
            ),
          Expanded(
            child: Container(
              padding: EdgeInsets.only(
                  left: context.isEng ? 15 : 0, right: context.isEng ? 0 : 15),
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  '${(receiptData.totalPrice?.roundedPrecisionToString(5).toString())!}${context.currency}',
                  style: const TextStyle(
                    fontFamily: 'Droid',
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      children: [
        Stack(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: EdgeInsets.only(
                      left: context.isEng ? 26.0 : 0.0,
                      right: context.isEng ? 0.0 : 26.0,
                    ),
                    child: Text(
                      context.isEng ? 'Product Name' : 'اسم المنتج',
                      textAlign: TextAlign.start,
                    ),
                  ),
                ),
                Expanded(child: Text(context.isEng ? 'Quantity' : 'الكمية')),
                Expanded(child: Text(context.isEng ? 'Unit' : 'الوحدة')),
                Expanded(child: Text(context.isEng ? 'Total' : 'الإجمالي')),
              ],
            ),
            if (receiptData.products != null)
              Container(
                margin: const EdgeInsets.only(
                  bottom: 50,
                  top: 10,
                ),
                child: ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  separatorBuilder: (context, index) {
                    return Container(
                      margin: const EdgeInsets.only(
                          top: 7, bottom: 6, right: 25, left: 25),
                      height: 0.6,
                      color: Colors.black54,
                    );
                  },
                  padding: const EdgeInsets.all(12),
                  itemCount: receiptData.products!.length,
                  itemBuilder: (context, itemsIndex) {
                    var items = receiptData.products![itemsIndex];

                    return Padding(
                      padding: const EdgeInsets.only(top: 5),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Padding(
                              padding: EdgeInsets.only(
                                left: context.isEng ? 20.0 : 0.0,
                                right: context.isEng ? 0.0 : 10.0,
                              ),
                              child: Text(
                                items!['name'],
                                style: const TextStyle(
                                    fontWeight: FontWeight.w300),
                                textAlign: TextAlign.start,
                              ),
                            ),
                          ),
                          Expanded(
                            child: Padding(
                              padding: EdgeInsets.only(
                                left: context.isEng ? 30.0 : 0.0,
                                right: context.isEng ? 0.0 : 10.0,
                              ),
                              child: Text(
                                items['quantity'].toString(),
                                style: const TextStyle(
                                    fontWeight: FontWeight.w300),
                              ),
                            ),
                          ),
                          Expanded(
                            child: Padding(
                              padding: EdgeInsets.only(
                                left: context.isEng ? 5.0 : 0.0,
                                right: context.isEng ? 0.0 : 5.0,
                              ),
                              child: Text(
                                items['type']?.toString() ?? '',
                                style: const TextStyle(
                                    fontWeight: FontWeight.w300),
                              ),
                            ),
                          ),
                          Expanded(
                            child: Padding(
                              padding: EdgeInsets.only(
                                left: context.isEng ? 16.0 : 0.0,
                                right: context.isEng ? 0.0 : 16.0,
                              ),
                              child: Text(
                                '${context.currency} ${(items['price'] as num).roundedPrecisionToString(5)}',
                                style: const TextStyle(
                                    fontWeight: FontWeight.w300),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            Positioned(
              bottom: 10,
              left: context.isEng ? null : 10,
              right: context.isEng ? 10 : null,
              child: Row(
                children: [
                  //Delete
                  user!.isAdmin!
                      ? GestureDetector(
                          onTap: () async {
                            await showDialog(
                                context: context,
                                builder: (_) =>
                                    DeleteInvoiceDialog(invoice: receiptData));
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                height: 35,
                                margin: const EdgeInsets.only(right: 10),
                                padding:
                                    const EdgeInsets.only(left: 8, right: 8),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: Colors.redAccent,
                                ),
                                child: Center(
                                  child: Text(
                                    context.isEng ? 'Delete' : 'حذف',
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                              ),
                            ],
                          ))
                      : Container(),

                  const SizedBox(
                    width: 15,
                  ),

                  GestureDetector(
                      onTap: () async {
                        // showModalBottomSheet(
                        //     context: context,
                        //     isScrollControlled: true,
                        //     builder: (context) {
                        //       return Padding(
                        //         padding: const EdgeInsets.all(30),
                        //         child: ThermalInvoicePrintWidget(
                        //           receiptData: receiptData,
                        //           isReturned: widget.isReturned,
                        //           isPurchase: widget.isPurchase,
                        //           info: infoData.companyInfo,
                        //           context: context,
                        //         ),
                        //       );
                        //     });
                        //
                        // return;

                        await showDialog(
                            context: context,
                            builder: (_) => AlertDialog(
                                  content: SizedBox(
                                    height: 230,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          context.isEng
                                              ? 'Print Type'
                                              : 'نوع الطباعة',
                                          style: const TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        AppGaps.gap24,
                                        GestureDetector(
                                          onTap: () async {
                                            await printInvoice(
                                                    receiptData,
                                                    widget.isReturned,
                                                    widget.isPurchase,
                                                    infoData.companyInfo!)
                                                .whenComplete(() {
                                              showBar(
                                                  context,
                                                  context.isEng
                                                      ? 'Invoice printed successfully'
                                                      : 'تمت طباعة الفاتورة بنجاح',
                                                  backgroundColor:
                                                      ColorManager.primaryColor,
                                                  indicatorColor: ColorManager
                                                      .secondaryColor,
                                                  icon: Icons.done_all);
                                            });
                                          },
                                          child: Container(
                                            height: 50,
                                            padding: const EdgeInsets.only(
                                                left: 8, right: 8),
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              color: ColorManager.primaryColor,
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                const Icon(
                                                  Icons.receipt_long,
                                                  color: Colors.white,
                                                ),
                                                const SizedBox(
                                                  width: 5,
                                                ),
                                                Text(
                                                  context.isEng
                                                      ? 'Print Roll'
                                                      : 'طباعة Roll',
                                                  style: const TextStyle(
                                                      color: Colors.white),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        const SizedBox(
                                          height: 15,
                                        ),
                                        GestureDetector(
                                          onTap: () async {
                                            showChangeLanguageDialog(
                                              context,
                                              onEnTap: () {
                                                printPdfInvoice(
                                                  receiptData,
                                                  widget.isReturned,
                                                  widget.isPurchase,
                                                  infoData.companyInfo,
                                                  context,
                                                  isEng: true,
                                                );
                                              },
                                              onArTap: () async {
                                                await printPdfInvoice(
                                                  receiptData,
                                                  widget.isReturned,
                                                  widget.isPurchase,
                                                  infoData.companyInfo,
                                                  context,
                                                );
                                              },
                                            );
                                          },
                                          child: Container(
                                            height: 50,
                                            padding: const EdgeInsets.only(
                                                left: 8, right: 8),
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              color: ColorManager.brown,
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                const Icon(
                                                  Icons.assignment_sharp,
                                                  color: Colors.white,
                                                ),
                                                const SizedBox(
                                                  width: 5,
                                                ),
                                                Text(
                                                  context.isEng
                                                      ? 'Print A4'
                                                      : 'طباعة A4',
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        const SizedBox(
                                          height: 15,
                                        ),
                                        HookBuilder(builder: (context) {
                                          final screenshotController =
                                              useState(ScreenshotController());

                                          void _printThermal(
                                              {bool isEng = false}) async {
                                            final printedWidget = Screenshot(
                                              controller:
                                                  screenshotController.value,
                                              child: ThermalInvoicePrintWidget(
                                                receiptData: receiptData,
                                                isReturned: widget.isReturned,
                                                isPurchase: widget.isPurchase,
                                                info: infoData.companyInfo,
                                                context: context,
                                                isEng: isEng,
                                              ),
                                            );

                                            final savedDevice =
                                                GetStorageService.getData(
                                                    key: LocalKeys.printer);

                                            if (savedDevice == null) {
                                              // await showModalBottomSheet(
                                              //   context: context,
                                              //   isScrollControlled: true,
                                              //   builder: (_) => const SizedBox(
                                              //       height: 600,
                                              //       child: AddPrinterScreen()),
                                              // );
                                            } else {
                                              await printThermalInvoice(context,
                                                  printedWidget: printedWidget,
                                                  screenshotController:
                                                      screenshotController
                                                          .value);
                                            }
                                          }

                                          return GestureDetector(
                                            onTap: () async {
                                              showChangeLanguageDialog(
                                                context,
                                                onEnTap: () =>
                                                    _printThermal(isEng: true),
                                                onArTap: () => _printThermal(),
                                              );
                                            },
                                            child: Container(
                                              height: 50,
                                              padding: const EdgeInsets.only(
                                                  left: 8, right: 8),
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                color:
                                                    ColorManager.secondaryColor,
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  const Icon(
                                                    Icons.assignment_sharp,
                                                    color: Colors.white,
                                                  ),
                                                  const SizedBox(
                                                    width: 5,
                                                  ),
                                                  Text(
                                                    context.isEng
                                                        ? 'Thermal Print'
                                                        : 'طباعة حرارية',
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          );
                                        }),
                                      ],
                                    ),
                                  ),
                                ));
                      },
                      child: Container(
                        height: 35,
                        padding: const EdgeInsets.only(left: 8, right: 8),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: ColorManager.primaryColor),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              context.isEng ? 'Print' : 'طباعة',
                              style: const TextStyle(color: Colors.white),
                            ),
                            const SizedBox(
                              width: 5,
                            ),
                            const Icon(
                              Icons.print,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      )),

                  !widget.isReturned
                      ? GestureDetector(
                          onTap: () {
                            showDialog(
                                context: context,
                                builder: (_) => widget.isPurchase
                                    ? AddPurchaseInvoiceDialog(
                                        isReturned: true,
                                        byProductPrice: true,
                                        invoiceData: receiptData,
                                        supplier: SupplierModel(
                                          id: receiptData.customerId,
                                          name: receiptData.customerName,
                                        ),

                                        // invoiceType: receiptData.invoiceType,
                                        // invoiceId: receiptData.invoiceNumber ??
                                        //     receiptData.invoiceId,
                                        // items: receiptData.products,
                                        // senderRepo:
                                        //     receiptData.storeId.toString(),
                                        // supplierId:
                                        //     receiptData.customerId.toString(),
                                        // supplierName:
                                        //     receiptData.customerName.toString(),
                                        // paidCash:
                                        //     receiptData.paidCash.toString(),
                                      )
                                    : AddSalesInvoiceDialog(
                                        isReturned: true,
                                        byProductPrice: true,
                                        senderRepoId:
                                            receiptData.storeId.toString(),
                                        invoiceData: receiptData,
                                        customerData: CustomerModel(
                                          id: receiptData.customerId,
                                          customerName:
                                              receiptData.customerName,
                                        ),
                                      ));
                          },
                          child: Container(
                            height: 35,
                            margin: const EdgeInsets.symmetric(horizontal: 10),
                            padding: const EdgeInsets.only(left: 8, right: 8),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: ColorManager.secondaryColor),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  context.isEng ? 'Return' : 'مرتجع',
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          ))
                      : Container(),
                ],
              ),
            ),
            Positioned(
                bottom: 15,
                right: context.isEng ? null : 15,
                left: context.isEng ? 15 : null,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.isEng
                          ? 'Total Products: ${receiptData.products != null && receiptData.products!.isNotEmpty ? receiptData.products!.length.toString() : ''}'
                          : 'إجمالي المنتجات: ${receiptData.products != null && receiptData.products!.isNotEmpty ? receiptData.products!.length.toString() : ''}',
                      style: const TextStyle(color: Colors.black54),
                    ),
                  ],
                )),
          ],
        ),
      ],
    );
  }
}
