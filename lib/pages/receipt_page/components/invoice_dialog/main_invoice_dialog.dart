import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/pages/receipt_page/components/invoice_dialog/components/view_invoice_items_in_dialog.dart';
import 'package:mandob/pages/repositories_page/transfer_repository_items/transfer_table/main_transfer_table.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:provider/provider.dart';

import '../../../../utils/loading_widget.dart';

class ItemInvoiceDialog extends StatefulWidget {
  final bool isReturned;
  final bool isPurchase;

  const ItemInvoiceDialog(
      {super.key, required this.isReturned, required this.isPurchase});

  @override
  State<StatefulWidget> createState() => _BottomDialog();
}

class _BottomDialog extends State<ItemInvoiceDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  @override
  void initState() {
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  bool loading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: Text(
            widget.isReturned
                ? context.isEng
                    ? 'New Return Invoice'
                    : 'فاتورة مرتجعات جديدة'
                : widget.isPurchase
                    ? context.isEng
                        ? 'New Purchase Invoice'
                        : 'فاتورة مشتريات جديدة'
                    : context.isEng
                        ? 'New Invoice'
                        : 'فاتورة جديدة',
            style: const TextStyle(
              color: Colors.black,
            )),
      ),
      backgroundColor: Colors.white,
      body: WillPopScope(
        onWillPop: () async {
          Navigator.pop(context);
          addedItems.clear();
          addedMap.clear();
          return true;
        },
        child: Stack(
          children: <Widget>[
            Positioned.fill(
              child: ScaleTransition(
                scale: scaleAnimation,
                child: Consumer<ProductsProvider>(
                    builder: (context, productData, child) {
                  if (productData.products == null) {
                    return Center(
                      child: Text(context.isEng
                          ? 'No products available!'
                          : 'لا يوجد منتجات !'),
                    );
                  }

                  if (productData.products!.isEmpty) {
                    return const Center(
                      child: LoadingWidget(),
                    );
                  }

                  return Container(
                    padding: const EdgeInsets.only(top: 10),
                    child: InvoiceViewItemsWidget(
                      productData: productData,
                      isPurchase: widget.isPurchase,
                      isReturned: widget.isReturned,
                    ),
                  );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
