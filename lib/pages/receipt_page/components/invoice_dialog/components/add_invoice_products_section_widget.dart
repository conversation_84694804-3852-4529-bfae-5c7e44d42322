import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/product_model.dart';
import 'package:mandob/models/quantity_model.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/pages/receipt_page/components/invoice_dialog/components/switches.dart';
import 'package:mandob/pages/repositories_page/transfer_repository_items/components/add_transfer_items_widget.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/extensions.dart';
import 'package:mandob/utils/text_field.dart';

class AddInvoiceProductsSection extends StatelessWidget {
  final bool isPurchase;
  final List<ProductModel> products;
  final List<ProductModel> filteredProducts;
  final RepositoryModel? filteredRepositoryListMap;
  final List<QuantityModel> quantities;
  final Map<String, dynamic> addedMap;
  final Map<String, TextEditingController> priceController;
  final Map<String, bool> isNormalPrice;
  final Map<String, bool> isQuantity;
  final Function(String, int?, ProductModel) onChangedQuantityField;
  final Function(String, ProductModel) onChangedPriceField;
  final Function(String, int, ProductModel) onChangedWeightField;
  final Function(bool?, ProductModel, int?, dynamic, Map<dynamic, dynamic>?)
      onChangedCheckBox;
  final Function(ProductModel, String) changeProductPrice;
  final String Function(ProductModel) viewedPrice;
  final dynamic user;

  const AddInvoiceProductsSection({
    super.key,
    required this.isPurchase,
    required this.products,
    required this.filteredProducts,
    required this.filteredRepositoryListMap,
    required this.quantities,
    required this.addedMap,
    required this.priceController,
    required this.isNormalPrice,
    required this.isQuantity,
    required this.onChangedQuantityField,
    required this.onChangedPriceField,
    required this.onChangedWeightField,
    required this.onChangedCheckBox,
    required this.changeProductPrice,
    required this.viewedPrice,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    Widget quantityFieldWidget(int? prodQuant, ProductModel product) {
      return Padding(
        padding: const EdgeInsets.only(top: 6.0),
        child: TextFieldWidget(
          label: context.isEng ? 'Product Quantity' : 'كمية المنتج',
          textAlign: TextAlign.left,
          textInputType: TextInputType.number,
          contentPadding: const EdgeInsets.all(10),
          initialValue:
              prodQuant == null || (prodQuant.toString() == '0' && !isPurchase)
                  ? '0'
                  : '1',
          onChanged: (val) {
            onChangedQuantityField(val, prodQuant, product);
          },
        ),
      );
    }

    Widget weightFieldWidget(int weight, ProductModel product) {
      return SizedBox(
        width: 150,
        child: Padding(
          padding: const EdgeInsets.only(top: 6.0),
          child: TextFieldWidget(
            enabled: isQuantity[product.id] == false,
            label: context.isEng ? 'Weight (g)' : 'الوزن (جم)',
            textAlign: TextAlign.left,
            textInputType: TextInputType.number,
            contentPadding: const EdgeInsets.all(10),
            initialValue: '1',
            onChanged: (val) {
              onChangedWeightField(val, weight, product);
            },
          ),
        ),
      );
    }

    Widget priceFieldWidget(int? prodQuant, ProductModel product, dynamic user,
        Map<dynamic, dynamic>? myRepoProducts) {
      if (priceController[product.id!]?.text == '0') {
        priceController[product.id!]!.text = viewedPrice(product);
      }

      return SizedBox(
        width: 150,
        child: Padding(
          padding: const EdgeInsets.only(top: 10.0),
          child: TextFieldWidget(
            controller: priceController[product.id!],
            label: context.isEng ? 'Product Price' : 'سعر المنتج',
            textAlign: TextAlign.left,
            textInputType: TextInputType.number,
            contentPadding: const EdgeInsets.all(10),
            onChanged: (val) {
              onChangedPriceField(val, product);
            },
          ),
        ),
      );
    }

    return Expanded(
      child: ListView.separated(
        itemCount: isPurchase ? products.length : filteredProducts.length,
        separatorBuilder: (context, index) {
          return const Padding(
            padding: EdgeInsets.only(left: 80, right: 80),
            child: Divider(),
          );
        },
        itemBuilder: (context, index) {
          ProductModel product =
              isPurchase ? products[index] : filteredProducts[index];
          var myRepoProducts = filteredRepositoryListMap != null
              ? filteredRepositoryListMap!.products
              : {};

          var prodQuant;
          var weight = 1;

          if (filteredRepositoryListMap != null) {
            var filtered = quantities.where((element) {
              return filteredRepositoryListMap!.products != null &&
                  filteredRepositoryListMap!.products!
                      .containsKey(element.productId) &&
                  element.productId == product.id;
            }).toList();

            prodQuant = filtered.isNotEmpty
                ? filtered[0].quantities![filteredRepositoryListMap!.id]
                : '0';
          }

          return ViewItemsWidget(
            quantityFieldWidget: addedMap.containsKey(product.id)
                ? quantityFieldWidget(prodQuant, product)
                : const SizedBox.shrink(),
            priceFieldWidget: addedMap.containsKey(product.id)
                ? priceFieldWidget(prodQuant, product, user, myRepoProducts)
                : const SizedBox.shrink(),
            weightFieldWidget: addedMap.containsKey(product.id)
                ? weightFieldWidget(weight, product)
                : const SizedBox.shrink(),
            productData: product,
            prodQuant: prodQuant,
            prodPrice: user.isAdmin!
                ? product.price!.roundedPrecisionToString(5)
                : isPurchase
                    ? product.price!.roundedPrecisionToString(5)
                    : myRepoProducts![product.id].toString(),
            checkBoxWidget: Checkbox(
              activeColor: ColorManager.primaryColor,
              value: addedMap.containsKey(product.id),
              onChanged: (val) {
                onChangedCheckBox(
                    val, product, prodQuant, user, myRepoProducts ?? {});
              },
            ),
            changePriceWidget: addedMap.containsKey(product.id)
                ? PriceSwitchWidget(
                    // value: isNormalPrice[product.id!] ?? true,
                    onToggle: (val) {
                      isNormalPrice[product.id!] = val;
                      changeProductPrice(product, viewedPrice(product));
                    },
                  )
                : const SizedBox(),
            changeWeightWidget: addedMap.containsKey(product.id)
                ? Padding(
                    padding: const EdgeInsets.only(top: 15.0),
                    child: WeightSwitchWidget(
                      // value: isQuantity[product.id!] ?? true,
                      onToggle: (val) {
                        isQuantity[product.id!] = val;
                        onChangedWeightField('0', 0, product);
                      },
                    ),
                  )
                : const SizedBox(),
          );
        },
      ),
    );
  }
}
