import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/utils/switch_tab_bar_widget.dart';

class PriceSwitchWidget extends StatelessWidget {
  const PriceSwitchWidget({
    super.key,
    required this.onToggle,
  });

  final Function(bool) onToggle;

  @override
  Widget build(BuildContext context) {
    return BaseSwitchTabBar(
      tabs: [
        context.isEng ? 'Unit' : 'قطاعي',
        context.isEng ? 'Total' : 'جملة'
      ],
      onTabChange: (index) {
        onToggle(index == 0);
      },
    );
  }
}

class WeightSwitchWidget extends StatelessWidget {
  const WeightSwitchWidget({
    super.key,
    required this.onToggle,
  });

  final Function(bool) onToggle;

  @override
  Widget build(BuildContext context) {
    return BaseSwitchTabBar(
      tabs: [
        context.isEng ? 'Quantity' : 'كمية',
        context.isEng ? 'Weight' : 'وزن'
      ],
      onTabChange: (index) {
        onToggle(index == 0);
      },
    );
  }
}
