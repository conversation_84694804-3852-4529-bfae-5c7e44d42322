import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/core/shared/drop_down_fields/customers_drop_down.dart';
import 'package:mandob/core/shared/drop_down_fields/repositories_drop_down.dart';
import 'package:mandob/core/shared/drop_down_fields/suppliers_drop_down.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/models/supplier_model.dart';
import 'package:mandob/utils/switch_tab_bar_widget.dart';

class AddInvoiceTopSectionWidget extends StatelessWidget {
  final bool isAdmin;
  final bool isPurchase;
  final String repoVal;
  final bool visible;
  final Function(String?) onChangedRepoVal;
  final Function() onTapRepo;
  final Function(CustomerModel?) onChangedCustomer;
  final Function(SupplierModel?) onChangedSupplier;
  final Function(int) onTabChange;
  final CustomerModel? customer;
  final SupplierModel? supplier;

  const AddInvoiceTopSectionWidget({
    super.key,
    required this.isAdmin,
    required this.isPurchase,
    required this.repoVal,
    required this.visible,
    required this.onChangedRepoVal,
    required this.onTapRepo,
    required this.onChangedCustomer,
    required this.onChangedSupplier,
    required this.onTabChange,
    this.customer,
    this.supplier,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.all(isAdmin ? 4 : 4),
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: Row(
              mainAxisAlignment: isPurchase && !isAdmin
                  ? MainAxisAlignment.center
                  : MainAxisAlignment.spaceEvenly,
              children: [
                /// Choose repository
                RepositoriesDropDownButton(
                  repoVal: repoVal,
                  visible: visible,
                  onChanged: onChangedRepoVal,
                  onTap: onTapRepo,
                ),

                /// Choose Customers
                !isPurchase
                    ? CustomersDropDownButton(
                        width: MediaQuery.sizeOf(context).width - 50,
                        onChanged: onChangedCustomer,
                        customer: customer,
                      )
                    : const SizedBox.shrink(),

                /// Choose Supplier
                isPurchase
                    ? SuppliersDropDownButton(
                        width: MediaQuery.sizeOf(context).width - 50,
                        onChanged: onChangedSupplier,
                        supplier: supplier,
                      )
                    : Container(),
              ],
            ),
          ),
        ),

        const SizedBox(
          height: 15,
        ),

        // Choose total type
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(
              width: 15,
            ),
            Text(context.isEng ? 'Total By: ' : 'حساب الإجمالي: '),
            const SizedBox(
              width: 15,
            ),
            Expanded(
              child: BaseSwitchTabBar(
                tabs: [
                  context.isEng ? 'Product Price' : 'لسعر المنتج',
                  context.isEng ? 'Paid' : 'للمدفوع',
                ],
                onTabChange: onTabChange,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
