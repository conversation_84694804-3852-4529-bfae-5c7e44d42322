import 'dart:async';

import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/models/product_model.dart';
import 'package:mandob/models/quantity_model.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/models/supplier_model.dart';
import 'package:mandob/pages/receipt_page/components/add_purachase_invoice/add_purchase_invoice_dialog.dart';
import 'package:mandob/pages/receipt_page/components/invoice_dialog/components/add_invoice_top_section_widget.dart';
import 'package:mandob/pages/repositories_page/transfer_repository_items/transfer_table/main_transfer_table.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/extensions.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:mandob/utils/text_field.dart';
import 'package:persian_number_utility/persian_number_utility.dart';
import 'package:provider/provider.dart';

import '../../../../../providers/company_provider.dart';
import '../../add_sales_invoice/add_sales_invoice_dialog.dart';
import 'add_invoice_products_section_widget.dart';

bool moreThanQuantity = false;

class InvoiceViewItemsWidget extends StatefulWidget {
  final ProductsProvider productData;
  final bool isPurchase;
  final bool isReturned;

  const InvoiceViewItemsWidget(
      {super.key,
      required this.productData,
      required this.isPurchase,
      required this.isReturned});

  @override
  State<InvoiceViewItemsWidget> createState() => _InvoiceViewItemsWidgetState();
}

class _InvoiceViewItemsWidgetState extends State<InvoiceViewItemsWidget> {
  String repoVal = '';

  List<RepositoryModel> repositoryListMap = [];

  String receiverRepository = '';

  late double totalItemsPrice = 0.0;

  RepositoryModel? filteredRepositoryListMap;

  bool visible = false;

  bool loaded = true;

  List<ProductModel> filteredProducts = [];

  addLocalData(repo, context, user) async {
    if (repoVal == '' && repo.repositories.isNotEmpty) {
      repoVal = repo.repositories[0].name.toString();
      filteredRepositoryListMap = repo.repositories.firstWhere((val) {
        return val.name == repoVal;
      });

      repoVal = filteredRepositoryListMap!.name.toString();

      filteredProducts = widget.productData.products!.where((element) {
        return filteredRepositoryListMap!.products == null
            ? false
            : filteredRepositoryListMap!.products!.containsKey(element.id);
      }).toList();
    }
    final repositories =
        Provider.of<RepositoryProvider>(context, listen: false).repositories;

    if (!user.activeUser!.isAdmin!) {
      filteredRepositoryListMap = repositories.firstWhere(
          (element) => element.id == user.activeUser.storeId,
          orElse: () => RepositoryModel.fromJson({"": ""}));
      final Map<String, dynamic> myProdIds =
          filteredRepositoryListMap!.products ?? {};

      filteredProducts = widget.productData.products!
          .where(
            (element) => myProdIds.containsKey(element.id),
          )
          .toList();
    } else {
      filteredProducts = widget.productData.products!.where((element) {
        if (filteredRepositoryListMap!.products == null) {
          showBar(context, 'لا يوجد منتجات في هذا المخزن !');
        }
        return filteredRepositoryListMap!.products!.containsKey(element.id);
      }).toList();

      if (receiverRepository == '' && repo.repositories.isNotEmpty) {
        receiverRepository = repo.repositories[0].name.toString();
      }
      if (repositoryListMap.isEmpty) {
        repositoryListMap = repo.repositories;
      }

      filteredRepositoryListMap = repositories.firstWhere(
          (element) => element.id == user.activeUser.storeId,
          orElse: () => RepositoryModel.fromJson({"": ""}));

      setState(() {});
    }
  }

  bool invalid = false;
  Timer? _timer;

  @override
  void dispose() {
    super.dispose();
    _timer!.cancel();
  }

  @override
  void initState() {
    super.initState();
    Provider.of<CompanyInfoProvider>(context, listen: false).fetchCompanyInfo();

    final repo = Provider.of<RepositoryProvider>(context, listen: false);
    final user = Provider.of<UserProvider>(context, listen: false);

    addLocalData(repo, context, user);

    _timer = Timer(const Duration(seconds: 7), () {
      if (mounted) {
        setState(() {
          loaded = false;
        });
      }
    });
  }

  CustomerModel? customer;

  SupplierModel? supplier;

  bool totalValueTypeByProductPrice = true;

  final Map<String, TextEditingController> priceController = {};

  @override
  Widget build(BuildContext context) {
    final repo = Provider.of<RepositoryProvider>(context, listen: false);
    final user = Provider.of<UserProvider>(context, listen: false).activeUser;

    return StatefulBuilder(builder: (context, setState) {
      final myRepo = repo.repositories.firstWhere(
          (element) => element.id == user!.storeId,
          orElse: () => RepositoryModel.fromJson({"": ""}));

      return !user!.isAdmin! && (myRepo.id == null)
          ? Center(
              child: Text(context.isEng
                  ? 'You don\'t have repository'
                  : 'أنت غير منتمي لمخزن'),
            )
          : Consumer<QuantitiesProvider>(
              builder: (context, _quantitiesData, child) {
              return FutureBuilder(
                  future: _quantitiesData.fetchQuantities(),
                  builder: (context, snapshot) {
                    if (_quantitiesData.quantities.isEmpty) {
                      Center(
                        child: Text(
                            context.isEng ? 'No Products' : 'لا يوجد منتجات'),
                      );
                    }
                    final List<QuantityModel> _quantities =
                        _quantitiesData.quantities;

                    return Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              AddInvoiceTopSectionWidget(
                                isAdmin: user.isAdmin ?? false,
                                isPurchase: widget.isPurchase,
                                repoVal: repoVal,
                                visible: visible,
                                onChangedRepoVal: (val) {
                                  if (repoVal != val) {
                                    addedItems.clear();
                                    addedMap.clear();
                                  }
                                  setState(() {
                                    onChangedRepoVal(val, repo);
                                  });
                                },
                                onTapRepo: () {
                                  setState(() {
                                    filteredRepositoryListMap =
                                        repo.repositories.firstWhere((val) {
                                      return val.name == repoVal;
                                    });
                                    visible = true;
                                  });
                                },
                                onChangedCustomer: (val) {
                                  setState(() {
                                    customer = val!;
                                  });
                                },
                                onChangedSupplier: (val) {
                                  setState(() {
                                    supplier = val;
                                  });
                                },
                                onTabChange: (index) {
                                  setState(() {
                                    addedItems.clear();
                                    addedMap.clear();
                                    totalValueTypeByProductPrice = index == 0;
                                  });
                                },
                                customer: customer,
                                supplier: supplier,
                              ),
                              Padding(
                                padding: const EdgeInsets.all(12),
                                child: Align(
                                    alignment: context.isEng
                                        ? Alignment.centerLeft
                                        : Alignment.centerRight,
                                    child: Text(
                                      context.isEng ? 'Products' : 'المنتجات',
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                    )),
                              ),

                              ///Choose products
                              !visible && user.isAdmin!
                                  ? const SizedBox(
                                      height: 25,
                                    )
                                  : AddInvoiceProductsSection(
                                      isPurchase: widget.isPurchase,
                                      products: widget.productData.products!,
                                      filteredProducts: filteredProducts,
                                      filteredRepositoryListMap:
                                          filteredRepositoryListMap,
                                      quantities: _quantities,
                                      addedMap: addedMap,
                                      priceController: priceController,
                                      isNormalPrice: isNormalPrice,
                                      isQuantity: isQuantity,
                                      onChangedQuantityField:
                                          onChangedQuantityField,
                                      onChangedPriceField: onChangedPriceField,
                                      onChangedWeightField:
                                          onChangedWeightField,
                                      onChangedCheckBox: onChangedCheckBox,
                                      changeProductPrice: changeProductPrice,
                                      viewedPrice: viewedPrice,
                                      user: user,
                                    )
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(bottom: 20.0),
                          child: GestureDetector(
                            onTap: () => onSubmit(),
                            child: Container(
                              height: 50,
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              decoration: BoxDecoration(
                                  color: ColorManager.primaryColor,
                                  borderRadius: BorderRadius.circular(15)),
                              child: Center(
                                child: Text(
                                  context.isEng
                                      ? 'Add Products'
                                      : 'إضافة المنتجات',
                                  style: const TextStyle(
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  });
            });
    });
  }

  void onChangedRepoVal(String? val, repo) {
    repoVal = val!;

    filteredRepositoryListMap =
        repo.repositories.firstWhere((element) => element.name == repoVal);

    filteredProducts = widget.productData.products!.where((element) {
      if (filteredRepositoryListMap!.products == null) {
        showBar(
            context,
            context.isEng
                ? 'No products in this repository!'
                : 'لا يوجد منتجات في هذا المخزن !');
      }
      return filteredRepositoryListMap!.products!.containsKey(element.id);
    }).toList();
  }

  void onChangedPriceField(String val, ProductModel product) {
    if (double.tryParse(val.toEnglishDigit().toString()) == null) {
      setState(() {
        invalid = true;
      });
      return;
    }

    setState(() {
      invalid = false;
      moreThanQuantity = false;
      changeProductPrice(product, val);
    });
  }

  void changeProductPrice(ProductModel product, String price) {
    for (var element in addedItems) {
      if (element['id'] == product.id) {
        double parsedPrice =
            double.tryParse(price.toEnglishDigit().toString())!;
        if (totalValueTypeByProductPrice) {
          element['price'] = parsedPrice;
          element['paidPrice'] = parsedPrice;
        } else {
          element['paidPrice'] = parsedPrice;
          var totalAddedPrice = parsedPrice;
          final info = Provider.of<CompanyInfoProvider>(context, listen: false)
              .companyInfo;
          var itemAllPrice = totalAddedPrice / (info!.taxPercent ?? 1.15);
          var itemPrice = itemAllPrice / element['quantity'];
          element['price'] = itemPrice;
        }
      }
    }
  }

  void onChangedQuantityField(
      String val, int? prodQuant, ProductModel product) {
    if (int.tryParse(val.toEnglishDigit().toString()) == null) {
      setState(() {
        invalid = true;
      });
      return;
    }

    setState(() {
      invalid = false;
    });

    if (prodQuant != null &&
        !widget.isPurchase &&
        int.tryParse(val.toEnglishDigit().toString())! > prodQuant) {
      setState(() {
        moreThanQuantity = true;
      });
      showBar(
          context,
          context.isEng
              ? 'Please select a quantity less than or equal to the product quantity!'
              : 'يجب اختيار كمية أقل من أو تساوي كمية المنتج !');
      return;
    }

    setState(() {
      moreThanQuantity = false;
      addedMap[product.id.toString()] =
          int.tryParse(val.toEnglishDigit().toString());

      for (var element in addedItems) {
        if (element['id'] == product.id) {
          element['quantity'] = int.tryParse(val.toEnglishDigit().toString());
          if (!totalValueTypeByProductPrice) {
            var totalAddedPrice = element['paidPrice'];
            final info =
                Provider.of<CompanyInfoProvider>(context, listen: false)
                    .companyInfo;
            var itemAllPrice = totalAddedPrice! / (info!.taxPercent ?? 1.15);
            var itemPrice = itemAllPrice / element['quantity'];
            element['price'] = itemPrice;
          }
        }
      }
    });
  }

  void onChangedWeightField(String val, int weight, ProductModel product) {
    if (int.tryParse(val.toEnglishDigit().toString()) == null) {
      setState(() {
        invalid = true;
      });
      return;
    }

    setState(() {
      invalid = false;
      weight = int.tryParse(val.toEnglishDigit().toString())!;
      for (var element in addedItems) {
        if (element['id'] == product.id) {
          element['weight'] = weight;
        }
      }
    });
  }

  Widget quantityFieldWidget(int? prodQuant, ProductModel product) {
    return Padding(
      padding: const EdgeInsets.only(top: 6.0),
      child: TextFieldWidget(
        label: context.isEng ? 'Product Quantity' : 'كمية المنتج',
        textAlign: TextAlign.left,
        textInputType: TextInputType.number,
        contentPadding: const EdgeInsets.all(10),
        initialValue: prodQuant == null ||
                (prodQuant.toString() == '0' && !widget.isPurchase)
            ? '0'
            : '1',
        onChanged: (val) {
          onChangedQuantityField(val, prodQuant, product);
        },
      ),
    );
  }

  Widget weightFieldWidget(int weight, ProductModel product) {
    return SizedBox(
      width: 150,
      child: Padding(
        padding: const EdgeInsets.only(top: 6.0),
        child: TextFieldWidget(
          enabled: isQuantity[product.id] == false,
          label: context.isEng ? 'Weight (g)' : 'الوزن (جم)',
          textAlign: TextAlign.left,
          textInputType: TextInputType.number,
          contentPadding: const EdgeInsets.all(10),
          initialValue: '1',
          onChanged: (val) {
            onChangedWeightField(val, weight, product);
          },
        ),
      ),
    );
  }

  Widget priceFieldWidget(int? prodQuant, ProductModel product, user,
      Map<dynamic, dynamic>? myRepoProducts) {
    if (priceController[product.id!]?.text == '0') {
      priceController[product.id!]!.text = viewedPrice(product);
    }

    return SizedBox(
      width: 150,
      child: Padding(
        padding: const EdgeInsets.only(top: 10.0),
        child: TextFieldWidget(
          controller: priceController[product.id!],
          label: totalValueTypeByProductPrice
              ? (context.isEng ? 'Product Price' : 'سعر المنتج')
              : (context.isEng ? 'Paid' : 'المدفوع'),
          textAlign: TextAlign.left,
          textInputType: TextInputType.number,
          contentPadding: const EdgeInsets.all(10),
          onChanged: (val) {
            onChangedPriceField(val, product);
          },
        ),
      ),
    );
  }

  void onChangedCheckBox(bool? newValue, ProductModel product, int? prodQuant,
      user, Map<dynamic, dynamic>? myRepoProducts) {
    setState(() {
      if (prodQuant == null || prodQuant.toString() == '0') {
        showBar(
            context,
            context.isEng
                ? 'Product quantity not available!'
                : 'كمية المنتج غير متوفرة !');
        return;
      }
      if (addedMap.containsKey(product.id)) {
        addedMap.remove(product.id);
        addedItems.removeWhere((element) => element['id'] == product.id);
        priceController.remove(product.id);
      } else {
        final price = user.isAdmin!
            ? viewedPrice(product)
            : widget.isPurchase
                ? viewedPrice(product)
                : totalValueTypeByProductPrice
                    ? myRepoProducts![product.id].toString()
                    : '0';
        priceController[product.id!] = TextEditingController(text: price);
        addedMap[product.id.toString()] = 1;
        addedItems.add({
          'id': product.id,
          'name': product.name,
          'price': double.tryParse(price),
          'type': product.type,
          'paidPrice': double.tryParse(price),
          'quantity': 1,
          'weight': isQuantity[product.id] == false ? 1 : 0,
        });
      }
    });
  }

  String viewedPrice(ProductModel product) {
    final price = product.price!.roundedPrecisionToString(5);
    final bulkPrice = product.bulkPrice == null
        ? price
        : product.bulkPrice!.roundedPrecisionToString(5);

    final viewedPrice = isNormalPrice[product.id!] == true ? price : bulkPrice;

    return viewedPrice;
  }

  void onSubmit() {
    if (_validateInputs()) {
      if (widget.isPurchase) {
        _handlePurchaseInvoice();
      } else {
        _handleSalesInvoice();
      }
    }
  }

  bool _validateInputs() {
    for (var element in addedItems) {
      if (element['price'] == 0) {
        invalid = true;
        break;
      }
    }

    if (invalid) {
      showBar(
          context,
          context.isEng
              ? 'Please enter valid quantity and price!'
              : 'برجاء إدخال الكمية والسعر بشكل صحيح !');
      return false;
    }

    if (widget.isPurchase && supplier == null) {
      showBar(
          context,
          context.isEng
              ? 'Please select a supplier!'
              : 'برجاء اختيار المورد !');
      return false;
    }

    if (!widget.isPurchase && customer == null) {
      showBar(
          context,
          context.isEng
              ? 'Please select a customer!'
              : 'برجاء اختيار العميل !');
      return false;
    }

    if (moreThanQuantity && !widget.isPurchase) {
      showBar(
          context,
          context.isEng
              ? 'Please select a quantity less than or equal to the product quantity!'
              : 'برجاء اختيار كمية أقل من أو تساوي كمية المنتج !');
      return false;
    }

    if (addedMap.isEmpty || addedItems.isEmpty) {
      showBar(
          context,
          context.isEng
              ? 'Please select products first!'
              : 'برجاء اختيار المنتجات أولا');
      return false;
    }

    return true;
  }

  void _handlePurchaseInvoice() {
    Navigator.pop(context);
    showDialog(
      context: context,
      builder: (_) => AddPurchaseInvoiceDialog(
        byProductPrice: totalValueTypeByProductPrice,
        isReturned: widget.isReturned,
        senderRepo: filteredRepositoryListMap!.id.toString(),
        supplier: supplier ?? SupplierModel(),
      ),
    );
  }

  void _handleSalesInvoice() {
    Navigator.pop(context);
    showDialog(
      context: context,
      builder: (_) => AddSalesInvoiceDialog(
        byProductPrice: totalValueTypeByProductPrice,
        isReturned: widget.isReturned,
        senderRepoId: filteredRepositoryListMap!.id.toString(),
        customerData: customer,
      ),
    );
  }
}
