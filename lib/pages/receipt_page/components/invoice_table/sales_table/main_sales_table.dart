import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/models/invoice_model.dart';
import 'package:mandob/pages/main_components/invoice_floating_buttons.dart';
import 'package:mandob/pages/receipt_page/components/add_sales_invoice/components/top_section.dart';
import 'package:mandob/pages/receipt_page/components/invoice_dialog/main_invoice_dialog.dart';
import 'package:mandob/pages/receipt_page/components/row_invoice_widget.dart';
import 'package:mandob/providers/customers_provider.dart';
import 'package:mandob/theme.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/shared/drop_down_fields/customers_drop_down.dart';
import '../../../../../providers/invoices_provider.dart';

bool scrollingSalesInvoices = false;

class SalesInvoicesTable extends StatefulWidget {
  final bool isReturned;
  final String? title;

  const SalesInvoicesTable({
    super.key,
    required this.isReturned,
    this.title,
  });

  @override
  State<SalesInvoicesTable> createState() => _SalesInvoicesTableState();
}

class _SalesInvoicesTableState extends State<SalesInvoicesTable> {
  List<InvoiceModel> invoicesData = [];
  bool loading = false;

  Future<void> _getInvoiceData() async {
    final invoicesProvider = Provider.of<InvoicesProvider>(
      context,
      listen: false,
    );

    setState(() {
      scrollingSalesInvoices = true;
      paginationLimit = 100;
      loading = true;
    });

    invoicesData = await invoicesProvider.fetchInvoices(
      isSales: true,
      isReturned: widget.isReturned,
      paginationLimit: paginationLimit,
      lastDocId: invoicesData.last.docId,
      customerId: customer?.id,
    );
    setState(() {
      loading = false;
    });
  }

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    scrollingSalesInvoices = false;
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        _getInvoiceData();
      }
    });
  }

  bool isSearching = false;

  int? paginationLimit;

  CustomerModel? customer;

  void onSearchCancel() {
    isSearching = false;
    setState(() {
      customer = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InvoicesProvider>(
      builder: (context, invoicesProvider, child) {
        return Scaffold(
            appBar: PreferredSize(
              preferredSize: Size.fromHeight(widget.title != null ? 140 : 100),
              child: AppBar(
                  automaticallyImplyLeading: false,
                  backgroundColor: Colors.transparent,
                  surfaceTintColor: Colors.transparent,
                  elevation: 0,
                  flexibleSpace: Column(
                    children: [
                      if (widget.title != null)
                        salesInvoiceAppBar(context, title: widget.title!),
                      Padding(
                        padding: const EdgeInsets.only(
                            top: 15.0, right: 30, left: 30),
                        child: Row(
                          children: [
                            Expanded(
                              child: Consumer<CustomersProvider>(
                                builder: (context, customerData, child) {
                                  return CustomersDropDownButton(
                                    width:
                                        MediaQuery.sizeOf(context).width - 100,
                                    onChanged: (val) {
                                      setState(() {
                                        customer = val!;
                                        scrollingSalesInvoices = false;
                                      });
                                    },
                                    customer: customer,
                                  );
                                },
                              ),
                            ),
                            if (customer != null) ...[
                              AppGaps.gap8,
                              IconButton(
                                onPressed: () {
                                  setState(() {
                                    onSearchCancel();
                                    invoicesProvider.fetchInvoices(
                                      isSales: true,
                                      isReturned: widget.isReturned,
                                      customerId: customer?.id,
                                    );
                                  });
                                },
                                icon: const Icon(
                                  Icons.close,
                                  color: Colors.black54,
                                ),
                              ),
                            ]
                          ],
                        ),
                      ),
                    ],
                  )),
            ),
            floatingActionButton: widget.isReturned
                ? Container()
                : FloatingButtonWidget(
                    onPressed: () async {
                      await showGeneralDialog(
                        context: context,
                        barrierDismissible: false,
                        barrierLabel: MaterialLocalizations.of(context)
                            .modalBarrierDismissLabel,
                        pageBuilder: (context, _, __) =>
                            const ItemInvoiceDialog(
                          isReturned: false,
                          isPurchase: false,
                        ),
                      );
                    },
                    label: context.isEng ? 'New Invoice' : 'فاتورة جديدة',
                  ),
            body: Column(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 0.0),
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Padding(
                                padding: EdgeInsets.only(
                                  left: context.isEng ? 35.0 : 0.0,
                                  right: context.isEng ? 0.0 : 35.0,
                                ),
                                child: Text(
                                  "#",
                                  style: Them.tableHeader,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                  left: context.isEng ? 10.0 : 0.0,
                                  right: context.isEng ? 0.0 : 10.0,
                                ),
                                child: Text(
                                  context.isEng ? "Date" : "التاريخ",
                                  style: Them.tableHeader,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                  left: context.isEng ? 30.0 : 0.0,
                                  right: context.isEng ? 0.0 : 10.0,
                                ),
                                child: Text(
                                  context.isEng ? "Customer" : "العميل",
                                  style: Them.tableHeader,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                  right: context.isEng ? 0.0 : 10.0,
                                ),
                                child: Text(
                                  context.isEng ? "Discount" : "الخصم",
                                  style: Them.tableHeader,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Text(
                                context.isEng ? "Total" : "الاجمالى",
                                style: Them.tableHeader,
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(
                                width: 5,
                              ),
                            ],
                          ),
                        ),
                        const Divider(
                          color: ColorManager.primaryColor,
                          thickness: 2,
                          height: 0,
                        ),
                        invoicesWidget(
                          invoicesProvider: invoicesProvider,
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ));
      },
    );
  }

  Widget invoicesWidget({required InvoicesProvider invoicesProvider}) {
    return Expanded(
      child: FutureBuilder(
          future: invoicesProvider.fetchInvoices(
            isSales: true,
            isReturned: widget.isReturned,
            paginationLimit: paginationLimit,
            customerId: customer?.id,
          ),
          builder: (context, snapshot) {
            if (!snapshot.hasData) {
              return const LoadingWidget();
            }

            if (!scrollingSalesInvoices) {
              invoicesData = snapshot.data as List<InvoiceModel>;
            }

            invoicesData.sort(
                (a, b) => b.createdAt?.compareTo((a.createdAt ?? '')) ?? 0);

            return Stack(
              children: [
                SingleChildScrollView(
                  padding: const EdgeInsets.only(bottom: 10),
                  child: Table(
                      defaultVerticalAlignment:
                          TableCellVerticalAlignment.middle,
                      columnWidths: const {
                        0: FlexColumnWidth(2.2),
                        1: FlexColumnWidth(1.6),
                        2: FlexColumnWidth(1.2),
                        3: FlexColumnWidth(1.4),
                      },
                      children: [
                        TableRow(children: [
                          SizedBox(
                            height: MediaQuery.of(context).size.height - 300,
                            child: ListView.separated(
                              controller: _scrollController,
                              shrinkWrap: true,
                              itemCount: invoicesData.length,
                              physics: const BouncingScrollPhysics(),
                              separatorBuilder: (context, index) {
                                return const Divider(
                                  color: ColorManager.lightFieldColor,
                                  thickness: 1,
                                  height: 0,
                                );
                              },
                              itemBuilder: (context, index) {
                                return RowInvoiceWidget(
                                  invoice: invoicesData[index],
                                  isReturned: widget.isReturned,
                                  isPurchase: false,
                                );
                              },
                            ),
                          ),
                        ])
                      ]),
                ),
                if (loading) ...[
                  const LoadingWidget(),
                ]
              ],
            );
          }),
    );
  }
}
