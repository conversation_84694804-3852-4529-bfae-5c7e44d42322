import 'package:bubble_tab_indicator/bubble_tab_indicator.dart';
import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/providers/invoices_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:provider/provider.dart';

import 'main_sales_table.dart';

class MainSalesTable extends StatefulWidget {
  const MainSalesTable({Key? key}) : super(key: key);

  @override
  State<MainSalesTable> createState() => _MainSalesTableState();
}

class _MainSalesTableState extends State<MainSalesTable>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;

  @override
  void initState() {
    _tabController = TabController(vsync: this, length: 2);
    super.initState();
  }

  @override
  void dispose() {
    _tabController!.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InvoicesProvider>(builder: (context, invoicesData, child) {
      return DefaultTabController(
        length: 2,
        initialIndex: 0,
        child: Scaffold(
            backgroundColor: Colors.white,
            appBar: PreferredSize(
              preferredSize:
                  Size.fromHeight(MediaQuery.of(context).size.height / 13),
              child: AppBar(
                automaticallyImplyLeading: false,
                backgroundColor: Colors.white,
                elevation: 0.0,
                bottom: TabBar(
                  unselectedLabelColor: Colors.black87,
                  controller: _tabController,
                  padding: const EdgeInsets.symmetric(horizontal: 40),
                  isScrollable: false,
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicator: const BubbleTabIndicator(
                    indicatorHeight: 40,
                    indicatorColor: ColorManager.secondaryColor,
                    tabBarIndicatorSize: TabBarIndicatorSize.tab,
                  ),
                  tabs: [
                    Tab(text: context.isEng ? 'Sales' : 'المبيعات'),
                    Tab(text: context.isEng ? 'Returns' : 'المرتجعات'),
                  ],
                ),
              ),
            ),
            body: TabBarView(
              controller: _tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: const [
                SalesInvoicesTable(
                  isReturned: false,
                ),
                SalesInvoicesTable(
                  isReturned: true,
                )
              ],
            )),
      );
    });
  }
}
