import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/invoice_model.dart';
import 'package:mandob/models/supplier_model.dart';
import 'package:mandob/pages/main_components/invoice_floating_buttons.dart';
import 'package:mandob/pages/receipt_page/components/add_sales_invoice/components/top_section.dart';
import 'package:mandob/pages/receipt_page/components/row_invoice_widget.dart';
import 'package:mandob/providers/invoices_provider.dart';
import 'package:mandob/theme.dart';
import 'package:provider/provider.dart';

import '../../../../../core/shared/drop_down_fields/suppliers_drop_down.dart';
import '../../../../../utils/color_manager.dart';
import '../../../../../utils/loading_widget.dart';
import '../../invoice_dialog/main_invoice_dialog.dart';

bool scrollingPurchaseInvoices = false;

class PurchaseInvoiceTable extends StatefulWidget {
  final bool isReturned;
  final String? title;

  const PurchaseInvoiceTable({super.key, required this.isReturned, this.title});

  @override
  State<PurchaseInvoiceTable> createState() => _PurchaseInvoiceTableState();
}

class _PurchaseInvoiceTableState extends State<PurchaseInvoiceTable> {
  final ScrollController _scrollController = ScrollController();

  List<InvoiceModel> invoicesData = [];
  bool loading = false;

  Future<void> _getInvoiceData() async {
    final invoicesProvider = Provider.of<InvoicesProvider>(
      context,
      listen: false,
    );
    setState(() {
      scrollingPurchaseInvoices = true;
      paginationLimit = 100;
      loading = true;
    });

    invoicesData = await invoicesProvider.fetchInvoices(
      isSales: false,
      isReturned: widget.isReturned,
      paginationLimit: paginationLimit,
      lastDocId: invoicesData.last.docId,
    );
    setState(() {
      loading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    scrollingPurchaseInvoices = false;

    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        _getInvoiceData();
      }
    });
  }

  bool isSearching = false;
  String filter = "";

  final TextEditingController _searchController = TextEditingController();
  int? paginationLimit;

  void onSearchCancel() {
    isSearching = false;
    filter = "";
    _searchController.clear();
    FocusScope.of(context).unfocus();
  }

  SupplierModel? supplier;

  @override
  Widget build(BuildContext context) {
    return Consumer<InvoicesProvider>(
      builder: (context, invoicesProvider, child) {
        return Scaffold(
            appBar: PreferredSize(
              preferredSize: Size.fromHeight(widget.title != null ? 140 : 100),
              child: AppBar(
                  automaticallyImplyLeading: false,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  flexibleSpace: Column(
                    children: [
                      if (widget.title != null)
                        salesInvoiceAppBar(context, title: widget.title!),
                      Padding(
                        padding: const EdgeInsets.only(
                            top: 15.0, right: 30, left: 30),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              height: 55,
                              child: SuppliersDropDownButton(
                                width: isSearching
                                    ? MediaQuery.of(context).size.width - 120
                                    : MediaQuery.of(context).size.width - 70,
                                supplier: supplier,
                                onChanged: (val) {
                                  setState(() {
                                    supplier = val;
                                    isSearching = true;
                                  });
                                },
                              ),
                            ),
                            if (isSearching)
                              IconButton(
                                onPressed: () {
                                  setState(() {
                                    onSearchCancel();

                                    invoicesProvider.fetchInvoices(
                                      isSales: false,
                                      isReturned: widget.isReturned,
                                    );
                                  });
                                },
                                icon: const Icon(
                                  Icons.close,
                                  color: Colors.black54,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  )),
            ),
            floatingActionButton: widget.isReturned
                ? Container()
                : FloatingButtonWidget(
                    onPressed: () => showGeneralDialog(
                          context: context,
                          barrierDismissible: false,
                          barrierLabel: MaterialLocalizations.of(context)
                              .modalBarrierDismissLabel,
                          pageBuilder: (context, _, __) => ItemInvoiceDialog(
                            isReturned: widget.isReturned,
                            isPurchase: true,
                          ),
                        ),
                    label: context.isEng ? 'New Invoice' : 'فاتورة جديدة'),
            body: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Expanded(
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                          left: context.isEng ? 0.0 : 30.0,
                          right: context.isEng ? 30.0 : 0.0,
                          top: 10,
                          bottom: 10,
                        ),
                        child: Table(columnWidths: const {
                          0: FlexColumnWidth(1.6),
                          1: FlexColumnWidth(1.4),
                          2: FlexColumnWidth(1.6),
                          3: FlexColumnWidth(1.4),
                        }, children: [
                          TableRow(
                            children: [
                              Padding(
                                padding: EdgeInsets.only(
                                  left: context.isEng ? 0.0 : 0.0,
                                  right: context.isEng ? 0.0 : 20.0,
                                ),
                                child: Text(
                                  "#",
                                  style: Them.tableHeader,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                  left: context.isEng ? 0.0 : 0.0,
                                  right: context.isEng ? 0.0 : 8.0,
                                ),
                                child: Text(
                                  context.isEng ? "Date" : "التاريخ",
                                  style: Them.tableHeader,
                                  textAlign: context.isEng
                                      ? TextAlign.start
                                      : TextAlign.center,
                                ),
                              ),
                              Text(
                                context.isEng ? "Supplier" : "المورد",
                                style: Them.tableHeader,
                                textAlign: TextAlign.center,
                              ),
                              Text(
                                context.isEng ? "Total" : "الاجمالى",
                                style: Them.tableHeader,
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ]),
                      ),
                      const Divider(
                        color: ColorManager.primaryColor,
                        thickness: 2,
                        height: 0,
                      ),
                      invoicesWidget(
                        invoicesProvider: invoicesProvider,
                      ),
                    ],
                  ),
                )
              ],
            ));
      },
    );
  }

  Widget invoicesWidget({required InvoicesProvider invoicesProvider}) {
    return Expanded(
      child: FutureBuilder(
          future: invoicesProvider.fetchInvoices(
            isSales: false,
            isReturned: widget.isReturned,
            paginationLimit: paginationLimit,
          ),
          builder: (context, snapshot) {
            if (!snapshot.hasData) {
              return const Center(
                child: LoadingWidget(),
              );
            }

            if (!scrollingPurchaseInvoices && !isSearching) {
              invoicesData = snapshot.data as List<InvoiceModel>;
            }

            if (isSearching) {
              final allInvoices = snapshot.data as List<InvoiceModel>;
              invoicesData = allInvoices
                  .where((element) => element.customerId == supplier?.id)
                  .toList();
            }

            return Stack(
              children: [
                SingleChildScrollView(
                  child: Table(
                      defaultVerticalAlignment:
                          TableCellVerticalAlignment.middle,
                      columnWidths: const {
                        0: FlexColumnWidth(1.6),
                        1: FlexColumnWidth(1.4),
                        2: FlexColumnWidth(1.6),
                        3: FlexColumnWidth(1.4),
                      },
                      children: [
                        TableRow(children: [
                          SizedBox(
                            height: MediaQuery.of(context).size.height - 300,
                            child: ListView.builder(
                              controller: _scrollController,
                              shrinkWrap: true,
                              itemCount: invoicesData.length,
                              physics: const BouncingScrollPhysics(),
                              itemBuilder: (context, index) {
                                return RowInvoiceWidget(
                                  invoice: invoicesData[index],
                                  isReturned: widget.isReturned,
                                  // index: index,
                                  isPurchase: true,
                                );
                              },
                            ),
                          )
                        ])
                      ]),
                ),
                if (loading) ...[
                  const Align(
                      alignment: Alignment.bottomCenter,
                      child: LoadingWidget()),
                ]
              ],
            );
          }),
    );
  }
}
