import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/utils/switch_tab_bar_widget.dart';

class DiscountSwitchWidget extends StatelessWidget {
  const DiscountSwitchWidget({
    Key? key,
    required this.value,
    required this.onToggle,
  }) : super(key: key);

  final bool value;
  final Function(bool) onToggle;

  @override
  Widget build(BuildContext context) {
    return BaseSwitchTabBar(
      tabs: context.isEng
          ? ['Value${context.currency}', 'Percentage %']
          : ['نسبة %', 'قيمة${context.currency}'],
      onTabChange: (index) {
        onToggle(index == 0);
      },
    );
  }
}
