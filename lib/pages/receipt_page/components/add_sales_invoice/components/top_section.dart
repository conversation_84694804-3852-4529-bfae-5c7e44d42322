import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:provider/provider.dart';

import '../../../../../providers/language.dart';
import '../../../../repositories_page/transfer_repository_items/transfer_table/main_transfer_table.dart';

AppBar salesInvoiceAppBar(
  BuildContext context, {
  required String title,
}) {
  final lang = Provider.of<LangProvider>(context, listen: false);

  return AppBar(
    title: Stack(
      alignment: Alignment.center,
      children: [
        GestureDetector(
          onTap: () {
            addedItems.clear();
            addedMap.clear();
            Navigator.pop(context);
          },
          child: Align(
            alignment:
                context.isEng ? Alignment.centerLeft : Alignment.centerRight,
            child: const CircleAvatar(
              backgroundColor: ColorManager.primaryColor,
              radius: 20,
              child: Icon(
                Icons.arrow_back_ios_outlined,
                color: Colors.white,
              ),
            ),
          ),
        ),
        Center(
          child: Text(
            title,
            style: const TextStyle(
                color: Colors.black, fontSize: 20, fontWeight: FontWeight.bold),
          ),
        ),
      ],
    ),
    automaticallyImplyLeading: false,
    backgroundColor: Colors.transparent,
    elevation: 0,
  );
}
