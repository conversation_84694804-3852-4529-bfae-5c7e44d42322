import 'dart:convert';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/collection_model.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/models/expense_model.dart';
import 'package:mandob/models/invoice_model.dart';
import 'package:mandob/models/operation_model.dart';
import 'package:mandob/models/quantity_model.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/pages/receipt_page/components/invoice_table/sales_table/main_sales_table.dart';
import 'package:mandob/pages/repositories_page/transfer_repository_items/transfer_table/main_transfer_table.dart';
import 'package:mandob/providers/cashier_provider.dart';
import 'package:mandob/providers/collections_provider.dart';
import 'package:mandob/providers/company_provider.dart';
import 'package:mandob/providers/customers_provider.dart';
import 'package:mandob/providers/expense_provider.dart';
import 'package:mandob/providers/invoices_provider.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/extensions.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:mandob/utils/show_bar/messages.dart';
import 'package:mandob/utils/submit_button.dart';
import 'package:mandob/utils/text_field.dart';
import 'package:persian_number_utility/src/extensions.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../models/company_info_model.dart';
import '../../../../providers/language.dart';
import '../../../../utils/color_manager.dart';
import 'components/discount_toggle.dart';
import 'components/top_section.dart';

class AddSalesInvoiceDialog extends StatefulWidget {
  final bool isReturned;
  final bool isPurchase;
  final bool byProductPrice;
  final CustomerModel? customerData;
  final String? senderRepoId;
  final invoiceData;

  const AddSalesInvoiceDialog({
    super.key,
    required this.isReturned,
    required this.byProductPrice,
    this.invoiceData,
    this.isPurchase = false,
    this.customerData,
    this.senderRepoId,
  });

  @override
  State<StatefulWidget> createState() => _AddSimpleReceiptDialog();
}

class _AddSimpleReceiptDialog extends State<AddSalesInvoiceDialog> {
  late double totalItemsPrice = 0.0;
  late double totalValue = 0.0;

  List<String> itemsStringController = [];

  List<int?> quantities = [];

  List? returnsProd = [];

  double? taxVal = 0;

  var randomNumber = Random.secure();
  late double invoiceNumber;

  var paidTotalValue = 0.0;

  @override
  void initState() {
    invoiceNumber = randomNumber.nextDouble() * 1000000;
    while (invoiceNumber < 100000) {
      invoiceNumber *= 10;
    }

    if (widget.isReturned) {
      setState(() {
        returnsProd = widget.invoiceData.products!;
      });

      for (var element in returnsProd!) {
        totalItemsPrice += element!['price'] * element['quantity'];
        itemsStringController
            .add(widget.invoiceData.products!.length.toString());
        quantities.add(int.tryParse(element['quantity'].toString()));

        addedMap.addAll({
          element['id'].toString(): int.tryParse(element['quantity'].toString())
        });
      }
    } else {
      for (var element in addedItems) {
        totalItemsPrice += element['price'] * element['quantity'];
        if (widget.byProductPrice == false) {
          totalValue += element['paidPrice'];
        }
      }
      if (widget.byProductPrice == false) {
        paidTotalValue = totalValue;
        priceCtrl.text = totalValue.roundedPrecisionToString(5);
        taxVal = totalValue - totalItemsPrice;
      }
    }

    setDiscount(
        Provider.of<CompanyInfoProvider>(context, listen: false).companyInfo);

    super.initState();
  }

  bool loading = false;
  bool invalid = false;

  var priceCtrl = TextEditingController();

  var formKey = GlobalKey<FormState>();

  bool moreThanQuantity = false;

  var remainingCash = 0.0;

  void setDiscount(CompanyInfoModel? info) {
    final infoTax =
        info != null && info.tax != null && info.tax != 0 ? info.tax! : 15;

    final discountVal = double.tryParse(discount.toEnglishDigit()) ?? 0.0;

    if (!invalid) {
      if (widget.byProductPrice) {
        final discountedPrice = totalItemsPrice - discountVal;

        taxVal = (infoTax / 100) * discountedPrice;
        totalValue = discountedPrice + taxVal!;

        priceCtrl.text = totalValue.roundedPrecisionToString(5);
      } else {
        final discountedPrice = totalItemsPrice - discountVal;
        taxVal = (infoTax / 100) * discountedPrice;

        totalValue = discountedPrice + taxVal!;

        priceCtrl.text = totalValue.roundedPrecisionToString(5);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final lang = Provider.of<LangProvider>(context, listen: false);
    final user = Provider.of<UserProvider>(context, listen: false);

    return WillPopScope(
      onWillPop: () async {
        if (MediaQuery.of(context).viewInsets.bottom != 0) {
          addedItems.clear();
          addedMap.clear();
        } else {
          Navigator.pop(context);
          addedItems.clear();
          addedMap.clear();
        }
        return true;
      },
      child: Scaffold(
        appBar: salesInvoiceAppBar(
          context,
          title: widget.isReturned
              ? (context.isEng ? 'Return Invoice' : 'فاتورة مرتجعات')
              : (context.isEng ? 'New Invoice' : 'فاتورة جديدة'),
        ),
        body:
            Consumer<CompanyInfoProvider>(builder: (context, infoData, child) {
          return FutureBuilder(
              future: infoData.fetchCompanyInfo(),
              builder: (context, snapshot) {
                final info = infoData.companyInfo;

                return Consumer<CustomersProvider>(
                    builder: (context, customerData, child) {
                  return Consumer<RepositoryProvider>(
                      builder: (context, repo, child) {
                    return Consumer<QuantitiesProvider>(
                        builder: (context, quantitiesProvider, child) {
                      return Consumer<InvoicesProvider>(
                          builder: (context, invoices, child) {
                        final bool isCustomerVatNumberNullOrEmpty = widget
                                .isReturned
                            ? (widget.invoiceData.customerVatNumber == null ||
                                widget.invoiceData!.customerVatNumber!.isEmpty)
                            : (widget.customerData!.vatNumber == null ||
                                widget.customerData!.vatNumber!.isEmpty);
                        return Center(
                          child: Form(
                            key: formKey,
                            child: Column(
                              children: [
                                Expanded(
                                  child: Stack(
                                    children: <Widget>[
                                      SingleChildScrollView(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 16.0),
                                        child: Column(
                                          children: [
                                            if (widget.isReturned)
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Text(
                                                        context.isEng
                                                            ? 'Invoice: '
                                                            : 'فاتورة: ',
                                                        style: const TextStyle(
                                                            fontSize: 16),
                                                      ),
                                                      Text(
                                                        '${widget.invoiceData.invoiceNumber}',
                                                        style: const TextStyle(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  Row(
                                                    children: [
                                                      Text(
                                                        context.isEng
                                                            ? 'Date: '
                                                            : 'التاريخ: ',
                                                        style: const TextStyle(
                                                            fontSize: 16),
                                                      ),
                                                      Text(
                                                        widget.invoiceData
                                                            .createdAt
                                                            .toString()
                                                            .split(' ')[0],
                                                        style: const TextStyle(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            AppGaps.gap12,
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Row(
                                                      children: [
                                                        Text(
                                                          context.isEng
                                                              ? 'Customer: '
                                                              : 'العميل: ',
                                                          style:
                                                              const TextStyle(
                                                                  fontSize: 16),
                                                        ),
                                                        Text(
                                                          '${widget.isReturned ? widget.invoiceData.customerName : widget.customerData!.customerName}',
                                                          style:
                                                              const TextStyle(
                                                            fontSize: 16,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    if (!widget.isReturned)
                                                      Row(
                                                        children: [
                                                          Text(
                                                            context.isEng
                                                                ? 'Date: '
                                                                : 'التاريخ: ',
                                                            style:
                                                                const TextStyle(
                                                                    fontSize:
                                                                        16),
                                                          ),
                                                          Text(
                                                            DateTime.now()
                                                                .toString()
                                                                .split(' ')[0],
                                                            style:
                                                                const TextStyle(
                                                              fontSize: 16,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                  ],
                                                ),
                                                AppGaps.gap8,
                                                Row(
                                                  children: [
                                                    Text(
                                                      context.isEng
                                                          ? 'Tax Number: '
                                                          : 'الرقم الضريبي: ',
                                                      style: const TextStyle(
                                                          fontSize: 16),
                                                    ),
                                                    Text(
                                                      isCustomerVatNumberNullOrEmpty
                                                          ? (context.isEng
                                                              ? 'Not Available'
                                                              : 'لا يوجد')
                                                          : '${widget.isReturned ? widget.invoiceData.customerVatNumber : widget.customerData!.vatNumber}',
                                                      style: TextStyle(
                                                        fontSize: 16,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        color:
                                                            isCustomerVatNumberNullOrEmpty
                                                                ? ColorManager
                                                                    .errorColor
                                                                : Colors.black,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                            AppGaps.gap12,
                                            const Divider(
                                              thickness: 1,
                                              color:
                                                  ColorManager.lightFieldColor,
                                            ),
                                            AppGaps.gap12,
                                            Align(
                                              alignment: context.isEng
                                                  ? Alignment.centerLeft
                                                  : Alignment.centerRight,
                                              child: Text(
                                                context.isEng
                                                    ? 'Products'
                                                    : 'المنتجات',
                                                style: const TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ),
                                            AppGaps.gap24,
                                            widget.isReturned
                                                ? returnedProductsFields(
                                                    lang: lang, info: info!)
                                                : invoiceProductsFields(),
                                            AppGaps.gap12,
                                            const Divider(
                                              thickness: 1,
                                              color:
                                                  ColorManager.lightFieldColor,
                                            ),
                                            AppGaps.gap12,
                                            discountField(lang, info),
                                            const SizedBox(height: 20),
                                            totalAndTaxWidget(
                                                lang: lang, info: info),
                                            AppGaps.gap12,
                                            if (!widget.isReturned)
                                              cashPaidField(lang: lang),
                                          ],
                                        ),
                                      ),
                                      loading
                                          ? const Center(child: LoadingWidget())
                                          : Container(),
                                    ],
                                  ),
                                ),
                                addInvoiceButton(
                                  lang: lang,
                                  repo: repo,
                                  quantitiesData: quantitiesProvider,
                                  userProvider: user,
                                ),
                              ],
                            ),
                          ),
                        );
                      });
                    });
                  });
                });
              });
        }),
      ),
    );
  }

  Widget returnedProductsFields(
      {required LangProvider lang, required CompanyInfoModel info}) {
    return ListView.separated(
      shrinkWrap: true,
      itemCount: widget.invoiceData.products!.length,
      separatorBuilder: (context, index) {
        return AppGaps.gap16;
      },
      scrollDirection: Axis.vertical,
      itemBuilder: (context, index) {
        var itemList = returnsProd;
        var quantity = quantities[index];

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: ColorManager.lightFieldColor.withOpacity(0.3),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    itemList![index]!['name'],
                    style: const TextStyle(
                      fontSize: 16,
                    ),
                  ),
                  Row(
                    children: [
                      const Text(
                        'كمية المنتج: ',
                        style: TextStyle(
                          fontSize: 13,
                        ),
                      ),
                      Text(
                        '$quantity',
                        style: const TextStyle(
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              AppGaps.gap12,
              const Divider(
                thickness: 1,
                color: ColorManager.lightFieldColor,
              ),
              AppGaps.gap12,
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Text(
                    'الكمية',
                    style: TextStyle(
                      fontSize: 12,
                    ),
                  ),
                  AppGaps.gap12,
                  SizedBox(
                    height: 45,
                    width: MediaQuery.of(context).size.width * 0.3,
                    child: TextFieldWidget(
                      initialValue: itemList![index]!['quantity'].toString(),
                      onChanged: (val) {
                        setState(() {
                          if (val != '' && val != '0') {
                            if (int.tryParse(val.toString().toEnglishDigit())! >
                                quantity!) {
                              moreThanQuantity = true;
                              showBar(
                                  context,
                                  context.isEng
                                      ? 'Please choose a quantity less than or equal to the quantity of the product !'
                                      : 'برجاء اختيار كمية مرتجع أقل من أو تساوي كمية المنتج !');
                              return;
                            }
                            moreThanQuantity = false;
                            itemsStringController[index] = val;

                            addedMap.addAll({
                              itemList[index]!['id'].toString():
                                  int.tryParse(val.toString().toEnglishDigit())
                            });

                            for (var element in returnsProd!) {
                              if (element!['id'] == itemList[index]!['id']) {
                                element['quantity'] = int.tryParse(
                                    val.toString().toEnglishDigit());
                              }
                            }

                            totalItemsPrice = 0.0;

                            for (var element in returnsProd!) {
                              totalItemsPrice +=
                                  element!['price'] * element['quantity'];
                            }

                            var taxVal =
                                ((info.tax ?? 15)! / 100) * totalItemsPrice;
                            totalValue = totalItemsPrice + taxVal;
                          }
                        });
                      },
                      label: "الكمية",
                      // label: 'كمية مرتجع ${itemList[index]!['name']}',
                    ),
                  ),
                  // AppGaps.gap8,
                  // const Text('للحبة'),
                  const Spacer(),
                  Row(
                    children: [
                      Text(
                        (itemList[index]!['price'] as num)
                            .roundedPrecisionToString(5),
                        style: const TextStyle(
                          fontSize: 16,
                          color: ColorManager.primaryColor,
                        ),
                      ),
                      Text('${context.currency}'),
                    ],
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget invoiceProductsFields() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        headingRowHeight: 35,
        columnSpacing: 55,
        border: TableBorder.all(),
        columns: [
          DataColumn(
              label: Padding(
            padding: const EdgeInsets.only(right: 15.0),
            child: Text(context.isEng ? 'Products' : 'المنتجات'),
          )),
          DataColumn(label: Text(context.isEng ? 'Price' : 'السعر')),
          DataColumn(label: Text(context.isEng ? 'Total' : 'الإجمالي')),
        ],
        rows: addedItems.map((item) {
          return DataRow(cells: [
            DataCell(
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['name'],
                    style: const TextStyle(
                      fontSize: 15,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'الكمية: ${item['quantity']}',
                        style: const TextStyle(
                          fontSize: 13,
                          color: ColorManager.primaryColor,
                        ),
                      ),
                      if (item['weight'] != null && item['weight'] != 0) ...[
                        const SizedBox(width: 8),
                        Text(
                          'الوزن: ${item['weight']}',
                          style: const TextStyle(
                            fontSize: 13,
                            color: ColorManager.secondaryColor,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            DataCell(Padding(
              padding: const EdgeInsets.only(right: 3.0),
              child: Row(
                children: [
                  Text((item['price'] as num).roundedPrecisionToString(5)),
                  Text(
                    context.currency,
                    style: TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            )),
            DataCell(Padding(
              padding: const EdgeInsets.only(right: 10.0),
              child: Row(
                children: [
                  Text(
                    ((item['price'] * item['quantity']) as num)
                        .roundedPrecisionToString(5),
                  ),
                  Text(
                    context.currency,
                    style: TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            )),
          ]);
        }).toList(),
      ),
    );
  }

  Widget cashPaidField({
    required LangProvider lang,
  }) {
    return Column(
      children: [
        const SizedBox(
          height: 15,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: TextFieldWidget(
                controller: priceCtrl,
                withoutEnter: true,
                hint: '0.0',
                label: context.isEng ? "Cash Paid" : 'المدفوع نقدي',
                textInputType: TextInputType.number,
                onChanged: (val) async {
                  await Future.delayed(const Duration(milliseconds: 100));

                  if (val == null ||
                      val.toString().toEnglishDigit() == '0' ||
                      val.toString().toEnglishDigit() == '') {
                    val = '0.0';
                    // priceCtrl.text = '0.0';
                  }

                  if (double.tryParse(val.toString().toEnglishDigit()) ==
                          null ||
                      double.tryParse(val.toString().toEnglishDigit())! >
                          totalValue) {
                    setState(() {
                      invalid = true;
                      showBar(context, 'برجاء إدخال مبلغ صحيح');
                    });
                    return;
                  }

                  setState(() {
                    invalid = false;
                    double? cashVal =
                        double.tryParse(val.toString().toEnglishDigit());

                    remainingCash = totalValue - cashVal!;
                  });
                },
              ),
            ),
            AppGaps.gap12,
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(context.isEng ? 'Remaining' : "المتبقي"),
                Row(
                  children: [
                    Text(
                      remainingCash.roundedPrecisionToString(5),
                      style: const TextStyle(
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      context.currency,
                      style: TextStyle(
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  var discountCtrl = TextEditingController();

  String discount = '';

  bool isDiscountPercent = true;

  void discountInValid(val) {
    if ((double.tryParse(val.toString().toEnglishDigit()) == null ||
            double.tryParse(val.toString().toEnglishDigit())! >
                totalItemsPrice) &&
        !isDiscountPercent) {
      setState(() {
        invalid = true;
        showBar(context, 'برجاء إدخال مبلغ خصم صحيح');
      });
      return;
    }

    if (isDiscountPercent) {
      if ((double.tryParse(val.toString().toEnglishDigit()) ?? 0) > 100) {
        setState(() {
          invalid = true;
          showBar(context, 'برجاء إدخال مبلغ خصم صحيح');
        });
        return;
      }
    }
  }

  Widget discountField(lang, CompanyInfoModel? info) => Row(
        children: [
          Expanded(
            child: TextFieldWidget(
              controller: discountCtrl,
              label: context.isEng ? "Discount" : 'الخصم',
              hint: context.isEng ? "Discount" : 'الخصم',
              textInputType: TextInputType.number,
              onChanged: (val) {
                if (val!.isEmpty) {
                  setState(() {
                    invalid = false;
                    discount = '';
                  });
                  setDiscount(info);

                  return;
                }

                //! Validation if discount > total
                discountInValid(val);

                if (isDiscountPercent) {
                  discount = (totalItemsPrice *
                          (double.tryParse(val.toString().toEnglishDigit())! /
                              100))
                      .roundedPrecisionToString(5);
                } else {
                  discount = val.toString().toEnglishDigit();
                }

                setState(() {
                  invalid = false;
                });

                setDiscount(info);
              },
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          DiscountSwitchWidget(
            onToggle: (val) {
              setState(() {
                isDiscountPercent = val;
                discount = '';
                discountCtrl.clear();
              });
            },
            value: isDiscountPercent,
          ),
        ],
      );

  Widget totalAndTaxWidget({
    required LangProvider lang,
    required CompanyInfoModel? info,
  }) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
                '${context.isEng ? "Total before VAT" : 'إجمالي قبل الضريبة'} '),
            Row(
              children: [
                Text(
                  (totalItemsPrice).roundedPrecisionToString(5),
                  style: const TextStyle(
                    fontSize: 18,
                  ),
                ),
                Text(
                  context.currency,
                  style: TextStyle(
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ],
        ),
        if (discount.isNotEmpty && !invalid) ...[
          const SizedBox(
            height: 5,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text(
                    context.isEng ? 'Discount ' : 'الخصم ',
                  ),
                  if (isDiscountPercent)
                    Text(
                      '(${discountCtrl.text}%)',
                      style: const TextStyle(
                        fontFamily: 'Droid',
                        color: ColorManager.errorColor,
                        fontSize: 13,
                      ),
                    ),
                ],
              ),
              Row(
                children: [
                  Text(
                    discount,
                    style: const TextStyle(
                      color: ColorManager.errorColor,
                      fontSize: 18,
                    ),
                  ),
                  Text(
                    context.currency,
                    style: TextStyle(
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
        const SizedBox(
          height: 5,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Text(
                  context.isEng ? 'VAT' : 'ضريبة القيمة المضافة',
                ),
                Text(
                  " (${(info == null || info.tax == null ? 15 : info.tax)!.toInt()}%) ",
                  style: const TextStyle(
                    color: ColorManager.primaryColor,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Text(
                  taxVal!.roundedPrecisionToString(5),
                  style: const TextStyle(
                    color: ColorManager.primaryColor,
                    fontSize: 18,
                  ),
                ),
                Text(
                  context.currency,
                  style: TextStyle(
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(
          height: 5,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('${context.isEng ? "Total" : 'الإجمالي'} '),
            Row(
              children: [
                Text(
                  totalValue.roundedPrecisionToString(5),
                  style: const TextStyle(
                    fontSize: 18,
                  ),
                ),
                Text(
                  context.currency,
                  style: TextStyle(
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget addInvoiceButton({
    required LangProvider lang,
    required RepositoryProvider repo,
    required QuantitiesProvider quantitiesData,
    required UserProvider userProvider,
  }) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: SubmitButton(
          width: context.width,
          label: context.isEng ? 'Save Invoice' : 'حفظ الفاتورة',
          onPressed: loading
              ? null
              : () async {
                  if (addedMap.isNotEmpty) {
                    if (invalid) {
                      showBar(context, 'برجاء إدخال مبلغ صحيح');
                      return;
                    }
                    if (moreThanQuantity == true) {
                      showBar(context,
                          'برجاء اختيار كمية مرتجع أقل من أو تساوي كمية المنتج !');
                    } else {
                      setState(() {
                        loading = true;
                      });

                      addSalesInvoice();
                    }
                  } else {
                    showBar(context, 'برجاء التأكد من بيانات الفاتورة !');
                  }
                }),
    );
  }

  void addSalesInvoice() async {
    try {
      final repo = Provider.of<RepositoryProvider>(context, listen: false);
      final quantitiesData =
          Provider.of<QuantitiesProvider>(context, listen: false);
      final activeUser =
          Provider.of<UserProvider>(context, listen: false).activeUser!;

      List<RepositoryModel> senderRepo = repo.repositories
          .where((element) => element.id == widget.senderRepoId)
          .toList();

      await Future.wait([_crateInvoiceOperations()]);

      await Future.wait([
        modifyQuantities(
          senderRepo: senderRepo,
          quantitiesData: quantitiesData,
        )
      ]);

      await Future.wait([_addToCollections(activeUser.uid!)]);

      await Future.wait([_addInvoice()]);

      if (mounted) {
        setState(() {
          scrollingSalesInvoices = false;
        });
        showDoneInvoiceMessageAndClose(
          context,
        );
      }
    } catch (e) {
      debugPrint('addSalesInvoiceError: $e');
      if (mounted) {
        showInvoiceErrorMessage(
          context,
        );
      }
    }
  }

  Future<void> modifyQuantities(
      {required List<RepositoryModel> senderRepo,
      required QuantitiesProvider quantitiesData}) async {
    List<QuantityModel> senderQuantitiesValue = [];

    int oldSenderValue = 0;

    addedMap.forEach((key, value) async {
      senderQuantitiesValue = quantitiesData.quantities
          .where((element) => element.productId == key)
          .toList();

      if (senderQuantitiesValue.isNotEmpty) {
        senderQuantitiesValue[0].quantities!.forEach((key, val) {
          if (key == senderRepo[0].id!) {
            setState(() {
              oldSenderValue = val;
            });
          }
        });
      }

      if (widget.isReturned) {
        await Provider.of<QuantitiesProvider>(context, listen: false)
            .modifyQuantity(
                productId: key,
                storeId: senderRepo[0].id!,
                quantity: oldSenderValue + value);
      } else {
        debugPrint('oldSenderValue: $oldSenderValue');
        debugPrint('value: $value');
        debugPrint('neww: ${oldSenderValue - value}');

        await Provider.of<QuantitiesProvider>(context, listen: false)
            .modifyQuantity(
                productId: key,
                storeId: senderRepo[0].id!,
                quantity: oldSenderValue - value);
      }
    });
  }

  Future<void> _crateInvoiceOperations() async {
    final activeUser =
        Provider.of<UserProvider>(context, listen: false).activeUser!;
    final customers = Provider.of<CustomersProvider>(context, listen: false);

    var userNewDebit = 0.0;

    var priceVal = double.tryParse(priceCtrl.text.toEnglishDigit()) ?? 0.0;

    if (widget.isReturned && widget.invoiceData.invoiceType == 'later') {
      CustomerModel? customer = customers.allCustomers.firstWhere(
          (element) => element.id == widget.invoiceData.customerId.toString(),
          orElse: () => CustomerModel.fromJson({'': ''}));

      userNewDebit = customer.debit ?? 0.0;

      if ((customer.debit! - totalValue).isNegative) {
        var totalForCustomer = (userNewDebit -= totalValue).abs();

        await _createOperation(
            activeUser: activeUser, value: totalForCustomer, source: "returns");

        await _updateCustomerDebit(
          activeUser: activeUser,
          customer: customer,
          totalForCustomer: totalForCustomer,
        );

        await Provider.of<CustomersProvider>(context, listen: false)
            .editDebit(widget.invoiceData.customerId.toString(), 0.0);
      } else {
        var remainingCash =
            widget.invoiceData.totalPrice - widget.invoiceData.paidCash;

        if ((totalValue - remainingCash).isNegative) {
          userNewDebit -= totalValue;
        } else {
          userNewDebit -= remainingCash;

          var totalExpense = (totalValue - remainingCash).abs();

          await _createOperation(
              activeUser: activeUser, value: totalExpense, source: 'expense');

          await _createExpense(
            activeUser: activeUser,
            totalExpense: totalExpense,
            customerName: customer.customerName!,
          );
        }

        await Provider.of<CustomersProvider>(context, listen: false).editDebit(
            widget.invoiceData.customerId.toString(),
            double.tryParse(userNewDebit.roundedPrecisionToString(5)));
      }
    } else {
      if (!widget.isReturned) {
        var customerOldDebit = widget.customerData?.debit ?? 0.0;
        userNewDebit = customerOldDebit;
        if (priceVal != totalValue) {
          var totalForCustomer = ((totalValue - priceVal)).abs();

          userNewDebit += totalForCustomer;

          await Provider.of<CustomersProvider>(context, listen: false)
              .editDebit(widget.customerData!.id.toString(),
                  double.tryParse(userNewDebit.roundedPrecisionToString(5)));
        }
      }
    }

    if (widget.isReturned && widget.invoiceData.invoiceType != 'later') {
      await _createOperation(
          activeUser: activeUser,
          value: widget.invoiceData.paidCash,
          source: 'sales-return');
    }
  }

  Future<void> _createOperation(
      {required UserModel activeUser,
      required double value,
      required String source}) async {
    await Provider.of<CashierProvider>(context, listen: false)
        .createOperation(OperationModel.fromJson({
      "isIncome": false,
      "value": value,
      "source": source,
      "userId": activeUser.uid,
      "opTime": DateTime.now().toIso8601String(),
    }));
  }

  Future<void> _createExpense(
      {required UserModel activeUser,
      required double totalExpense,
      required String customerName}) async {
    await Provider.of<ExpensesProvider>(context, listen: false)
        .createExpense(ExpenseModel.fromJson({
      'notes': 'مرتجع ل $customerName',
      'source': 'عامة',
      'value': totalExpense,
      'mandobId': activeUser.uid,
      'createdAt': DateTime.now().toString()
    }));
  }

  Future<void> _updateCustomerDebit(
      {required UserModel activeUser,
      required CustomerModel customer,
      required double totalForCustomer}) async {
    await await Provider.of<ExpensesProvider>(context, listen: false)
        .createExpense(ExpenseModel.fromJson({
      'notes': 'مرتجع ل ${customer.customerName}',
      'source': 'عامة',
      'value': totalForCustomer,
      'mandobId': activeUser.uid,
      'createdAt': DateTime.now().toString()
    }));
  }

  Future<void> _addToCollections(String userId) async {
    if (!widget.isReturned &&
        double.tryParse(priceCtrl.text.toEnglishDigit()) != 0 &&
        double.tryParse(priceCtrl.text.toEnglishDigit()) != null) {
      await Provider.of<CollectionsProvider>(context, listen: false)
          .addCollection(CollectionModel.fromJson({
        'createdAt': DateTime.now().toString(),
        'customerName': widget.customerData!.customerName,
        'customerId': widget.customerData!.id,
        'value': double.tryParse(priceCtrl.text.toEnglishDigit()),
        'isCashe': true,
        'type': 'sales',
        'userId': userId,
      }));
    }
  }

  Future<void> _addInvoice() async {
    var user = Provider.of<UserProvider>(context, listen: false);
    var info =
        Provider.of<CompanyInfoProvider>(context, listen: false).companyInfo;

    final doc = await Provider.of<InvoicesProvider>(context, listen: false)
        .addInvoice(InvoiceModel.fromJson({
      'createdAt': DateTime.now().toString(),
      'products': jsonEncode(widget.isReturned ? returnsProd : addedItems),
      'totalPrice': totalValue,
      'isReturned': widget.isReturned,
      'isSales': true,
      'taxValPercent': info == null || info.tax == null ? 15 : info.tax!,
      'totalWithoutTax': totalItemsPrice,
      'storeId': widget.senderRepoId,
      'mandobId': user.activeUser!.uid,
      'customerId': widget.isReturned
          ? widget.invoiceData.customerId
          : widget.customerData!.id,
      'customerVatNumber': widget.isReturned
          ? widget.invoiceData.customerVatNumber
          : widget.customerData!.vatNumber,
      'invoiceNumber': invoiceNumber.toStringAsFixed(0),
      'mainInvoiceId': widget.isReturned
          ? widget.invoiceData.invoiceNumber == null
              ? widget.invoiceData.invoiceId
              : widget.invoiceData.invoiceNumber.toString()
          : '',
      'customerName': widget.isReturned
          ? widget.invoiceData.customerName
          : widget.customerData!.customerName,
      'invoiceType': widget.isReturned
          ? widget.invoiceData.invoiceType
          : double.tryParse(priceCtrl.text.toEnglishDigit())?.toInt() ==
                  totalValue.toInt()
              ? 'cash'
              : 'later',
      'paidCash': widget.isReturned
          ? widget.invoiceData.paidCash
          : double.tryParse(priceCtrl.text.toEnglishDigit()) ?? 0.0,
      'discount': double.tryParse(discount.toEnglishDigit()) ?? 0.0,
    }));

    final invoiceId = doc.$id.toString().substring(1, 7);

    await zakahIntegration(invoiceNumber: invoiceId);
  }

  Future<void> zakahIntegration({
    required String invoiceNumber,
  }) async {
    var info =
        Provider.of<CompanyInfoProvider>(context, listen: false).companyInfo;

    final isCompanyCustomer = widget.customerData?.customerType == 'مؤسسة' ||
        widget.customerData?.customerType == 'Company';

    await context.read<InvoicesProvider>().zakahIntegration(
      {
        "seller": {
          "name": info?.companyName ?? "شركة الإنتاج المبارك",
          "commercial_registration_number":
              info?.commercialRegisterNumber ?? "12345678",
          "vat_number": info?.vatNumber ?? "302008161400003",
          "street_name": "Main Street",
          "building_number": "4222",
          "plot_identification": "Plot 101",
          "city_subdivision": "Subdivision A",
          "city": "Riyadh",
          "postal_number": "12345",
          "country": "SA"
        },
        "client": {
          "name": widget.customerData?.customerName ?? "Client Name",
          // if (widget.customerData?.commercialRegisterNumber != null &&
          //     widget.customerData!.commercialRegisterNumber!.isNotEmpty)
          "commercial_registration_number":
              widget.customerData?.commercialRegisterNumber ?? "12345658",
          // if (widget.customerData?.vatNumber != null &&
          //     widget.customerData!.vatNumber!.isNotEmpty)
          "vat_number": widget.customerData?.vatNumber ?? "302008161200003",
          "street_name": widget.customerData?.streetName ?? 'Jeddah',
          "building_number":
              widget.customerData?.buildingNumber ?? "1222", //? 4 numbers
          "plot_identification":
              widget.customerData?.plotIdentification ?? "Plot 202",
          "city_subdivision":
              widget.customerData?.citySubdivision ?? "Subdivision B",
          "city": widget.customerData?.city ?? "Client City",
          "postal_number":
              widget.customerData?.postalNumber ?? "54321", //? 5 numbers
          "country": "SA"
        },
        "invoice_number": invoiceNumber,
        "total_without_vat": totalItemsPrice,
        "vat": taxVal,
        "discount": discount,
        "invoice_date":
            DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime.now()),
        "invoice_items": widget.isReturned
            ? returnsProd?.map((e) {
                final dynamicTax = e['price'] * (info?.tax ?? 15) / 100;
                return {
                  "name": e['name'],
                  "quantity": e['quantity'],
                  "tax_class_code": "S",
                  "tax_percent": 15.0,
                  "price": e['price'],
                  "vat": e['price'] * dynamicTax,
                  "total": e['price'] + (e['price'] * dynamicTax),
                };
              }).toList()
            : addedItems.map((e) {
                final dynamicTax = e['price'] * (info?.tax ?? 15) / 100;
                return {
                  "name": e['name'],
                  "quantity": e['quantity'],
                  "tax_class_code": "S",
                  "tax_percent": 15.0,
                  "price": e['price'],
                  "vat": e['price'] * dynamicTax,
                  "total": e['price'] + (e['price'] * dynamicTax),
                };
              }).toList(),
        "notes": "This is a sample invoice note.",
        if (widget.isReturned) "note_type": "cn",
        if (widget.isReturned) "reason": "مرتجع",
        if (widget.isReturned)
          "reference_number": widget.invoiceData.invoiceNumber.toString(),
      },
      isRefund: widget.isReturned,
      isSimplfiedInvoice: !isCompanyCustomer,
    );
  }
}
