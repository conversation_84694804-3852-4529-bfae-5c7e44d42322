import 'package:bubble_tab_indicator/bubble_tab_indicator.dart';
import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/pages/receipt_page/components/invoice_table/purchases_table/purchases_navigation_tabs.dart';
import 'package:mandob/pages/receipt_page/components/invoice_table/sales_table/sales_navigation_tabs.dart';

class ReceiptsPage extends StatefulWidget {
  const ReceiptsPage({super.key});
  @override
  _ReceiptsPageState createState() => _ReceiptsPageState();
}

class _ReceiptsPageState extends State<ReceiptsPage>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;

  late final List<Tab> tabs;

  @override
  void initState() {
    super.initState();

    tabs = <Tab>[
      Tab(text: context.isEng ? 'Sales' : 'المبيعات'),
      Tab(text: context.isEng ? 'Purchases' : 'المشتريات'),
    ];

    _tabController = TabController(vsync: this, length: tabs.length);
  }

  @override
  void dispose() {
    _tabController!.dispose();
    super.dispose();
  }

  String? errorText;
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      initialIndex: 0,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PreferredSize(
          preferredSize:
              Size.fromHeight(MediaQuery.of(context).size.height / 13),
          child: AppBar(
            backgroundColor: Colors.white,
            elevation: 0.0,
            bottom: TabBar(
              unselectedLabelColor: Colors.black87,
              controller: _tabController,
              padding: const EdgeInsets.only(
                right: 70,
                left: 20,
              ),
              isScrollable: false,
              indicatorSize: TabBarIndicatorSize.tab,
              indicator: const BubbleTabIndicator(
                indicatorHeight: 35,
                indicatorColor: Colors.orangeAccent,
                tabBarIndicatorSize: TabBarIndicatorSize.tab,
              ),
              tabs: tabs,
            ),
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: const [
            MainSalesTable(),
            MainPurchasesTable(),
          ],
        ),
      ),
    );
  }
}
