import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/providers/expense_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/widgets/date_widget.dart';
import 'package:provider/provider.dart';

import '../../utils/app_bar.dart';
import '../../utils/loading_widget.dart';
import 'components/add_expense_dialog.dart';
import 'components/widgets.dart';

class ExpensesPage extends StatefulWidget {
  @override
  _ExpensesPageState createState() => _ExpensesPageState();
}

class _ExpensesPageState extends State<ExpensesPage> {
  var sDate = ValueNotifier(DateTime.now().subtract(const Duration(days: 1)));
  var eDate = ValueNotifier(DateTime.now());
  var totalExpenses = 0.0;
  String? userId;
  String? expensesType;
  final List<String> expensesTypes = [
    'عامة',
    'بنزين',
    'أخرى',
  ];

  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;
    TextTheme textTheme(context) => Theme.of(context).textTheme;

    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: appBarWidget(
        context,
        title: isEng ? 'Daily Operations' : 'العمليات اليومية',
        subtitle: isEng ? 'Expense +' : 'صرف + ',
        onTap: () => showDialog(
          context: context,
          builder: (context) => const AddExpensesDialog(),
        ),
      ),
      body: Consumer<UserProvider>(builder: (context, users, _) {
        return Consumer<ExpensesProvider>(builder: (context, expenses, child) {
          Future fetch() async {
            if (users.activeUser!.isAdmin!) await users.fetchUsers();
            await expenses.fetchExpenses(
              userId:
                  users.activeUser!.isAdmin! ? userId : users.activeUser!.uid,
              type: expensesType,
              startDate: sDate.value,
              endDate: eDate.value,
            );
            print(userId);
          }

          return FutureBuilder(
              future: fetch(),
              builder: (context, snapshot) {
                return Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        if (users.activeUser!.isAdmin!)
                          snapshot.connectionState == ConnectionState.waiting
                              ? const SizedBox(
                                  width: 50, height: 3, child: LoadingWidget())
                              : Card(
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 5.0),
                                    child: DropdownButton<String>(
                                        value: userId,
                                        hint: Text(isEng
                                            ? 'Daily Operations'
                                            : 'العمليات اليومية'),
                                        underline: const Center(),
                                        items: [
                                          ...(users.users ?? []).map((value) {
                                            return DropdownMenuItem<String>(
                                              value: value.uid,
                                              child: Text(
                                                value.name!,
                                                style: textTheme(context)
                                                    .titleMedium,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            );
                                          }).toList()
                                        ],
                                        onChanged: (val) {
                                          setState(() {
                                            userId = val!;
                                          });
                                        }),
                                  ),
                                ),
                        Card(
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 5.0),
                            child: DropdownButton<String>(
                                value: expensesType,
                                hint: Text(isEng
                                    ? 'Daily Operations'
                                    : 'العمليات اليومية'),
                                underline: const Center(),
                                items: expensesTypes.map((String value) {
                                  return DropdownMenuItem<String>(
                                    value: value,
                                    child: Text(
                                      value,
                                      style: textTheme(context).titleMedium,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  );
                                }).toList(),
                                onChanged: (val) {
                                  setState(() {
                                    expensesType = val!;
                                  });
                                }),
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10.0, vertical: 8.0),
                      child: UiWidgets.datePicker(
                        context,
                        () {
                          setState(() {});
                        },
                        startDate: sDate,
                        endDate: eDate,
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    snapshot.connectionState == ConnectionState.waiting
                        ? const SizedBox(child: LoadingWidget())
                        : Expanded(
                            child: Material(
                                elevation: 4,
                                child: ExpenseTableWidget(
                                  data: expenses.expenses!,
                                )),
                          )
                  ],
                );
              });
        });
      }),
    );
  }
}
