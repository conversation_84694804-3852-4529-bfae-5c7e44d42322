import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/models/expense_model.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/pages/expenses_page/expenses_page.dart';
import 'package:mandob/providers/expense_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:mandob/utils/submit_button.dart';
import 'package:mandob/utils/text_field.dart';
import 'package:persian_number_utility/persian_number_utility.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../models/operation_model.dart';
import '../../../providers/cashier_provider.dart';

class AddExpensesDialog extends StatefulWidget {
  const AddExpensesDialog({
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _AddExpensesDialog();
}

class _AddExpensesDialog extends State<AddExpensesDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  late CustomerModel customer;

  String? expenseType;

  @override
  void initState() {
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    Provider.of<CashierProvider>(context, listen: false).fetchOps();

    super.initState();
  }

  bool loading = false;

  var noteCtrl = TextEditingController();
  var expenseCtrl = TextEditingController();

  var formKey = GlobalKey<FormState>();

  final ExpensesProvider _expenses = ExpensesProvider();

  String mandob = '';

  late UserModel user;

  @override
  Widget build(BuildContext context) {
    final userProvider =
        Provider.of<UserProvider>(context, listen: false).activeUser;
    final isKeyboardOpened = MediaQuery.of(context).viewInsets.bottom != 0;

    expenseType ??= context.isEng ? 'General' : 'عامة';
    return WillPopScope(
      onWillPop: () async {
        if (MediaQuery.of(context).viewInsets.bottom != 0) {
        } else {
          Navigator.pop(context);
        }
        return true;
      },
      child: Scaffold(
        bottomNavigationBar: Padding(
          padding: EdgeInsets.only(
              bottom: isKeyboardOpened ? 70 : 20.0, right: 20, left: 20),
          child: SubmitButton(
            onPressed: () async {
              setState(() {
                loading = true;
              });

              await Provider.of<CashierProvider>(context, listen: false)
                  .createOperation(OperationModel.fromJson({
                "isIncome": false,
                "value": double.tryParse(expenseCtrl.text.toEnglishDigit()),
                "source": "expense",
                "userId": !userProvider!.isAdmin! ? userProvider.uid : user.uid,
                "opTime": DateTime.now().toIso8601String(),
              }))
                  .catchError((error) {
                setState(() {
                  loading = false;
                });
                Navigator.pop(context);
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    content: Text(error.toString()),
                  ),
                );
                throw error;
              });
              await _expenses
                  .createExpense(ExpenseModel.fromJson({
                'notes': noteCtrl.text,
                'source': expenseType,
                'value': double.tryParse(expenseCtrl.text.toEnglishDigit()),
                'mandobId':
                    !userProvider.isAdmin! ? userProvider.uid : user.uid,
                'createdAt': DateTime.now().toString()
              }))
                  .then((value) async {
                setState(() {
                  loading = false;
                });

                Navigator.pop(context);
                Navigator.pushReplacement(context,
                    MaterialPageRoute(builder: (context) => ExpensesPage()));

                showBar(context,
                    context.isEng ? 'Added successfully' : 'تمت الإضافة بنجاح',
                    backgroundColor: ColorManager.primaryColor,
                    indicatorColor: ColorManager.secondaryColor,
                    icon: Icons.done_all);
              });

              // await Provider.of<ExpensesProvider>(context, listen: false)
              //     .fetchExpenses(
              //
              // );
            },
            label: context.isEng ? 'Save Expense' : 'حفظ المصروف',
          ),
        ),
        appBar: AppBar(
          leading: const BackButton(color: Colors.white),
          title: Text(context.isEng ? 'Add New Expense' : 'اضافة مصروف جديد'),
          backgroundColor: ColorManager.primaryColor,
        ),
        body: Form(
          key: formKey,
          child: Stack(
            children: <Widget>[
              Container(
                  padding: const EdgeInsets.all(20),
                  decoration: ShapeDecoration(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15.0))),
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      const SizedBox(
                        height: 5,
                      ),
                      TextFieldWidget(
                        controller: expenseCtrl,
                        label: context.isEng ? 'Amount' : 'المبلغ',
                        textInputType: TextInputType.number,
                      ),
                      AppGaps.gap16,
                      TextFieldWidget(
                        controller: noteCtrl,
                        label: context.isEng ? 'Details' : 'التفاصيل',
                        textInputType: TextInputType.text,
                      ),
                      AppGaps.gap16,
                      dropDownFilters(userProvider),
                    ],
                  )),
              loading
                  ? const Center(
                      child: LoadingWidget(),
                    )
                  : Container()
            ],
          ),
        ),
      ),
    );
  }

  Widget dropDownFilters(userProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Choose Mandob
        userProvider.isAdmin!
            ? Consumer<UserProvider>(builder: (context, mandobD, child) {
                if (mandobD.users!.isEmpty) {
                  return const Center(child: LoadingWidget());
                }

                if (mandob == '') {
                  mandob = mandobD.users![0].name.toString();

                  user = mandobD.users!.firstWhere((val) {
                    return val.name == mandob;
                  });
                }

                return Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(context.isEng ? 'Representative' : 'المندوب'),
                    const SizedBox(
                      width: 5,
                    ),
                    DropdownButton<String>(
                        value: mandob,
                        underline: Container(),
                        items: [
                          ...mandobD.users!.map((value) {
                            return DropdownMenuItem<String>(
                              value: value.name,
                              child: Text(
                                value.name ?? "",
                                style: const TextStyle(color: Colors.black),
                              ),
                            );
                          })
                        ],
                        onChanged: (val) {
                          setState(() {
                            mandob = val!;

                            user = mandobD.users!.firstWhere((val) {
                              return val.name == mandob;
                            });
                          });
                        }),
                  ],
                );
              })
            : Container(),

        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(context.isEng ? 'Type' : 'النوع'),
            DropdownButton<String>(
                value: expenseType,
                hint: Text(context.isEng ? 'Unit' : 'الوحدة'),
                underline: Container(),
                items: <String>[
                  context.isEng ? 'General' : 'عامة',
                  context.isEng ? 'Gasoline' : 'بنزين',
                  context.isEng ? 'Other' : 'أخرى',
                ].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: const TextStyle(color: Colors.black),
                    ),
                  );
                }).toList(),
                onChanged: (val) {
                  setState(() {
                    expenseType = val!;
                  });
                }),
          ],
        ),
      ],
    );
  }
}
