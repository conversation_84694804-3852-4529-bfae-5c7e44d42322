import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/expense_model.dart';

import '../../../theme.dart';

class TopExpensesSection extends StatelessWidget {
  const TopExpensesSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Table(columnWidths: const {
            0: FlexColumnWidth(1.5),
            1: FlexColumnWidth(1.5),
            2: FlexColumnWidth(2),
            3: <PERSON>lex<PERSON><PERSON>umnWidth(1.5),
            4: FlexColumnWidth(1.3),
          }, children: [
            TableRow(
              children: [
                Text(
                  isEng ? 'Date' : 'التاريخ',
                  style: Them.tableHeader,
                  textAlign: TextAlign.center,
                ),
                Text(
                  isEng ? 'Type' : 'النوع',
                  style: Them.tableHeader,
                  textAlign: TextAlign.center,
                ),
                Text(
                  isEng ? 'Details' : 'التفاصيل',
                  style: Them.tableHeader,
                  textAlign: TextAlign.center,
                ),
                Text(
                  isEng ? 'Amount' : 'المبلغ',
                  style: Them.tableHeader,
                  textAlign: TextAlign.center,
                ),
                Text(
                  isEng ? 'Payment Method' : 'طريقة الدفع',
                  style: Them.tableHeader,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ]),
        ),
        const Divider(
          color: Color(0xff06c4f1),
          thickness: 2,
          height: 0,
        ),
      ],
    );
  }
}

class ExpenseTableWidget extends StatelessWidget {
  final List<ExpenseModel>? data;

  const ExpenseTableWidget({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;

    return SingleChildScrollView(
        child: Column(
      children: [
        // titles
        Table(
          columnWidths: const {
            0: FlexColumnWidth(1.5),
            1: FlexColumnWidth(1.5),
            2: FlexColumnWidth(2),
            3: FlexColumnWidth(1.5),
            4: FlexColumnWidth(1.3),
          },
          children: [
            TableRow(
              decoration: BoxDecoration(
                color: Colors.blueGrey.shade50,
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    isEng ? 'Date' : 'التاريخ',
                    style: Them.tableHeader,
                    textAlign: TextAlign.center,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    isEng ? 'Type' : 'النوع',
                    style: Them.tableHeader,
                    textAlign: TextAlign.center,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    isEng ? 'Details' : 'التفاصيل',
                    style: Them.tableHeader,
                    textAlign: TextAlign.center,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    isEng ? 'Amount' : 'المبلغ',
                    style: Them.tableHeader,
                    textAlign: TextAlign.center,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    right: 8.0,
                    top: 8,
                    bottom: 8,
                  ),
                  child: Text(
                    isEng ? 'Payment' : 'الدفع',
                    style: Them.tableHeader,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ],
        ),
        Table(
            border: TableBorder.all(color: Colors.grey.shade300),
            defaultVerticalAlignment: TableCellVerticalAlignment.middle,
            columnWidths: const {
              0: FlexColumnWidth(1.5),
              1: FlexColumnWidth(1.5),
              2: FlexColumnWidth(2),
              3: FlexColumnWidth(1.7),
              4: FlexColumnWidth(1),
            },
            children: data!
                .map((state) => TableRow(children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            state.createdAt == null
                                ? ''
                                : state.createdAt.toString().substring(0, 10),
                            style: Them.tableCell,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          state.source == null ? '' : state.source!,
                          style: Them.tableCell
                              .copyWith(fontWeight: FontWeight.normal),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          state.notes == null ? '' : state.notes!,
                          style: Them.tableCell
                              .copyWith(fontWeight: FontWeight.normal),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          state.value == null
                              ? ''
                              : state.value!.toStringAsFixed(2),
                          style: Them.tableCell,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      state.source == null
                          ? const Center()
                          : Text(
                              isEng ? 'Cash' : 'نقداََ',
                              style: Them.tableCell,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                            )
                    ]))
                .toList()),
      ],
    ));
  }
}
