import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/quantity_model.dart';
import 'package:mandob/pages/repositories_page/components/delete_repository_dialog.dart';
import 'package:mandob/pages/repositories_page/components/repository_form_screen.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:provider/provider.dart';

import '../../utils/loading_widget.dart';

class MainRepositoryPage extends StatefulWidget {
  const MainRepositoryPage({super.key});

  @override
  State<MainRepositoryPage> createState() => _MainRepositoryPageState();
}

class _MainRepositoryPageState extends State<MainRepositoryPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.push(context,
            MaterialPageRoute(builder: (_) => const AddRepositoryFormScreen())),
        child: const Icon(Icons.add),
      ),
      body: Consumer<RepositoryProvider>(builder: (context, repos, child) {
        return FutureBuilder(
            future: repos.fetchRepositories(),
            builder: (context, snapshot) {
              return snapshot.connectionState == ConnectionState.waiting
                  ? const LoadingWidget()
                  : Consumer<ProductsProvider>(
                      builder: (context, _product, child) {
                        return FutureBuilder(
                            future: _product.fetchProducts(),
                            builder: (context, snapshot) {
                              return snapshot.connectionState ==
                                      ConnectionState.waiting
                                  ? const LoadingWidget()
                                  : ListView.separated(
                                      shrinkWrap: true,
                                      padding: const EdgeInsets.all(12),
                                      itemCount: repos.repositories.length,
                                      separatorBuilder: (context, index) {
                                        return const Padding(
                                            padding: EdgeInsets.only(
                                                left: 80, right: 80),
                                            child: Divider());
                                      },
                                      itemBuilder: (context, index) {
                                        var repositoryData = repos
                                            .repositories.reversed
                                            .toList()[index];
                                        var myRepoProducts =
                                            repositoryData.products ?? {};

                                        return ExpansionTile(
                                          backgroundColor: Colors.white70,
                                          title: Padding(
                                            padding: const EdgeInsets.only(
                                                top: 8, bottom: 8),
                                            child: Stack(
                                              alignment: Alignment.topLeft,
                                              children: [
                                                ListTile(
                                                    isThreeLine: true,
                                                    minVerticalPadding: 5,
                                                    title: Text(
                                                        repositoryData.name ??
                                                            ''),
                                                    subtitle: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(repositoryData
                                                                .location
                                                                ?.toString() ??
                                                            ''),
                                                        myRepoProducts.isEmpty
                                                            ? const Text(
                                                                "لا توجد منتجات")
                                                            : Text(
                                                                'كمية المنتجات: ${myRepoProducts.length} '),
                                                      ],
                                                    ),
                                                    trailing: Column(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        GestureDetector(
                                                          onTap: () => Navigator.push(
                                                              context,
                                                              MaterialPageRoute(
                                                                  builder: (_) =>
                                                                      AddRepositoryFormScreen(
                                                                          repo:
                                                                              repositoryData))),
                                                          child: const Icon(
                                                            Icons.edit,
                                                            size: 25,
                                                            color: Colors.blue,
                                                          ),
                                                        ),
                                                        GestureDetector(
                                                          onTap: () =>
                                                              showDialog(
                                                                  context:
                                                                      context,
                                                                  builder: (_) =>
                                                                      DeleteRepositoryDialog(
                                                                        repoId:
                                                                            repositoryData.id,
                                                                      )),
                                                          child: const Icon(
                                                            Icons.delete,
                                                            size: 25,
                                                            color: Colors.red,
                                                          ),
                                                        ),
                                                      ],
                                                    )),
                                              ],
                                            ),
                                          ),
                                          children: [
                                            Builder(builder: (context) {
                                              return Consumer<
                                                      QuantitiesProvider>(
                                                  builder: (context,
                                                      _quantities, child) {
                                                return FutureBuilder(
                                                    future: _quantities
                                                        .fetchQuantities(),
                                                    builder:
                                                        (context, snapshot) {
                                                      return snapshot
                                                                  .connectionState ==
                                                              ConnectionState
                                                                  .waiting
                                                          ? const Center(
                                                              child:
                                                                  LoadingWidget())
                                                          : ListView.separated(
                                                              shrinkWrap: true,
                                                              physics:
                                                                  const ScrollPhysics(
                                                                      parent:
                                                                          NeverScrollableScrollPhysics()),
                                                              itemCount: _product
                                                                  .products!
                                                                  .where((element) =>
                                                                      myRepoProducts
                                                                          .containsKey(
                                                                              element.id))
                                                                  .length,
                                                              separatorBuilder:
                                                                  (context,
                                                                      index) {
                                                                return const Divider(
                                                                  thickness: 2,
                                                                );
                                                              },
                                                              itemBuilder:
                                                                  (context,
                                                                      index) {
                                                                var productData = _product
                                                                    .products!
                                                                    .where((element) =>
                                                                        myRepoProducts
                                                                            .containsKey(element.id))
                                                                    .toList();

                                                                final product =
                                                                    productData[
                                                                        index];
                                                                int? prodQuant;

                                                                if (_quantities
                                                                    .quantities
                                                                    .isNotEmpty) {
                                                                  final quants = _quantities.quantities.firstWhere(
                                                                      (element) =>
                                                                          element
                                                                              .productId ==
                                                                          product
                                                                              .id,
                                                                      orElse: () =>
                                                                          QuantityModel());

                                                                  if (quants
                                                                          .quantities !=
                                                                      null) {
                                                                    prodQuant = quants
                                                                            .quantities![
                                                                        repositoryData
                                                                            .id];
                                                                  }
                                                                } else {
                                                                  prodQuant = 0;
                                                                }
                                                                log('sdsaf ${prodQuant}');
                                                                return Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .only(
                                                                          top:
                                                                              8,
                                                                          bottom:
                                                                              8),
                                                                  child: Stack(
                                                                    alignment:
                                                                        Alignment
                                                                            .topLeft,
                                                                    children: [
                                                                      ListTile(
                                                                        // isThreeLine: true,
                                                                        minVerticalPadding:
                                                                            0,
                                                                        title:
                                                                            Row(
                                                                          mainAxisAlignment:
                                                                              MainAxisAlignment.spaceBetween,
                                                                          children: [
                                                                            Text(product.name?.toString() ??
                                                                                ''),
                                                                            Text('${context.currency} ${myRepoProducts[product.id]}'),
                                                                          ],
                                                                        ),
                                                                        subtitle:
                                                                            Row(
                                                                          crossAxisAlignment:
                                                                              CrossAxisAlignment.start,
                                                                          children: [
                                                                            Text(context.isEng
                                                                                ? 'Quantity: ' '${prodQuant == null ? 0 : prodQuant.toString()}'
                                                                                : 'الكمية: ' '${prodQuant == null ? 0 : prodQuant.toString()}'),
                                                                            const SizedBox(
                                                                              width: 10,
                                                                            ),
                                                                            Text(product.type?.toString() ??
                                                                                ''),
                                                                          ],
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                );
                                                              },
                                                            );
                                                    });
                                              });
                                            })
                                          ],
                                        );
                                      });
                            });
                      },
                    );
            });
      }),
    );
  }
}
