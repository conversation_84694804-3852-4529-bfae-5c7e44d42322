import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/product_model.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/utils/show_bar/messages.dart';
import 'package:persian_number_utility/persian_number_utility.dart';
import 'package:provider/provider.dart';

import '../../../utils/loading_widget.dart';

class AddRepositoryFormScreen extends StatefulWidget {
  final RepositoryModel? repo;

  const AddRepositoryFormScreen({super.key, this.repo});

  @override
  State<StatefulWidget> createState() => _AddRepositoryScreen();
}

class _AddRepositoryScreen extends State<AddRepositoryFormScreen> {
  @override
  void initState() {
    if (widget.repo != null) {
      nameCtrl.text = widget.repo!.name!;
      locationCtrl.text = widget.repo!.location!;
    }
    super.initState();
  }

  bool loading = false;

  var nameCtrl = TextEditingController();
  var locationCtrl = TextEditingController();

  var formKey = GlobalKey<FormState>();

  var nameFocus = FocusNode();
  var locationFocus = FocusNode();

  Future addRepository() async {
    if (!formKey.currentState!.validate()) return;
    setState(() {
      loading = true;
    });
    final reposProv = Provider.of<RepositoryProvider>(context, listen: false);
    Map<String, dynamic>? storeProducts;
    if (_selectedStores.isNotEmpty) {
      storeProducts =
          _selectedStores.map((key, value) => MapEntry(key.id!, key.price));
    }

    final repoModel = RepositoryModel.fromJson({
      'name': nameCtrl.text,
      'createdAt': DateTime.now().toIso8601String(),
      'location': locationCtrl.text,
      "products": jsonEncode(storeProducts)
    });

    await reposProv.createRepository(repoModel).then((repo) async {
      await reposProv.fetchRepositories();
    });
    if (_selectedStores.isEmpty) {
      setState(() {
        loading = false;
      });
      Navigator.pop(context);
      return;
    }

    await modifyProductsQuantity();

    if (mounted) {
      setState(() {
        loading = false;
      });

      showDoneMessageAndClose(context);
    }
  }

  Future<void> modifyProductsQuantity() async {
    final reposProv = Provider.of<RepositoryProvider>(context, listen: false);

    final repo = reposProv.repositories
        .firstWhere((element) => element.name == nameCtrl.text);
    final prods = _selectedStores.map((key, value) => MapEntry(key.id!, value));

    prods.entries.forEach((element) async {
      final int val = element.value;
      await Provider.of<QuantitiesProvider>(context, listen: false)
          .modifyQuantity(
              productId: element.key, storeId: repo.id!, quantity: val);
    });
    // await Provider.of<QuantitiesProvider>(context, listen: false)
    //     .fetchQuantities();
  }

  Future editRepository() async {
    if (!formKey.currentState!.validate()) return;
    try {
      setState(() {
        loading = true;
      });
      final reposProv = Provider.of<RepositoryProvider>(context, listen: false);

      final repoModel = RepositoryModel.fromJson({
        "name": nameCtrl.text,
        "location": locationCtrl.text,
        "products": jsonEncode(widget.repo!.products),
      });
      await reposProv.editRepository(widget.repo!.id!, repoModel);

      if (mounted) {
        setState(() {
          loading = false;
        });
        showDoneMessageAndClose(context, true);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          loading = false;
        });
        showErrorMessageAndClose(context);
      }
    }
  }

  void onSelect(value, bool add) {
    if (add) {
      _selectedStores[value] = 0;
    } else {
      _selectedStores.remove(value);
    }
    setState(() {});

    //add
    //         ? _selectedStores.addEntries([MapEntry(value, 0)])
    //         : _selectedStores.remove(value);
  }

  String? type;
  final Map<ProductModel, int> _selectedStores = {};

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (MediaQuery.of(context).viewInsets.bottom != 0) {
          nameFocus.unfocus();
          locationFocus.unfocus();
        } else {
          Navigator.pop(context);
        }
        return true;
      },
      child: Scaffold(
          appBar: AppBar(
            title: Text(widget.repo == null
                ? (context.isEng ? "Add Repository" : " اضافة مخزن")
                : (context.isEng ? "Edit Repository" : "تعديل مخزن")),
            actions: [
              IconButton(
                  icon: const Icon(Icons.done_all),
                  onPressed: () async {
                    widget.repo == null ? addRepository() : editRepository();
                  })
            ],
          ),
          body: Stack(
            children: [
              Form(
                  key: formKey,
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12.0, vertical: 20),
                        child: TextFormField(
                          focusNode: nameFocus,
                          controller: nameCtrl,
                          onChanged: (v) {},
                          decoration: InputDecoration(
                            labelText:
                                context.isEng ? 'Store name' : ' اسم المخزن',
                          ),
                          validator: (String? value) {
                            if (value!.isEmpty) {
                              return context.isEng
                                  ? "Store name is empty"
                                  : "اسم المخزن فارغ";
                            }
                            return null;
                          },
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12.0, vertical: 20),
                        child: TextFormField(
                          focusNode: locationFocus,
                          controller: locationCtrl,
                          onChanged: (v) {},
                          decoration: InputDecoration(
                            labelText: context.isEng ? 'Location' : ' الموقع ',
                          ),
                          validator: (String? value) {
                            if (value!.isEmpty) {
                              return context.isEng
                                  ? "Location is empty"
                                  : "الموقع فارغ";
                            }
                            return null;
                          },
                        ),
                      ),
                      if (widget.repo == null)
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Card(
                              child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              TextButton(
                                  onPressed: () => showDialog(
                                        context: context,
                                        builder: (context) => Dialog(
                                            child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.stretch,
                                          children: [
                                            Consumer<ProductsProvider>(builder:
                                                (context, products, child) {
                                              if (products.products == null) {
                                                return Center(
                                                  child: Text(context.isEng
                                                      ? 'No Products'
                                                      : 'لا يوجد منتجات'),
                                                );
                                              }
                                              return Flexible(
                                                  fit: FlexFit.tight,
                                                  child: products
                                                          .products!.isEmpty
                                                      ? const Center(
                                                          child:
                                                              LoadingWidget(),
                                                        )
                                                      : ListView(
                                                          shrinkWrap: true,
                                                          children: products
                                                              .products!
                                                              .map((store) =>
                                                                  Padding(
                                                                      padding: const EdgeInsets
                                                                          .symmetric(
                                                                          horizontal:
                                                                              3.0,
                                                                          vertical:
                                                                              3.0),
                                                                      child:
                                                                          StoresLI(
                                                                        isSelected: _selectedStores
                                                                            .keys
                                                                            .where((element) =>
                                                                                element.id ==
                                                                                store.id)
                                                                            .isNotEmpty,
                                                                        product:
                                                                            store,
                                                                        onSelect:
                                                                            onSelect,
                                                                      )))
                                                              .toList()));
                                            }),
                                            ElevatedButton(
                                                onPressed: () {
                                                  Navigator.pop(context);
                                                  setState(() {});
                                                },
                                                child: Text(context.isEng
                                                    ? "Done"
                                                    : "تم"))
                                          ],
                                        )),
                                      ),
                                  child: Text(context.isEng
                                      ? "Select Products"
                                      : "اختر المنتجات"))
                            ],
                          )),
                        ),
                      ..._selectedStores.entries
                          .map((item) => Card(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 15.0),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Flexible(
                                        flex: 4,
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8.0, vertical: 15),
                                          child: Text(item.key.name!),
                                        ),
                                      ),
                                      Flexible(
                                        flex: 2,
                                        child: TextFormField(
                                          initialValue: '0',
                                          textAlign: TextAlign.center,
                                          decoration: InputDecoration(
                                              border: InputBorder.none,
                                              fillColor: Colors.grey.shade300,
                                              filled: true),
                                          onChanged: (newValue) {
                                            if (newValue.isEmpty) {
                                              _selectedStores[item.key] = 0;
                                            } else {
                                              _selectedStores[item.key] =
                                                  int.parse(newValue
                                                      .toEnglishDigit());
                                              // _selectedStores.update(
                                              //     item.key,
                                              //         (value) => value = int.parse(
                                              //         newValue.toEnglishDigit()));
                                            }
                                          },
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ))
                          .toList(),
                    ],
                  )),
              if (loading)
                const AlertDialog(
                  content: SizedBox(
                      height: 100, child: Center(child: LoadingWidget())),
                )
            ],
          )),
    );
  }
}

class StoresLI extends StatefulWidget {
  final ProductModel product;
  final Function onSelect;
  final bool isSelected;

  const StoresLI({
    Key? key,
    required this.product,
    required this.onSelect,
    required this.isSelected,
  }) : super(key: key);

  @override
  State<StoresLI> createState() => _StoresLIState();
}

class _StoresLIState extends State<StoresLI> {
  @override
  void initState() {
    checkValue = widget.isSelected;
    super.initState();
  }

  bool checkValue = false;

  @override
  Widget build(BuildContext context) {
    return CheckboxListTile(
      key: ValueKey(widget.product.id),
      tileColor: Colors.grey.shade300,
      value: checkValue,
      onChanged: (value) {
        setState(() {
          checkValue = value!;
        });
        widget.onSelect(widget.product, value);
      },
      title: Text(widget.product.name!),
    );
  }
}
