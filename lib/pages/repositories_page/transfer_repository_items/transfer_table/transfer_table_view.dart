import 'package:flutter/material.dart';
import 'package:mandob/models/product_model.dart';
import 'package:mandob/models/transition_model.dart';
import 'package:mandob/pages/repositories_page/transfer_repository_items/components/row_transfer_widget.dart';
import 'package:mandob/theme.dart';

class TransferTable extends StatelessWidget {
  final List<TransitionModel> transitions;
  TransferTable({Key? key, required this.transitions})
      : super(key: key);

  List<ProductModel> filteredInvoicesProducts = [];


  @override
  Widget build(BuildContext context) {

   List<TransitionModel> transitionsD = transitions.reversed.toList();

    return Expanded(
      child: Material(
          elevation: 0,
          shadowColor: Colors.blue,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(30), topRight: Radius.circular(30)),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 0.0),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Table(columnWidths: {
                    0: FlexColumnWidth(2.5),
                    1: FlexColumnWidth(1.9),
                    2: FlexColumnWidth(1.8),
                    3: FlexColumnWidth(3),
                  }, children: [
                    TableRow(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(right: 15.0),
                          child: Text(
                            "رقم الفاتوره",
                            style: Them.tableHeader,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Text(
                          "التاريخ",
                          style: Them.tableHeader,
                          textAlign: TextAlign.center,
                        ),
                        Text(
                          "الاجمالى",
                          style: Them.tableHeader,
                          textAlign: TextAlign.center,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(right: 28.0),
                          child: Text(
                            "من",
                            style: Them.tableHeader,
                            textAlign: TextAlign.right,
                          ),
                        ),
                        SizedBox(
                          width: 5,
                        ),
                      ],
                    ),
                  ]),
                ),
                Divider(
                  color: const Color(0xff06c4f1),
                  thickness: 2,
                  height: 0,
                ),
                Expanded(
                    child: SingleChildScrollView(
                  child: Table(
                      defaultVerticalAlignment:
                          TableCellVerticalAlignment.middle,
                      columnWidths: {
                        0: FlexColumnWidth(2.5),
                        1: FlexColumnWidth(2),
                        2: FlexColumnWidth(2.0),
                        3: FlexColumnWidth(2.5),
                      },
                      children: [
                        TableRow(children: [
                          Container(
                            height: MediaQuery.of(context).size.height - 250,
                            child: ListView.builder(
                              shrinkWrap: true,
                              itemCount: transitionsD.length,
                              physics: BouncingScrollPhysics(),
                              itemBuilder: (context, index) {
                                return RowTransferWidget(
                                  index: index,
                                  receiptList: transitionsD,
                                );
                              },
                            ),
                          )
                        ])
                      ]),
                ))
              ],
            ),
          )),
    );
  }
}
