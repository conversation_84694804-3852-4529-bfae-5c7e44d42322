import 'package:flutter/material.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/models/transition_model.dart';
import 'package:mandob/pages/main_components/invoice_floating_buttons.dart';
import 'package:mandob/pages/repositories_page/transfer_repository_items/transfer_table/transfer_table_view.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/providers/transitions_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:provider/provider.dart';

import '../../../../../../main.dart';
import '../components/main_transfer_items_dialog.dart';

class MainTransferTable extends StatefulWidget {
  const MainTransferTable({Key? key}) : super(key: key);

  @override
  State<MainTransferTable> createState() => _MainTransferTableState();
}

List<Map<String, dynamic>> addedItems = [];
Map<String, dynamic> addedMap = {};
Map<String, bool> isNormalPrice =
    {}; //Or bulkPrice (key: productId, value: isNormalPrice)

Map<String, bool> isQuantity = {};

class _MainTransferTableState extends State<MainTransferTable> {
  String? invoiceTypeValue = 'المبيعات';

  String repoVal = '';

  List<RepositoryModel> repositoryListMap = [];
  List<RepositoryModel> filteredRepositoryListMap = [];

  String receiverRepository = '';

  late double totalItemsPrice = 0.0;

  @override
  void initState() {
    Provider.of<RepositoryProvider>(context, listen: false).fetchRepositories();
    Provider.of<ProductsProvider>(context, listen: false).fetchProducts();
    Provider.of<QuantitiesProvider>(context, listen: false).fetchQuantities();

    super.initState();
  }

  bool visible = false;

  bool loading = false;

  late List<TransitionModel> filteredTransfers = [];

  RepositoryModel? storeRepo;

  @override
  Widget build(BuildContext context) {
    final user = Provider.of<UserProvider>(context, listen: false).activeUser;

    return Consumer<TransitionsProvider>(
        builder: (context, invoicesData, child) {
      // filteredTransfers = invoicesData.transitions;
      return FutureBuilder(
          future: invoicesData.fetchTransition(),
          builder: (context, snapshot) {
            return Consumer<RepositoryProvider>(
                builder: (context, repo, child) {
              if (repo.repositories.isEmpty) {
                return Scaffold(
                    floatingActionButton: user!.isAdmin!
                        ? FloatingButtonWidget(
                            onPressed: () => showDialog(
                                context: context,
                                builder: (_) => TransferItemsDialog(
                                      repo: repo,
                                    )),
                            label: 'تحويل جديد')
                        : Container());
              }
              if (invoicesData.transitions.isEmpty) {
                return Scaffold(
                  floatingActionButton: user!.isAdmin!
                      ? FloatingButtonWidget(
                          onPressed: () => showDialog(
                              context: context,
                              builder: (_) => TransferItemsDialog(
                                    repo: repo,
                                  )),
                          label: 'تحويل جديد')
                      : Container(),
                  body: const Center(
                    child: Text('لا يوجد تحويلات'),
                  ),
                );
              }

              if (!user!.isAdmin!) {
                storeRepo = repo.repositories.firstWhere((val) {
                  return val.id == user.storeId;
                }, orElse: () => RepositoryModel.fromJson({'': ''}));

                if (filteredTransfers.isEmpty) {
                  invoicesData.transitions.forEach((element) {
                    if (element.to == storeRepo!.name) {
                      filteredTransfers.add(element);
                    }
                  });
                }
                // }
              }

              if (!user.isAdmin! && storeRepo!.id == null) {
                return const Center(
                  child: Text('أنت غير منتمي لمخزن !'),
                );
              }

              return Scaffold(
                  floatingActionButton: user.isAdmin!
                      ? Padding(
                          padding: const EdgeInsets.only(right: 25.0),
                          child: FloatingButtonWidget(
                              onPressed: () => showDialog(
                                  context: context,
                                  builder: (_) => TransferItemsDialog(
                                        repo: repo,
                                      )),
                              label: 'تحويل جديد'),
                        )
                      : Container(),
                  body: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      TransferTable(
                        transitions: user.isAdmin!
                            ? invoicesData.transitions
                            : filteredTransfers,
                      )
                    ],
                  ));
            });
          });
    });
  }

  void onSelectionChanged(value) {
    if (value.startDate != null) {
      sDate = value.startDate;
    }
    if (value.endDate != null) {
      eDate = value.endDate;
    }
    setState(() {});
  }
}
