import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/pages/repositories_page/transfer_repository_items/components/view_transfer_items_widget.dart';
import 'package:mandob/pages/repositories_page/transfer_repository_items/transfer_table/main_transfer_table.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:provider/provider.dart';

import '../../../../utils/loading_widget.dart';

class TransferItemsDialog extends StatefulWidget {
  final repo;

  TransferItemsDialog({required this.repo});

  @override
  State<StatefulWidget> createState() => _TransferItemsDialog();
}

class _TransferItemsDialog extends State<TransferItemsDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  @override
  void initState() {
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  bool loading = false;

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);

    return WillPopScope(
      onWillPop: () async {
        Navigator.pop(context);
        addedItems.clear();
        addedMap.clear();
        return true;
      },
      child: Stack(
        children: <Widget>[
          Padding(
            padding: mediaQuery.viewInsets,
            child: Center(
              child: Material(
                color: Colors.transparent,
                child: ScaleTransition(
                  scale: scaleAnimation,
                  child: SingleChildScrollView(
                    child: Container(
                        width: 350,
                        // height: MediaQuery.of(context).size.height / 2,
                        padding: const EdgeInsets.all(15),
                        decoration: ShapeDecoration(
                            color: Colors.white,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(15.0))),
                        child: Consumer<ProductsProvider>(
                            builder: (context, productData, child) {
                          if (productData.products == null) {
                            return Center(
                              child: Text(context.isEng
                                  ? 'No Products !'
                                  : 'لا يوجد منتجات !'),
                            );
                          }
                          return Container(
                            // height: 500,
                            padding: const EdgeInsets.only(top: 10),
                            child: ViewTransferItemsWidget(
                              productData: productData,
                              repo: widget.repo,
                            ),
                          );
                        })),
                  ),
                ),
              ),
            ),
          ),
          loading
              ? const Center(
                  child: LoadingWidget(),
                )
              : Container()
        ],
      ),
    );
  }
}
