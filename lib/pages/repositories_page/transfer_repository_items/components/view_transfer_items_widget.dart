import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:mandob/models/product_model.dart';
import 'package:mandob/models/quantity_model.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/models/transition_model.dart';
import 'package:mandob/pages/repositories_page/transfer_repository_items/transfer_table/main_transfer_table.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/providers/transitions_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:mandob/utils/text_field.dart';
import 'package:persian_number_utility/persian_number_utility.dart';
import 'package:provider/provider.dart';

import '../../../../utils/color_manager.dart';
import '../../../../utils/loading_widget.dart';
import 'add_transfer_items_widget.dart';

class ViewTransferItemsWidget extends StatefulWidget {
  final RepositoryProvider repo;
  final ProductsProvider productData;

  ViewTransferItemsWidget(
      {Key? key, required this.repo, required this.productData})
      : super(key: key);

  @override
  State<ViewTransferItemsWidget> createState() =>
      _ViewTransferItemsWidgetState();
}

class _ViewTransferItemsWidgetState extends State<ViewTransferItemsWidget> {
  String repoVal = '';

  List<RepositoryModel> repositoryListMap = [];

  List<ProductModel> filteredProducts = [];

  late RepositoryModel filteredRepositoryListMap;

  String receiverRepository = '';

  String storeId = '';

  late double totalItemsPrice = 0.0;

  bool visible = false;

  bool loading = false;

  bool moreThanQuantity = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    addLocalData(widget.repo);
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(builder: (context, setState) {
      final user = Provider.of<UserProvider>(context, listen: false).activeUser;

      final List<QuantityModel> _quantities =
          Provider.of<QuantitiesProvider>(context, listen: false).quantities;

      return Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                //Choose Repositories
                Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.all(user!.isAdmin! ? 8.0 : 8),
                      child: Wrap(
                        // mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Row(
                            children: [
                              const Text('من'),
                              const SizedBox(
                                width: 10,
                              ),
                              DropdownButton<String>(
                                  value: repoVal,
                                  underline: Container(),
                                  items: [
                                    ...widget.repo.repositories.map((value) {
                                      return DropdownMenuItem<String>(
                                        value: value.name,
                                        child: new Text(
                                          value.name ?? "",
                                          style: const TextStyle(
                                              color: Colors.black),
                                        ),
                                      );
                                    })
                                  ],
                                  onChanged: (val) {
                                    onChangedRepoVal(val);
                                  }),
                            ],
                          ),
                          user.isAdmin!
                              ? Row(
                                  children: [
                                    const Text('إلى'),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    DropdownButton<String>(
                                        value: receiverRepository,
                                        underline: Container(),
                                        items: [
                                          ...widget.repo.repositories
                                              .map((value) {
                                            return DropdownMenuItem<String>(
                                              value: value.name,
                                              child: new Text(
                                                value.name ?? "",
                                                style: const TextStyle(
                                                    color: Colors.black),
                                              ),
                                            );
                                          })
                                        ],
                                        onChanged: (val) {
                                          if (val == repoVal) {
                                            showBar(context,
                                                'لا يمكنك التحويل إلى نفس مخزنك !');
                                          } else {
                                            setState(() {
                                              receiverRepository = val!;
                                            });
                                          }
                                        }),
                                  ],
                                )
                              : Container(),
                        ],
                      ),
                    ),
                    Container(
                      height: MediaQuery.of(context).size.height / 1.5,
                      child: ListView.separated(
                        shrinkWrap: true,
                        itemCount: filteredProducts.length,
                        separatorBuilder: (context, index) {
                          return const Padding(
                              padding: EdgeInsets.only(left: 80, right: 80),
                              child: Divider());
                        },
                        itemBuilder: (context, index) {
                          ProductModel product = filteredProducts[index];
                          var myRepoProducts =
                              filteredRepositoryListMap.products ?? {};

                          var prodQuant;

                          var list = _quantities.firstWhere(
                              (element) => element.productId == product.id,
                              orElse: () => QuantityModel.fromJson({"": ""}));

                          if (list != null &&
                              _quantities != null &&
                              list.quantities != null &&
                              list.quantities!.isNotEmpty &&
                              _quantities.length != 0) {
                            prodQuant = list
                                .quantities![filteredRepositoryListMap.id]
                                .toString();
                          } else {
                            prodQuant = '0';
                          }
                          if (prodQuant.toString() == 'null') {
                            prodQuant = '0';
                          }
                          return ViewItemsWidget(
                            priceFieldWidget: const SizedBox(),
                            weightFieldWidget: const SizedBox(),
                            prodQuant: prodQuant,
                            prodPrice: product.price!.toStringAsFixed(2),
                            quantityFieldWidget: addedMap
                                    .containsKey(product.id)
                                ? SizedBox(
                                    width: 100,
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 6.0),
                                      child: TextFieldWidget(
                                          label: 'كمية المنتج',
                                          textAlign: TextAlign.left,
                                          textInputType: TextInputType.number,
                                          contentPadding: const EdgeInsets.only(
                                              left: 10, top: 10, bottom: 10),
                                          initialValue: prodQuant != null &&
                                                  prodQuant.toString() == '0'
                                              ? '0'
                                              : '1',
                                          onChanged: (val) {
                                            onChangedTextField(
                                                val, prodQuant, product);
                                          }),
                                    ),
                                  )
                                : Container(),
                            productData: product,
                            checkBoxWidget: Checkbox(
                              value: addedMap.containsKey(product.id),
                              onChanged: (bool? newValue) {
                                setState(() {
                                  onChangedCheckBox(prodQuant, product);
                                });
                              },
                            ),
                            changePriceWidget: const SizedBox(),
                            changeWeightWidget: const SizedBox(),
                          );
                        },
                      ),
                    )
                  ],
                ),

                //Total price with add button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Consumer<QuantitiesProvider>(
                        builder: (context, quantitiesData, child) {
                      // if (quantitiesData.quantities.isEmpty) {
                      //   return Center(
                      //     child: LoadingWidget(),
                      //   );
                      // }

                      return GestureDetector(
                        onTap: () async {
                          if (user.isAdmin! && repoVal == receiverRepository) {
                            showBar(
                                context, 'لا يمكنك التحويل إلى نفس المخزن !');
                            return;
                          }

                          if (!user.isAdmin! &&
                              filteredRepositoryListMap.id == user.storeId) {
                            showBar(
                                context, 'لا يمكنك التحويل إلى نفس مخزنك !');
                            return;
                          }

                          if (moreThanQuantity == true) {
                            showBar(context,
                                'برجاء اختيار كمية أقل من أو تساوي كمية المنتج !');
                            return;
                          }
                          // Navigator.pop(context);
                          if (addedMap.isNotEmpty && repoVal != '') {
                            setState(() {
                              loading = true;
                            });

                            addNewTransferButton(quantitiesData);
                          } else {
                            showBar(context, 'برجاء إختيار المنتجات أولا !');
                          }
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width / 3,
                          height: 40,
                          margin: const EdgeInsets.all(8),
                          child: const Center(
                            child: Text(
                              'تحويل',
                              style: TextStyle(
                                color: Colors.white,
                              ),
                            ),
                          ),
                          decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(5)),
                        ),
                      );
                    })
                  ],
                )
              ],
            ),
          ),
          loading
              ? const Center(
                  child: LoadingWidget(),
                )
              : Container()
        ],
      );
    });
  }

  onChangedRepoVal(val) {
    if (val == receiverRepository) {
      showBar(context, 'لا يمكنك التحويل إلى نفس مخزنك !');
    } else {
      // if (addedMap.isNotEmpty) {
      if (val != repoVal) {
        addedMap.clear();
        addedItems.clear();
      }
      setState(() {
        repoVal = val!;

        filteredRepositoryListMap = repositoryListMap.firstWhere((val) {
          return val.name == repoVal;
        });

        filteredProducts = widget.productData.products!.where((element) {
          if (filteredRepositoryListMap.products == null) {
            showBar(context, 'لا يوجد منتجات في هذا المخزن !');
          }
          return filteredRepositoryListMap.products!.containsKey(element.id);
        }).toList();
      });
    }
  }

  void onChangedTextField(String val, prodQuant, product) {
    if (val == '' || val == '0' && prodQuant.toString() != '0') {
      setState(() {
        val = '1';
      });
    }

    if (int.tryParse(val.toEnglishDigit().toString()) == null) {
      setState(() {
        moreThanQuantity = true;
      });
      showBar(context, 'برجاء إدخال كمية منتج صحيحة !');
      return;
    }

    if (prodQuant != null) {
      if (int.tryParse(val.toEnglishDigit().toString())! >
          int.tryParse(prodQuant.toString())!) {
        setState(() {
          moreThanQuantity = true;
        });
        showBar(context, 'يجب اختيار كمية أقل من أو تساوي كمية المنتج !');
        return;
      }
    }

    setState(() {
      moreThanQuantity = false;
      addedMap.addAll({
        product.id.toString(): int.tryParse(val.toEnglishDigit().toString())
      });

      addedItems.forEach((element) {
        if (element['id'] == product.id) {
          element['quantity'] = int.tryParse(val.toEnglishDigit().toString());
        }
      });

      print('quantity ${addedMap}');
    });
  }

  void onChangedCheckBox(prodQuant, product) {
    if (prodQuant.toString() == 'null' ||
        prodQuant == null ||
        prodQuant.toString() == '0') {
      showBar(context, 'كمية المنتج غير متوفرة !');
      return;
    }
    if (addedMap.containsKey(product.id)) {
      addedMap.remove(product.id);
      addedItems.removeWhere((element) => element['id'] == product.id);
    } else {
      addedMap.addAll({'${product.id}': 1});
      addedItems.add({
        'id': product.id,
        'name': product.name,
        'price': product.price,
        'type': product.type,
        'quantity': 1,
      });
    }
  }

  void addNewTransferButton(quantitiesData) async {
    List<RepositoryModel> senderRepo = widget.repo.repositories
        .where((element) => element.name == repoVal)
        .toList();

    RepositoryModel receiverRepo = widget.repo.repositories.firstWhere(
        (element) => element.name == receiverRepository,
        orElse: () => RepositoryModel.fromJson({"": ""}));

    int oldSenderValue = 0;
    int oldReceiverValue = 0;

    List<QuantityModel> senderQuatitiesValue = [];
    List<QuantityModel> receiverQuatitiesValue = [];

    Map<String, dynamic>? senderProducts = {};
    Map<String, dynamic>? receiverProducts = {};

    if (senderRepo.isNotEmpty && senderRepo[0].products!.isNotEmpty) {
      senderProducts = senderRepo[0].products;
    }

    if (receiverRepo != null && receiverRepo.products != null) {
      receiverProducts = receiverRepo.products;
    }

    addedMap.forEach((key, value) async {
      if (!receiverProducts!.containsKey(key)) {
        receiverProducts.addAll({key: value});
      }
      // senderProducts!.removeWhere((val) => val == key);

      senderQuatitiesValue = quantitiesData.quantities
          .where((element) => element.productId == key)
          .toList();

      receiverQuatitiesValue = quantitiesData.quantities
          .where((element) => element.productId == key)
          .toList();

      if (senderQuatitiesValue.isNotEmpty) {
        senderQuatitiesValue[0].quantities!.forEach((key, val) {
          if (senderRepo.isNotEmpty && key == senderRepo[0].id!) {
            setState(() {
              oldSenderValue = val;
            });
          }
        });
      }

      if (receiverQuatitiesValue.isNotEmpty) {
        receiverQuatitiesValue[0].quantities!.forEach((key, val) {
          if (receiverRepo != null && key == receiverRepo.id!) {
            setState(() {
              oldReceiverValue = val;
              print('oold $oldReceiverValue');
            });
          }
        });
      }

      await Provider.of<QuantitiesProvider>(context, listen: false)
          .modifyQuantity(
              productId: key,
              storeId: senderRepo.isNotEmpty ? senderRepo[0].id! : '',
              quantity: oldSenderValue - value);

      await Provider.of<QuantitiesProvider>(context, listen: false)
          .modifyQuantity(
              productId: key,
              storeId: receiverRepo.id!,
              quantity: oldReceiverValue + value);
    });

    await Provider.of<RepositoryProvider>(context, listen: false)
        .editRepository(
            senderRepo.isNotEmpty ? senderRepo[0].id! : '',
            RepositoryModel.fromJson({
              "name": senderRepo.isNotEmpty ? senderRepo[0].name : '',
              "location": senderRepo.isNotEmpty ? senderRepo[0].location : '',
              "products": jsonEncode(senderProducts)
            }));

    await Provider.of<TransitionsProvider>(context, listen: false)
        .addTransition(TransitionModel.fromJson({
      'from': repoVal,
      'to': receiverRepository,
      'time': DateTime.now().toString(),
      // 'quantity': addedMap,
      'products': jsonEncode(addedItems),
    }))
        .then((value) {
      setState(() {
        loading = false;
      });
      addedItems.clear();
      addedMap.clear();
      Navigator.pop(context);
      showBar(context, 'تم عمل التحويل بنجاح !',
          backgroundColor: ColorManager.primaryColor,
          indicatorColor: ColorManager.secondaryColor,
          icon: Icons.done_all);
      Provider.of<TransitionsProvider>(context, listen: false)
          .fetchTransition();
      Provider.of<QuantitiesProvider>(context, listen: false).fetchQuantities();
    });
  }

  addLocalData(repo) {
    if (repoVal == '' && repo.repositories.isNotEmpty) {
      repoVal = repo.repositories[0].name.toString();

      filteredRepositoryListMap = repo.repositories.firstWhere((val) {
        return val.name == repoVal;
      });
      filteredProducts = widget.productData.products!.where((element) {
        return filteredRepositoryListMap.products == null
            ? false
            : filteredRepositoryListMap.products!.containsKey(element.id);
      }).toList();
    }

    if (receiverRepository == '' && repo.repositories.isNotEmpty) {
      receiverRepository = repo.repositories[0].name.toString();
    }

    if (repositoryListMap.isEmpty) {
      repositoryListMap = repo.repositories;
    }
  }
}
