import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';

class ViewItemsWidget extends StatelessWidget {
  final productData;
  final prodQuant;
  final prodPrice;
  final Widget checkBoxWidget;
  final Widget changePriceWidget;
  final Widget quantityFieldWidget;
  final Widget priceFieldWidget;
  final Widget weightFieldWidget;
  final Widget changeWeightWidget;

  const ViewItemsWidget({
    super.key,
    this.productData,
    required this.quantityFieldWidget,
    required this.prodQuant,
    required this.prodPrice,
    required this.checkBoxWidget,
    required this.changePriceWidget,
    required this.weightFieldWidget,
    required this.priceFieldWidget,
    required this.changeWeightWidget,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      titleAlignment: ListTileTitleAlignment.top,
      leading: Padding(
        padding: const EdgeInsets.only(bottom: 20.0),
        child: checkBoxWidget,
      ),
      contentPadding: EdgeInsets.only(
        left: context.isEng ? 0 : 10,
        right: context.isEng ? 10 : 0,
      ),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                  child: Text(
                productData.name?.toString() ?? '',
                style: const TextStyle(fontSize: 14),
                textAlign: TextAlign.center,
              )),
              Container(
                width: .5,
                height: 30,
                color: Colors.grey,
                margin: const EdgeInsets.symmetric(horizontal: 4),
              ),
              Expanded(
                child: prodQuant != null
                    ? Text(
                        context.isEng
                            ? 'Quantity: $prodQuant (${productData.type?.toString()})'
                            : 'الكمية: $prodQuant (${productData.type?.toString()})',
                        style: const TextStyle(fontSize: 14),
                        textAlign: TextAlign.center,
                      )
                    : const SizedBox(),
              ),
              Container(
                width: .5,
                height: 30,
                color: Colors.grey,
                margin: const EdgeInsets.symmetric(horizontal: 4),
              ),
              Text(
                "${context.currency} " + prodPrice,
                textAlign: TextAlign.center,
              ),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          SizedBox(
            width: MediaQuery.of(context).size.width,
            child: quantityFieldWidget,
          ),
          const SizedBox(
            height: 10,
          ),
          Row(
            children: [
              changePriceWidget,
              const SizedBox(
                width: 15,
              ),
              Expanded(child: priceFieldWidget),
            ],
          ),
          Row(
            children: [
              changeWeightWidget,
              const SizedBox(
                width: 15,
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: weightFieldWidget,
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
