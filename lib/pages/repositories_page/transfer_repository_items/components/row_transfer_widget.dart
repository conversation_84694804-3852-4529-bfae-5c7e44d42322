import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/company_info_model.dart';
import 'package:mandob/models/transition_model.dart';
import 'package:mandob/providers/company_provider.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';
import 'package:sunmi_printer_plus/enums.dart';
import 'package:sunmi_printer_plus/sunmi_printer_plus.dart';
import 'package:sunmi_printer_plus/sunmi_style.dart';
import 'package:sunmi_printer_service/sunmi_printer_service.dart' as s;

import '../../../../utils/color_manager.dart';

class RowTransferWidget extends StatefulWidget {
  final List<TransitionModel> receiptList;
  final int index;

  const RowTransferWidget({
    Key? key,
    required this.receiptList,
    required this.index,
  }) : super(key: key);

  @override
  State<RowTransferWidget> createState() => _RowTransferWidgetState();
}

class _RowTransferWidgetState extends State<RowTransferWidget> {
  double totalPrice = 0.0;

  bool printBinded = false;
  int paperSize = 0;
  String serialNumber = "";
  String printerVersion = "";

  Future<bool?> _bindingPrinter() async {
    final bool? result = await SunmiPrinter.bindingPrinter();
    return result;
  }

  @override
  void initState() {
    super.initState();
    Provider.of<CompanyInfoProvider>(context, listen: false).fetchCompanyInfo();

    _bindingPrinter().then((bool? isBind) async {
      SunmiPrinter.paperSize().then((int size) {
        setState(() {
          paperSize = size;
        });
      });

      SunmiPrinter.printerVersion().then((String version) {
        setState(() {
          printerVersion = version;
        });
      });

      SunmiPrinter.serialNumber().then((String serial) {
        setState(() {
          serialNumber = serial;
        });
      });

      setState(() {
        printBinded = isBind!;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    TransitionModel receiptData = widget.receiptList[widget.index];

    if (totalPrice == 0.0) {
      for (var element in receiptData.products!) {
        totalPrice += (element!['price']! * element['quantity']!);
      }
    }
    var infoData = Provider.of<CompanyInfoProvider>(context, listen: false);

    return ExpansionTile(
      backgroundColor: Colors.transparent,
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Text(
            receiptData.id.toString().substring(1, 7),
            style: const TextStyle(
              fontFamily: 'Droid',
              fontSize: 13,
            ),
          ),
          Text(
            receiptData.time.toString().substring(0, 10),
            style: const TextStyle(
              fontFamily: 'Droid',
              fontSize: 13,
            ),
          ),
          Text(
            totalPrice.toString(),
            style: const TextStyle(
              fontFamily: 'Droid',
              fontSize: 13,
            ),
          ),
          Container(
            width: 80,
            child: Text(
              receiptData.from.toString(),
              style: const TextStyle(
                fontFamily: 'Droid',
                fontSize: 13,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
      children: [
        Stack(
          children: [
            ListView.builder(
              shrinkWrap: true,
              padding: const EdgeInsets.all(12),
              itemCount: receiptData.products!.length,
              itemBuilder: (context, itemsIndex) {
                var items = receiptData.products![itemsIndex];
                // var quantity = '';

                // if (quantity == '') {
                //   widget.receiptList.forEach((element) {
                //     element.quantity!.forEach((key, value) {
                //       if (key == items['id']) {
                //         quantity = value.toString();
                //       }
                //     });
                //   });
                // }

                return ListTile(
                  isThreeLine: true,
                  minVerticalPadding: 5,
                  title: Text(
                    items!['name'].toString(),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 50.0),
                        child: Text('الإجمالي: ' +
                            '${context.currency} ' +
                            items['price'].toString()),
                      ),
                      Text('الكمية: ${items['quantity']}'),
                      Text(items['type']?.toString() ?? ''),
                    ],
                  ),
                );
              },
            ),
            Positioned(
                left: 25,
                top: 15,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إحمالي المنتجات: ${receiptData.products!.length}',
                      style:
                          const TextStyle(color: Colors.black54, fontSize: 14),
                    ),
                  ],
                )),
            Positioned(
              bottom: 10,
              left: 30,
              child: Row(
                children: [
                  GestureDetector(
                      onTap: () async {
                        await showDialog(
                            context: context,
                            builder: (_) => AlertDialog(
                                  content: SizedBox(
                                    height: 130,
                                    child: Column(
                                      children: [
                                        GestureDetector(
                                          onTap: () async {
                                            await printInvoice(
                                                    receiptData,
                                                    totalPrice,
                                                    infoData.companyInfo)
                                                .whenComplete(() {
                                              showBar(context,
                                                  'تمت طباعة الفاتورة بنجاح',
                                                  backgroundColor:
                                                      ColorManager.primaryColor,
                                                  indicatorColor: ColorManager
                                                      .secondaryColor,
                                                  icon: Icons.done_all);
                                            });
                                          },
                                          child: Container(
                                            height: 50,
                                            padding: const EdgeInsets.only(
                                                left: 8, right: 8),
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              color: Colors.blue,
                                            ),
                                            child: const Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text(
                                                  'طباعة roll',
                                                  style: TextStyle(
                                                      color: Colors.white),
                                                ),
                                                SizedBox(
                                                  width: 5,
                                                ),
                                                Icon(
                                                  Icons.receipt_long,
                                                  color: Colors.white,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        const SizedBox(
                                          height: 25,
                                        ),
                                        GestureDetector(
                                          onTap: () async {
                                            await printPdfInvoice(receiptData,
                                                infoData.companyInfo);
                                          },
                                          child: Container(
                                            height: 50,
                                            padding: const EdgeInsets.only(
                                                left: 8, right: 8),
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              color: Colors.deepOrangeAccent,
                                            ),
                                            child: const Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text(
                                                  'طباعة A4',
                                                  style: TextStyle(
                                                      color: Colors.white),
                                                ),
                                                SizedBox(
                                                  width: 5,
                                                ),
                                                Icon(
                                                  Icons.assignment_sharp,
                                                  color: Colors.white,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ));
                      },
                      child: Container(
                        height: 35,
                        padding: const EdgeInsets.only(left: 8, right: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: Colors.blue,
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'طباعة',
                              style: TextStyle(color: Colors.white),
                            ),
                            SizedBox(
                              width: 5,
                            ),
                            Icon(
                              Icons.print,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      )),
                  const SizedBox(
                    width: 10,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future printInvoice(TransitionModel receiptData, double? totalPrice,
      CompanyInfoModel? info) async {
    // var taxVal = receiptData.totalPrice !- receiptData.totalWithoutTax!;
    // var taxNumber = taxVal / receiptData.totalWithoutTax!;
    // var taxPercent = taxNumber * 100;

    await SunmiPrinter.initPrinter();
    await s.SunmiPrinterService.init();
    await SunmiPrinter.startTransactionPrint(true);

    //Company Info
    await SunmiPrinter.setAlignment(SunmiPrintAlign.CENTER);
    await SunmiPrinter.printText(info!.companyName.toString(),
        style: SunmiStyle(
            fontSize: SunmiFontSize.LG,
            align: SunmiPrintAlign.RIGHT,
            bold: true));

    await SunmiPrinter.printText('VAT # ${info.vatNumber.toString()}',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD,
            align: SunmiPrintAlign.RIGHT,
            bold: true));

    await SunmiPrinter.line();
    await SunmiPrinter.lineWrap(1);

    await SunmiPrinter.printText('فاتورة ضريبية',
        style: SunmiStyle(
            fontSize: SunmiFontSize.LG,
            align: SunmiPrintAlign.CENTER,
            bold: true));

    await SunmiPrinter.lineWrap(1);

    //Invoice info
    await SunmiPrinter.printText(
        'رقم الفاتورة ${receiptData.id.toString().substring(1, 7)}',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD, align: SunmiPrintAlign.RIGHT));

    // await SunmiPrinter.lineWrap(1);

    await SunmiPrinter.printText(
        'التاريخ ${DateFormat("dd-MM-yyyy hh:mm").format(receiptData.time!)}',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD, align: SunmiPrintAlign.RIGHT));

    // await SunmiPrinter.lineWrap(1);

    await SunmiPrinter.printText(
        'المخزن المحول منه: ${receiptData.from.toString()}',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD, align: SunmiPrintAlign.RIGHT));

    await SunmiPrinter.printText(
        'المخزن المحول إليه: ${receiptData.to.toString()}',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD, align: SunmiPrintAlign.RIGHT));

    await SunmiPrinter.line();

    await SunmiPrinter.setFontSize(SunmiFontSize.MD);

    await SunmiPrinter.bold();

    await s.SPrinter.columnsString(
      ["إجمالي", "سعر", "وحدة", "كمية", "الصنف"],
      width: [5, 4, 5, 3, 6],
      align: [1, 1, 1, 1, 2],
    );

    await SunmiPrinter.setFontSize(SunmiFontSize.MD);
    await s.SPrinter.setAlign(s.Align.right);

    await SunmiPrinter.resetBold();

    //Table Content
    receiptData.products!.forEach((item) async {
      await s.SPrinter.columnsString(
        [
          (item!['price'] * item['quantity']).toStringAsFixed(2),
          item['price'].toStringAsFixed(2),
          item['type'].toString(),
          item['quantity'].toString(),
          item['name'].toString()
        ],
        width: [5, 4, 5, 3, 6],
        align: [1, 2, 1, 1, 2],
      );
    });

    await SunmiPrinter.line();

    await SunmiPrinter.printText(
        'مبلغ التحويل        ${totalPrice!.toStringAsFixed(2)} ر.س',
        style: SunmiStyle(
            fontSize: SunmiFontSize.MD,
            align: SunmiPrintAlign.RIGHT,
            bold: true));

    await SunmiPrinter.lineWrap(3);

    await SunmiPrinter.exitTransactionPrint(true);
    await SunmiPrinter.cut();
  }

  Future printPdfInvoice(
      TransitionModel receiptData, CompanyInfoModel? info) async {
    final doc = pw.Document();

    final font = await rootBundle.load("assets/fonts/cairo/Cairo-Regular.ttf");
    final fontBold = await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf");

    final ttf = pw.Font.ttf(font);
    final ttfBold = pw.Font.ttf(fontBold);

    await addPdfPage(doc, ttf, ttfBold, receiptData, info);

    await showDialog(
        context: context,
        builder: (_) => AlertDialog(
              backgroundColor: Colors.transparent,
              content: Container(
                height: 500,
                width: 300,
                child: PdfPreview(
                  previewPageMargin: const EdgeInsets.all(0),
                  dynamicLayout: false,
                  allowPrinting: true,
                  canChangeOrientation: false,
                  canChangePageFormat: false,
                  canDebug: false,
                  initialPageFormat: PdfPageFormat.a4,
                  build: (format) => doc.save(),
                  useActions: true,
                ),
              ),
            ));
  }

  pw.Widget getList(TransitionModel receiptData, ttf) {
    pw.Widget cell(String text, bool header) => pw.Text(
          text,
          maxLines: 1,
          style: pw.TextStyle(
              fontSize:
                  header ? 3.5 * PdfPageFormat.mm : 2.9 * PdfPageFormat.mm,
              fontWeight: header ? pw.FontWeight.bold : pw.FontWeight.normal,
              font: ttf),
          textDirection: pw.TextDirection.rtl,
        );

    final _productsWidget = pw.ListView(children: [
      pw.Table(columnWidths: {
        0: const pw.FlexColumnWidth(0.41 * PdfPageFormat.mm),
        1: const pw.FlexColumnWidth(0.3 * PdfPageFormat.mm),
        2: const pw.FlexColumnWidth(0.27 * PdfPageFormat.mm),
        3: const pw.FlexColumnWidth(0.25 * PdfPageFormat.mm),
        4: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
      }, children: [
        pw.TableRow(children: [
          cell("الاجمالى", true),
          cell("سعر", true),
          pw.Center(child: cell("كمية", true)),
          cell("وحدة", true),
          cell("الصنف", true),
        ]),
      ]),
      pw.Divider(
          thickness: 0.5 * PdfPageFormat.mm, height: 0.5 * PdfPageFormat.mm),
      pw.Table(
          columnWidths: {
            0: const pw.FlexColumnWidth(0.41 * PdfPageFormat.mm),
            1: const pw.FlexColumnWidth(0.3 * PdfPageFormat.mm),
            2: const pw.FlexColumnWidth(0.27 * PdfPageFormat.mm),
            3: const pw.FlexColumnWidth(0.25 * PdfPageFormat.mm),
            4: const pw.FlexColumnWidth(0.45 * PdfPageFormat.mm),
          },
          children: receiptData.products!
              .map((product) => pw.TableRow(children: [
                    cell((product!["price"] * product["quantity"]).toString(),
                        false),
                    cell(product["price"].toString(), false),
                    pw.Center(
                        child: cell(product["quantity"].toString(), false)),
                    cell(product['type'].toString(), false),
                    cell(product["name"].toString(), false),
                  ]))
              .toList())
    ]);
    return _productsWidget;
  }

  addPdfPage(
      doc, ttf, ttfBold, TransitionModel receiptData, CompanyInfoModel? info) {
    doc.addPage(pw.Page(
        pageTheme: pw.PageTheme(
            pageFormat: PdfPageFormat.a4,
            theme: pw.ThemeData(bulletStyle: pw.TextStyle(font: ttf))),
        // pageFormat: PdfPageFormat.roll80,
        build: (pw.Context context) {
          return pw.Column(mainAxisSize: pw.MainAxisSize.max, children: [
            pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                children: [
                  // pw.BarcodeWidget(
                  //     width: 16.5 * PdfPageFormat.mm,
                  //     height: 16.5 * PdfPageFormat.mm,
                  //     data: generateQr(receiptData, totalPrice, info!),
                  //     barcode: pw.Barcode.qrCode()),
                  pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.end,
                      mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                      mainAxisSize: pw.MainAxisSize.max,
                      children: [
                        pw.Text(info!.companyName.toString(),
                            style: pw.TextStyle(
                                fontSize: 2.5 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttfBold),
                            textDirection: pw.TextDirection.rtl,
                            textAlign: pw.TextAlign.right),
                        pw.SizedBox(height: 20),
                        pw.Text('VAT # ${info.vatNumber}',
                            style: pw.TextStyle(
                                fontSize: 3 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttfBold),
                            textDirection: pw.TextDirection.rtl,
                            textAlign: pw.TextAlign.left),
                      ]),
                ]),
            pw.Divider(height: 5),
            pw.Center(
                child: pw.Column(children: [
              pw.Text(
                'فاتورة ضريبية',
                style: pw.TextStyle(
                    fontSize: 3 * PdfPageFormat.mm,
                    fontWeight: pw.FontWeight.bold,
                    font: ttfBold),
                textDirection: pw.TextDirection.rtl,
              ),
            ])),
            pw.SizedBox(height: 7),
            pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                mainAxisAlignment: pw.MainAxisAlignment.start,
                children: [
                  pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.end,
                      children: [
                        pw.Text(
                          receiptData.id.toString().substring(0, 10),
                          style: pw.TextStyle(
                              fontSize: 3 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttf),
                          textDirection: pw.TextDirection.ltr,
                        ),
                        pw.SizedBox(width: 5),
                        pw.Text(
                          'رقم الفاتورة : ',
                          style: pw.TextStyle(
                              fontSize: 3.5 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttf),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ]),
                  pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.end,
                      children: [
                        pw.Text(
                          DateFormat("dd-MM-yyyy hh:mm:ss")
                              .format(receiptData.time!),
                          style: pw.TextStyle(
                              fontSize: 3.5 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttf),
                          textDirection: pw.TextDirection.rtl,
                        ),
                        pw.SizedBox(width: 5),
                        pw.Text(
                          'التاريخ : ',
                          style: pw.TextStyle(
                              fontSize: 3.5 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttf),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ]),
                  pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.end,
                      children: [
                        pw.Text(
                          receiptData.from.toString(),
                          style: pw.TextStyle(
                              fontSize: 3.5 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttf),
                          textDirection: pw.TextDirection.rtl,
                        ),
                        pw.SizedBox(width: 5),
                        pw.Text(
                          'المخزن المحول منه : ',
                          style: pw.TextStyle(
                              fontSize: 3.5 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttf),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ]),
                  pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.end,
                      children: [
                        pw.Text(
                          receiptData.to.toString(),
                          style: pw.TextStyle(
                              fontSize: 3.5 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttf),
                          textDirection: pw.TextDirection.rtl,
                        ),
                        pw.SizedBox(width: 5),
                        pw.Text(
                          'المخزن المحول إليه : ',
                          style: pw.TextStyle(
                              fontSize: 3.5 * PdfPageFormat.mm,
                              fontWeight: pw.FontWeight.bold,
                              font: ttf),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ]),
                  pw.SizedBox(height: 7),
                  pw.Column(children: [
                    getList(receiptData, ttf),
                    pw.Divider(height: 5),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.SizedBox(height: 15),
                          pw.Text(
                            'رس',
                            style: pw.TextStyle(
                                fontSize: 3 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttfBold),
                            textDirection: pw.TextDirection.rtl,
                          ),
                          pw.SizedBox(width: 10),
                          pw.Text(
                            totalPrice.toStringAsFixed(2),
                            style: pw.TextStyle(
                                fontSize: 3.5 * PdfPageFormat.mm,
                                font: ttfBold),
                          ),
                          pw.Spacer(),
                          pw.Text(
                            'مبلغ التحويل',
                            style: pw.TextStyle(
                                fontSize: 3 * PdfPageFormat.mm,
                                fontWeight: pw.FontWeight.bold,
                                font: ttfBold),
                            textDirection: pw.TextDirection.rtl,
                          ),
                        ]),
                    pw.SizedBox(height: 15),
                  ]),
                ]),
          ]);
        }));
  }
}
