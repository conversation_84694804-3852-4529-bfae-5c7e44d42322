import 'package:bubble_tab_indicator/bubble_tab_indicator.dart';
import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/pages/repositories_page/transfer_repository_items/transfer_table/main_transfer_table.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:provider/provider.dart';

import 'main_repository_page.dart';

class RepositoriesPage extends StatefulWidget {
  const RepositoriesPage({
    super.key,
  });

  @override
  State<RepositoriesPage> createState() => _RepositoriesPageState();
}

class _RepositoriesPageState extends State<RepositoriesPage>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;

  // late final List<Tab> tabs;

  @override
  void initState() {
    super.initState();

    // tabs = <Tab>[
    //   Tab(text: context.isEng ? 'Repositories' : 'المخازن'),
    //   Tab(text: context.isEng ? 'Transfers' : 'التحويلات'),
    // ];

    _tabController = TabController(vsync: this, length: 2);
  }

  @override
  Widget build(BuildContext context) {
    final List<Tab> tabs = <Tab>[
      Tab(text: context.isEng ? 'Repositories' : 'المخازن'),
      Tab(text: context.isEng ? 'Transfers' : 'التحويلات'),
    ];

    final userProvider =
        Provider.of<UserProvider>(context, listen: false).activeUser;

    return DefaultTabController(
      length: 2,
      initialIndex: 0,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(userProvider!.isAdmin!
              ? MediaQuery.of(context).size.height * 0.12
              : 0),
          child: userProvider.isAdmin!
              ? AppBar(
                  backgroundColor: Colors.white,
                  elevation: 0.0,
                  bottom: TabBar(
                    unselectedLabelColor: Colors.black87,
                    controller: _tabController,
                    padding: const EdgeInsets.only(
                      right: 70,
                      left: 20,
                    ),
                    isScrollable: false,
                    indicatorSize: TabBarIndicatorSize.tab,
                    indicator: const BubbleTabIndicator(
                      indicatorHeight: 35,
                      indicatorColor: Colors.orangeAccent,
                      tabBarIndicatorSize: TabBarIndicatorSize.tab,
                    ),
                    tabs: tabs,
                  ),
                )
              : Container(),
        ),
        body: userProvider.isAdmin!
            ? TabBarView(
                controller: _tabController,
                children: const [
                  MainRepositoryPage(),
                  MainTransferTable(),
                ],
              )
            : const MainTransferTable(),
      ),
    );
  }
}
