import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/core/shared/drop_down_fields/customers_drop_down.dart';
import 'package:mandob/models/collection_model.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/pages/collections_page/components/add_collection_form.dart';
import 'package:mandob/providers/collections_provider.dart';
import 'package:mandob/providers/customers_provider.dart';
import 'package:mandob/theme.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:mandob/widgets/date_widget.dart';
import 'package:provider/provider.dart';

import '../../utils/app_bar.dart';
import '../../utils/color_manager.dart';

class CollectionsPage extends StatefulWidget {
  const CollectionsPage({super.key});

  @override
  CollectionsPageState createState() => CollectionsPageState();
}

class CollectionsPageState extends State<CollectionsPage> {
  var sDate =
      ValueNotifier<DateTime>(DateTime.now().subtract(const Duration(days: 1)));
  var eDate = ValueNotifier<DateTime>(DateTime.now());

  void onSelectionChanged() {
    setState(() {});
  }

  List<CustomerModel> mandobUsers = [];

  CustomerModel? custoFilter;

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      Future.wait([
        Provider.of<CustomersProvider>(context, listen: false)
            .fetchAllCustomers()
      ]);
    });
  }

  @override
  Widget build(BuildContext context) {
    final isEng = context
        .isEng; // Assuming context.isEng is a boolean indicating the language

    return Consumer<CollectionsProvider>(
        builder: (context, collectionsP, child) {
      return Scaffold(
        resizeToAvoidBottomInset: false,
        body: Column(
          children: [
            appBarWidget(
              context,
              title: isEng ? 'Collections' : 'التحصيلات',
              subtitle: isEng ? 'Collect +' : 'تحصيل +',
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddCollectionForm(),
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Consumer<CustomersProvider>(builder: (context, _customers, _) {
              if (_customers.isLoading) {
                return const Center(
                  child: LoadingWidget(),
                );
              }
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12.0),
                child: CustomersDropDownButton(
                  width: MediaQuery.of(context).size.width,
                  onChanged: (val) {
                    setState(() {
                      custoFilter = val!;
                    });
                  },
                  customer: custoFilter,
                ),
              );
            }),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: UiWidgets.datePicker(
                context,
                onSelectionChanged,
                startDate: sDate,
                endDate: eDate,
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15.0),
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Table(columnWidths: const {
                        0: FlexColumnWidth(0.7),
                        1: FlexColumnWidth(0.7),
                        2: FlexColumnWidth(0.7),
                        3: FlexColumnWidth(0.7),
                      }, children: [
                        TableRow(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Text(
                                isEng ? "Date" : "التاريخ",
                                style: Them.tableHeader,
                                textAlign: TextAlign.center,
                              ),
                            ),
                            Text(
                              isEng ? "Customer Name" : "اسم العميل",
                              style: Them.tableHeader,
                              textAlign: TextAlign.center,
                            ),
                            Text(
                              isEng ? "Amount" : "المبلغ",
                              style: Them.tableHeader,
                              textAlign: TextAlign.center,
                            ),
                            Text(
                              isEng ? "Payment Method" : "طريقة الدفع",
                              style: Them.tableHeader,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ]),
                    ),
                    const Divider(
                      color: ColorManager.primaryColor,
                      thickness: 2,
                      height: 0,
                    ),
                    _table(collectionsP)
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _table(CollectionsProvider collectionsP) {
    return Expanded(
        child: FutureBuilder(
            future: collectionsP.fetchCollections(
              customerId: custoFilter?.id,
              startDate: sDate.value,
              endDate: eDate.value,
            ),
            builder: (context, snapshot) {
              final isEng = context
                  .isEng; // Assuming context.isEng is a boolean indicating the language

              List<CollectionModel>? _collections = collectionsP.collections;
              if (custoFilter == null) {
                return const SizedBox();
              }
              return snapshot.connectionState == ConnectionState.waiting
                  ? const Center(
                      child: SizedBox(
                          width: 70, height: 70, child: LoadingWidget()),
                    )
                  : ListView(
                      controller: _scrollController,
                      children: [
                        Table(
                            defaultVerticalAlignment:
                                TableCellVerticalAlignment.middle,
                            columnWidths: const {
                              0: FlexColumnWidth(0.7),
                              1: FlexColumnWidth(0.7),
                              2: FlexColumnWidth(0.7),
                              3: FlexColumnWidth(0.7),
                            },
                            children: _collections == null
                                ? []
                                : _collections.reversed
                                    .map((state) => TableRow(children: [
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 20.0),
                                            child: Text(
                                                state.createdAt!
                                                    .toString()
                                                    .split(" ")
                                                    .first,
                                                style: Them.tableCell,
                                                overflow:
                                                    TextOverflow.ellipsis),
                                          ),
                                          Text(
                                            state.customerName!,
                                            style: Them.tableCell.copyWith(
                                                fontWeight: FontWeight.normal),
                                            textAlign: TextAlign.center,
                                          ),
                                          Text(
                                            state.value.toString(),
                                            style: Them.tableCell,
                                            textAlign: TextAlign.center,
                                          ),
                                          state.isCashe!
                                              ? Text(
                                                  isEng ? "Cash" : "نقداََ",
                                                  style: Them.tableCell,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  textAlign: TextAlign.center,
                                                )
                                              : InkWell(
                                                  onTap: () => showDialog(
                                                    context: context,
                                                    builder: (context) =>
                                                        AlertDialog(
                                                      content: Image.network(
                                                          state.checkImage!),
                                                    ),
                                                  ),
                                                  child: Image.network(
                                                    state.checkImage!,
                                                    width: 120,
                                                    height: 50,
                                                  ),
                                                ),
                                        ]))
                                    .toList())
                      ],
                    );
            }));
  }
}
