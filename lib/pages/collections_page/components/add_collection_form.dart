import 'dart:io';

// import 'package:firebase_storage/firebase_storage.dart' as firebase_storage;
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/core/shared/drop_down_fields/customers_drop_down.dart';
import 'package:mandob/models/collection_model.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/providers/collections_provider.dart';
import 'package:mandob/providers/customers_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:mandob/utils/submit_button.dart';
import 'package:mandob/utils/text_field.dart';
import 'package:persian_number_utility/persian_number_utility.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../utils/show_bar/flush_bar.dart';

class AddCollectionForm extends StatefulWidget {
  const AddCollectionForm({
    super.key,
  });

  @override
  _AddCollectionFormState createState() => _AddCollectionFormState();
}

class _AddCollectionFormState extends State<AddCollectionForm> {
  bool _isCashe = true;
  String? colValue;
  bool _loading = false;
  final GlobalKey<FormState> _formKey = GlobalKey();
  String? checkImage;

  late File? imageFile = File("");
  late String? fileName;

  Future pickImage() async {
    final _imagePicker = ImagePicker();
    var imagePicked = await _imagePicker.pickImage(
        source: ImageSource.camera, maxHeight: 500, maxWidth: 1200);

    if (imagePicked != null) {
      setState(() {
        imageFile = File(imagePicked.path);
        fileName = (imageFile!.path);
      });
    } else {
      print('No image selected!');
    }
  }

  // Future<String?> uploadFile(File? file) async {
  //   if (file == null) {
  //     debugPrint('no image selected !');
  //     return null;
  //   }
  //   firebase_storage.UploadTask uploadTask;
  //   firebase_storage.Reference ref = firebase_storage.FirebaseStorage.instance
  //       .ref()
  //       .child('checks')
  //       .child('/${imageFile!.path}');
  //   final metadata = firebase_storage.SettableMetadata(
  //       contentType: 'image/jpeg',
  //       customMetadata: {'picked-file-path': file.path});
  //   uploadTask = ref.putFile(File(file.path), metadata);
  //   checkImage = await (await uploadTask).ref.getDownloadURL();
  //   setState(() {});
  //   return checkImage;
  // }

  CustomerModel? selectedCusto;
  List<CustomerModel> mandobUsers = [];

  @override
  Widget build(BuildContext context) {
    final isEng = context
        .isEng; // Assuming context.isEng is a boolean indicating the language
    final remainingAmount = colValue == null ||
            selectedCusto == null ||
            selectedCusto?.debit == null
        ? 0
        : (selectedCusto?.debit ?? 0) -
            (num.tryParse(colValue?.toEnglishDigit() ?? '0.0') ?? 0.0);

    return Consumer<CollectionsProvider>(
      builder: (context, collectionsProvider, child) {
        void submit() async {
          context.back();

          setState(() {
            _loading = true;
          });
          // if (imageFile!.path.isNotEmpty) {
          //   await uploadFile(imageFile);
          // }

          await collectionsProvider.addCollection(CollectionModel.fromJson({
            "createdAt": DateTime.now().toIso8601String(),
            "customerName": selectedCusto!.customerName,
            "customerId": selectedCusto!.id,
            "value": double.parse(colValue!.toEnglishDigit()),
            "isCashe": _isCashe,
            "checkImage": checkImage,
            "type": 'collection'
          }));

          var userNewDebit = 0.0;
          var customerOldDebit = selectedCusto?.debit ?? 0.0;

          userNewDebit = customerOldDebit;

          userNewDebit -= double.parse(colValue!.toEnglishDigit());

          await Provider.of<CustomersProvider>(context, listen: false)
              .editDebit(selectedCusto!.id.toString(), userNewDebit);
          _loading = false;

          Navigator.pop(context);

          showBar(
              context,
              isEng
                  ? 'Collection added successfully'
                  : 'تم اضافة التحصيل بنجاح',
              backgroundColor: ColorManager.primaryColor,
              indicatorColor: ColorManager.secondaryColor,
              icon: Icons.done_all);
        }

        return Scaffold(
          bottomNavigationBar: Padding(
            padding: const EdgeInsets.all(20.0),
            child: SubmitButton(
              onPressed: () {
                if (!_formKey.currentState!.validate()) return;
                if (!_isCashe && imageFile!.path.isEmpty) return;
                if (selectedCusto == null) return;
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    contentPadding: const EdgeInsets.all(30),
                    content: Text(isEng
                        ? 'Please check the collection details before confirming'
                        : 'من فضلك تاكد من بيانات التحصيل قبل الموافقه'),
                    actionsPadding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 30),
                    actions: [
                      SizedBox(
                        width: 100,
                        child: SubmitButton(
                          onPressed: submit,
                          label: isEng ? 'Save' : 'حفظ',
                        ),
                      ),
                      SizedBox(
                        width: 100,
                        child: SubmitButton(
                          color: ColorManager.errorColor,
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          label: isEng ? 'Cancel' : 'الغاء',
                        ),
                      ),
                    ],
                  ),
                );
              },
              label: isEng ? 'Save Collection' : 'حفظ التحصيل',
            ),
          ),
          appBar: AppBar(
            leading: const BackButton(color: Colors.white),
            title: Text(isEng ? 'Add New Collection' : 'اضافة تحصيل جديد'),
            backgroundColor: ColorManager.primaryColor,
          ),
          body: _loading
              ? const Center(child: LoadingWidget())
              : Form(
                  key: _formKey,
                  child: ListView(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 30),
                    shrinkWrap: true,
                    children: [
                      CustomersDropDownButton(
                        customer: selectedCusto,
                        showBalance: true,
                        onChanged: (val) {
                          setState(() {
                            selectedCusto = val;
                          });
                        },
                      ),
                      AppGaps.gap16,
                      Text(isEng ? 'Collected Amount' : 'المبلغ المحصل',
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          )),
                      AppGaps.gap12,
                      TextFieldWidget(
                        initialValue: colValue,
                        textInputType: TextInputType.number,
                        label: isEng ? 'Amount' : 'المبلغ',
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return isEng ? 'Enter Amount' : 'ادخل المبلغ';
                          }
                          if (double.tryParse(colValue!.toEnglishDigit()) ==
                              null) {
                            return isEng
                                ? 'Enter a valid amount'
                                : 'ادخل المبلغ الصحيح';
                          }
                          if (double.tryParse(colValue!.toEnglishDigit())! >
                              selectedCusto!.debit!) {
                            return isEng
                                ? 'Cannot collect more than the debt'
                                : 'لا يمكن تحصيل قيمة أكبر من الدين';
                          }

                          return null;
                        },
                        onChanged: (value) => setState(() {
                          colValue = value;
                        }),
                      ),

                      AppGaps.gap16,

                      // المتبقي من العميل
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            isEng
                                ? 'Remaining from customer:'
                                : 'المتبقي من العميل:',
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            '${remainingAmount.toStringAsFixed(2)}${context.currency}',
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
        );
      },
    );
  }
}
