import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/operation_model.dart';
import 'package:mandob/pages/cashier_page/components/deposit_form.dart';
import 'package:mandob/pages/main_components/price_card.dart';
import 'package:mandob/providers/cashier_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:mandob/widgets/date_widget.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../utils/app_bar.dart';
import 'components/cashier_table.dart';

class CashierPage extends StatefulWidget {
  const CashierPage({super.key});

  @override
  _CashierPageState createState() => _CashierPageState();
}

String? sourceFilterTrans;

class _CashierPageState extends State<CashierPage> {
  String? _selectedUser;
  String? typeFilter;
  String? sourceFilter;
  String? sourceFilterTranslate;

  var sDate = ValueNotifier(DateTime.now().subtract(const Duration(days: 1)));
  var eDate = ValueNotifier(DateTime.now());

  @override
  Widget build(BuildContext context) {
    final isEng = context
        .isEng; // Assuming context.isEng is a boolean indicating the language

    if (sourceFilter == (isEng ? 'Collections' : 'تحصيلات')) {
      sourceFilterTranslate = 'collection';
    } else if (sourceFilter == (isEng ? 'Deposit' : 'إيداع')) {
      sourceFilterTranslate = 'Deposit';
    } else if (sourceFilter == (isEng ? 'Sales' : 'مبيعات')) {
      sourceFilterTranslate = 'sales';
    } else if (sourceFilter == (isEng ? 'Purchases' : 'مشتريات')) {
      sourceFilterTranslate = 'purchases';
    } else if (sourceFilter == (isEng ? 'Supplier Payments' : 'سداد موردين')) {
      sourceFilterTranslate = 'supplier-payments';
    } else if (sourceFilter == (isEng ? 'Expenses' : 'مصروفات')) {
      sourceFilterTranslate = 'expense';
    }

    final currentUserId =
        Provider.of<UserProvider>(context, listen: false).activeUser!;

    return Scaffold(
        body: Consumer<CashierProvider>(builder: (context, _cashier, child) {
      log('asfsafsasf ${typeFilter} SSSS ${sourceFilterTranslate}');
      return FutureBuilder(
          future: _cashier.fetchOps(
            startDate: sDate.value,
            endDate: eDate.value,
            userId: currentUserId.isAdmin == true
                ? _selectedUser
                : currentUserId.uid,
            isIncome: typeFilter == null
                ? null
                : typeFilter == (isEng ? "Deposit" : "ايداع"),
            source: sourceFilterTranslate,
          ),
          builder: (context, snapshot) {
            List<OperationModel>? _cashierOps = _cashier.operations ?? [];

            return Column(
              children: [
                appBarWidget(
                  context,
                  title: isEng ? 'Cashier Movements' : 'حركة الخزينه',
                  subtitle: isEng ? 'Deposit +' : 'ايداع +',
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const DepositForm(),
                    ),
                  ),
                  //     showDialog(
                  //   context: context,
                  //   builder: (context) => DepositForm(),
                  // ),
                ),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 18.0),
                        child: snapshot.connectionState ==
                                ConnectionState.waiting
                            ? const SizedBox(
                                width: 60, height: 30, child: LoadingWidget())
                            : PriceCard(
                                price: _cashier.totalCurrency,
                                title:
                                    isEng ? "Current Balance" : "الرصيد الحالى",
                              ),
                      ),
                      const SizedBox(width: 15),
                      Padding(
                        padding: const EdgeInsets.only(top: 15.0),
                        child: Consumer<UserProvider>(
                            builder: (context, _users, child) {
                          if (_users.activeUser!.isAdmin!) {
                            return DropdownButton<String>(
                                onChanged: (val) {
                                  setState(() {
                                    _selectedUser = val;
                                  });
                                },
                                icon: const Icon(
                                    Icons.keyboard_arrow_down_outlined),
                                value: _selectedUser,
                                items: [
                                  DropdownMenuItem(
                                    value: null,
                                    child: Text(isEng
                                        ? "All Representatives"
                                        : "كل المندوبين"),
                                  ),
                                  ..._users.users!
                                      .map((mandob) => DropdownMenuItem<String>(
                                          value: mandob.uid,
                                          child: Text(mandob.name!)))
                                      .toList()
                                ]);
                          }
                          return const Center();
                        }),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius:
                                BorderRadius.circular(AppRadius.radius8),
                            color: Colors.blueGrey.shade50,
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: DropdownButton<String>(
                              isExpanded: true,
                              underline: const SizedBox(),
                              borderRadius:
                                  BorderRadius.circular(AppRadius.radius8),
                              onChanged: (val) {
                                setState(() {
                                  typeFilter = val;
                                });
                              },
                              hint: FittedBox(
                                  fit: BoxFit.contain,
                                  child: Text(isEng
                                      ? "Operation Type"
                                      : "نوع العملية")),
                              icon: const Icon(
                                  Icons.keyboard_arrow_down_outlined),
                              value: typeFilter,
                              items: [
                                DropdownMenuItem(
                                  value: null,
                                  child: Text(
                                      isEng ? "All Operations" : "كل العمليات"),
                                ),
                                ...[
                                  isEng ? "Withdrawal" : "سحب",
                                  isEng ? "Deposit" : "ايداع"
                                ]
                                    .map((estore) => DropdownMenuItem<String>(
                                        value: estore, child: Text(estore)))
                                    .toList()
                              ]),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius:
                                BorderRadius.circular(AppRadius.radius8),
                            color: Colors.blueGrey.shade50,
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: DropdownButton<String>(
                              isExpanded: true,
                              underline: const SizedBox(),
                              borderRadius:
                                  BorderRadius.circular(AppRadius.radius8),
                              onChanged: (val) {
                                setState(() {
                                  sourceFilter = val;
                                });
                              },
                              hint: FittedBox(
                                  fit: BoxFit.contain,
                                  child: Text(isEng ? "Source" : "المصدر")),
                              icon: const Icon(
                                  Icons.keyboard_arrow_down_outlined),
                              value: sourceFilter,
                              items: [
                                DropdownMenuItem(
                                  value: null,
                                  child: Text(
                                      isEng ? "All Sources" : "كل المصادر"),
                                ),
                                ...[
                                  isEng ? "Collections" : "تحصيلات",
                                  isEng ? "Deposit" : "إيداع",
                                  isEng ? "Sales" : "مبيعات",
                                  isEng ? "Purchases" : "مشتريات",
                                  isEng ? "Supplier Payments" : "سداد موردين",
                                  isEng ? "Expenses" : "مصروفات",
                                ]
                                    .map((estore) => DropdownMenuItem<String>(
                                        value: estore, child: Text(estore)))
                                    .toList()
                              ]),
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 10.0, vertical: 8.0),
                  child: UiWidgets.datePicker(
                    context,
                    onSelectionChanged,
                    startDate: sDate,
                    endDate: eDate,
                  ),
                ),
                CashierTable(
                  cashierOps: _cashierOps,
                  snapshot: snapshot,
                ),
              ],
            );
          });
    }));
  }

  void onSelectionChanged() {
    setState(() {});
  }
}
