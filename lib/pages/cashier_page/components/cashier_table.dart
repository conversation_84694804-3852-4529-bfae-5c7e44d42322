import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/operation_model.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/loading_widget.dart';

import '../../../theme.dart';

class CashierTable extends StatelessWidget {
  final AsyncSnapshot<dynamic> snapshot;
  final List<OperationModel>? cashierOps;

  const CashierTable(
      {super.key, required this.snapshot, required this.cashierOps});

  @override
  Widget build(BuildContext context) {
    final isEng = context
        .isEng; // Assuming context.isEng is a boolean indicating the language

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Table(columnWidths: const {
                0: FlexColumnWidth(1),
                1: FlexColumnWidth(0.8),
                2: Flex<PERSON><PERSON>umnWidth(0.7),
                3: FlexColumnWidth(0.7),
              }, children: [
                TableRow(
                  children: [
                    Text(
                      isEng ? "Date" : "التاريخ",
                      style: Them.tableHeader,
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      isEng ? "Operation" : "العمليه",
                      style: Them.tableHeader,
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      isEng ? "Amount" : "المبلغ",
                      style: Them.tableHeader,
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      isEng ? "Source" : "المصدر",
                      style: Them.tableHeader,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ]),
            ),
            const Divider(
              color: ColorManager.primaryColor,
              thickness: 2,
              height: 0,
            ),
            Expanded(
                child: snapshot.connectionState == ConnectionState.waiting
                    ? const Center(child: LoadingWidget())
                    : SingleChildScrollView(
                        child: Table(
                            border:
                                TableBorder.all(color: Colors.grey.shade300),
                            defaultVerticalAlignment:
                                TableCellVerticalAlignment.middle,
                            columnWidths: const {
                              0: FlexColumnWidth(1),
                              1: FlexColumnWidth(0.8),
                              2: FlexColumnWidth(0.7),
                              3: FlexColumnWidth(0.7),
                            },
                            children: (cashierOps ?? []).reversed.map((op) {
                              String sourceFilterTrans;
                              if (op.source.toString() == 'collection') {
                                sourceFilterTrans =
                                    isEng ? 'Collections' : 'تحصيلات';
                              } else if (op.source.toString() == 'Deposit') {
                                sourceFilterTrans = isEng ? 'Deposit' : 'إيداع';
                              } else if (op.source.toString() == 'sales') {
                                sourceFilterTrans = isEng ? 'Sales' : 'مبيعات';
                              } else if (op.source.toString() == 'purchases') {
                                sourceFilterTrans =
                                    isEng ? 'Purchases' : 'مشتريات';
                              } else if (op.source.toString() ==
                                  'supplier-payments') {
                                sourceFilterTrans = isEng
                                    ? 'Supplier Payments'
                                    : 'سداد الموردين';
                              } else if (op.source.toString() == 'expense') {
                                sourceFilterTrans =
                                    isEng ? 'Expenses' : 'مصروفات';
                              } else {
                                sourceFilterTrans = '';
                              }

                              return TableRow(children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8.0, horizontal: 3.0),
                                  child: Text(
                                    DateFormat('dd/MM/yyy hh:mm')
                                        .format(op.opTime!),
                                    style: Them.tableCell
                                        .copyWith(fontWeight: FontWeight.bold),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                Text(
                                  op.isIncome!
                                      ? (isEng ? "Deposit" : "ايداع")
                                      : (isEng ? "Withdrawal" : "سحب"),
                                  style: Them.tableCell,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.center,
                                ),
                                Text(
                                  op.value!.toStringAsFixed(2) +
                                      context.currency,
                                  style: Them.tableCell,
                                  textAlign: TextAlign.center,
                                ),
                                Text(
                                  sourceFilterTrans,
                                  style: Them.tableCell,
                                  textAlign: TextAlign.center,
                                ),
                              ]);
                            }).toList()),
                      ))
          ],
        ),
      ),
    );
  }
}
