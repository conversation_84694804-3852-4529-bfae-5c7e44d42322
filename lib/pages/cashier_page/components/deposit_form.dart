import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/operation_model.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/providers/cashier_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:mandob/utils/submit_button.dart';
import 'package:mandob/utils/text_field.dart';
import 'package:persian_number_utility/persian_number_utility.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

class DepositForm extends StatefulWidget {
  const DepositForm({super.key});

  @override
  _DepositFormState createState() => _DepositFormState();
}

class _DepositFormState extends State<DepositForm> {
  String? dipositValue;
  final GlobalKey<FormState> _formKey = GlobalKey();
  UserModel? _user;

  @override
  void initState() {
    _user = Provider.of<UserProvider>(context, listen: false).activeUser;

    super.initState();
  }

  bool isLoading = false;

  Future _submit() async {
    setState(() {
      isLoading = true;
    });
    context.back();
    await Provider.of<CashierProvider>(context, listen: false)
        .createOperation(OperationModel.fromJson({
      "isIncome": true,
      "value": double.parse(dipositValue!.toEnglishDigit()),
      "source": "Deposit",
      "userId": _user!.uid,
      "opTime": DateTime.now().toIso8601String(),
    }))
        .catchError((error) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          content: Text(error.toString()),
        ),
      );
    });
    setState(() {
      isLoading = true;
    });
    context.back();
    // await Provider.of<CashierProvider>(context, listen: false).fetchOps();
    // Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) {
    // return const CashierPage();
    // }));
  }

  @override
  Widget build(BuildContext context) {
    final isEng = context
        .isEng; // Assuming context.isEng is a boolean indicating the language

    return Scaffold(
      appBar: AppBar(
        leading: const BackButton(color: Colors.white),
        title: Text(isEng ? 'Add Deposit' : 'اضافه ايداع'),
        backgroundColor: ColorManager.primaryColor,
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(20.0),
        child: isLoading
            ? const LoadingWidget()
            : SubmitButton(
                onPressed: () {
                  if (!_formKey.currentState!.validate()) return;
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      contentPadding: const EdgeInsets.all(30),
                      content: Text(
                          '${isEng ? 'Deposit Amount: $dipositValue' : 'ايداع مبلغ : $dipositValue'}${context.currency}'),
                      actionsPadding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 30),
                      actions: [
                        SizedBox(
                          width: 100,
                          child: SubmitButton(
                            onPressed: _submit,
                            label: isEng ? 'Save' : 'حفظ',
                          ),
                        ),
                        const Spacer(),
                        SizedBox(
                          width: 100,
                          child: SubmitButton(
                            color: ColorManager.errorColor,
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            label: isEng ? 'Cancel' : 'الغاء',
                          ),
                        ),
                      ],
                    ),
                  );
                  // showDialog(
                  //   context: context,
                  //   builder: (context) => AlertDialog(
                  //     content: Text((isEng ? 'Deposit Amount: ' : 'ايداع مبلغ : ') +
                  //         dipositValue!),
                  //     actions: [
                  //       TextButton(
                  //           onPressed: () {
                  //             _submit();
                  //           },
                  //           child: Text(isEng ? "Done" : "تم")),
                  //       TextButton(
                  //           onPressed: () => Navigator.pop(context),
                  //           child: Text(isEng ? "Back" : "عوده"))
                  //     ],
                  //   ),
                  // );
                },
                label: isEng ? 'Save Deposit' : 'حفظ الايداع',
              ),
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          child: ListView(
            shrinkWrap: true,
            children: [
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceAround,
              //   children: [
              //     const SizedBox(width: 3),
              //     Text(isEng ? "Enter Amount" : "ادخل المبلغ"),
              //     Flexible(
              //       flex: 2,
              //       child: Padding(
              //         padding: const EdgeInsets.symmetric(
              //             horizontal: 8.0, vertical: 18.0),
              //         child: SizedBox(
              //           height: 60,
              //           child: TextFormField(
              //             initialValue: dipositValue,
              //             textAlign: TextAlign.center,
              //             validator: (value) {
              //               if (value == null || value.isEmpty) {
              //                 return isEng ? "Enter Amount" : "ادخل المبلغ";
              //               }
              //               if (double.tryParse(
              //                       dipositValue!.toEnglishDigit()) ==
              //                   null) {
              //                 return isEng
              //                     ? 'Enter a valid amount'
              //                     : 'ادخل المبلغ الصحيح';
              //               }
              //             },
              //             onChanged: (value) => dipositValue = value,
              //             decoration: InputDecoration(
              //                 fillColor: Colors.grey.shade300,
              //                 hintText: "00.0",
              //                 filled: true),
              //           ),
              //         ),
              //       ),
              //     )
              //   ],
              // ),
              AppGaps.gap16,
              Text(isEng ? 'Deposit Amount' : 'المبلغ المودع',
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  )),
              AppGaps.gap12,

              TextFieldWidget(
                initialValue: dipositValue,
                onChanged: (value) => setState(() {
                  dipositValue = value;
                }),
                textInputType: TextInputType.number,
                label: isEng ? 'Amount' : 'المبلغ',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return isEng ? "Enter Amount" : "ادخل المبلغ";
                  }
                  if (double.tryParse(dipositValue!.toEnglishDigit()) == null) {
                    return isEng
                        ? 'Enter a valid amount'
                        : 'ادخل المبلغ الصحيح';
                  }
                  return null;
                },
              ),

              // AppGaps.gap12,

              // ElevatedButton(
              //     onPressed: () {
              //       if (!_formKey.currentState!.validate()) return;
              //       showDialog(
              //         context: context,
              //         builder: (context) => AlertDialog(
              //           content: Text(
              //               (isEng ? 'Deposit Amount: ' : 'ايداع مبلغ : ') +
              //                   dipositValue!),
              //           actions: [
              //             TextButton(
              //                 onPressed: () {
              //                   _submit();
              //                 },
              //                 child: Text(isEng ? "Done" : "تم")),
              //             TextButton(
              //                 onPressed: () => Navigator.pop(context),
              //                 child: Text(isEng ? "Back" : "عوده"))
              //           ],
              //         ),
              //       );
              //     },
              //     child: Text(isEng ? "Deposit" : "ايداع"))
            ],
          ),
        ),
      ),
    );
  }
}
