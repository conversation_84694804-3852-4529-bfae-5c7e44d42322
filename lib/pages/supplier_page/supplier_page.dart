import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/pages/supplier_page/components/delete_supplier_dialog.dart';
import 'package:mandob/providers/suppliers_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:provider/provider.dart';

import '../../utils/app_bar.dart';
import 'components/add_supplier_dialog.dart';

class SupplierPage extends StatefulWidget {
  const SupplierPage({Key? key}) : super(key: key);

  @override
  State<SupplierPage> createState() => _SupplierPageState();
}

class _SupplierPageState extends State<SupplierPage> {
  final _searchController = TextEditingController();
  String searchValue = '';

  @override
  Widget build(BuildContext context) {
    return Consumer<SuppliersProvider>(builder: (context, supplierData, child) {
      List suppliers = supplierData.suppliers;

      if (searchValue.isNotEmpty) {
        suppliers = suppliers
            .where((supplier) => supplier.name.contains(searchValue))
            .toList();
      }

      final searchSuppliersField = Container(
        height: 55,
        margin: const EdgeInsets.only(top: 24, left: 24, right: 24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Colors.grey,
              blurRadius: 2,
              offset: Offset(0, 2),
            )
          ],
        ),
        child: TextField(
          controller: _searchController,
          onChanged: (value) {
            setState(() {
              searchValue = value;
            });
          },
          textInputAction: TextInputAction.search,
          decoration: InputDecoration(
            hintText: context.isEng ? 'Search for Supplier' : 'بحث عن مورد',
            hintStyle: const TextStyle(color: Colors.grey),
            prefixIcon: const Icon(CupertinoIcons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.grey),
            ),
          ),
        ),
      );

      return Scaffold(
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () =>
              showDialog(context: context, builder: (_) => AddSupplierDialog()),
          label: Text(context.isEng ? 'Add Supplier' : 'إضافة مورد'),
          icon: const Icon(Icons.add),
        ),
        body: Stack(
          children: [
            appBarWidget(
              context,
              title: context.isEng ? 'Suppliers' : 'الموردين',
            ),
            FutureBuilder(
                future: supplierData.fetchSuppliers(),
                builder: (context, snapshot) {
                  if (supplierData.isLoading) return const LoadingWidget();

                  if (supplierData.suppliers.isEmpty) {
                    return Center(
                        child: Text(context.isEng
                            ? 'No suppliers found'
                            : 'لا يوجد موردين'));
                  }
                  return Padding(
                    padding: const EdgeInsets.only(top: 70),
                    child: Column(
                      children: [
                        searchSuppliersField,
                        Expanded(
                          child: ListView.separated(
                            shrinkWrap: true,
                            padding: const EdgeInsets.all(12),
                            separatorBuilder: (context, index) {
                              return const Padding(
                                  padding: EdgeInsets.only(left: 80, right: 80),
                                  child: Divider());
                            },
                            itemCount: suppliers.length,
                            itemBuilder: (context, index) {
                              var supplier = suppliers[index];

                              return Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  color: Colors.white,
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Colors.grey,
                                      blurRadius: 5,
                                      offset: Offset(0, 3),
                                    ),
                                  ],
                                ),
                                margin: const EdgeInsets.only(
                                  top: 8,
                                  bottom: 8,
                                ),
                                child: Stack(
                                  alignment: context.isEng
                                      ? Alignment.topRight
                                      : Alignment.topLeft,
                                  children: [
                                    ListTile(
                                      isThreeLine: true,
                                      minVerticalPadding: 5,
                                      title:
                                          Text(supplier.name?.toString() ?? ''),
                                      subtitle: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                              supplier.streetName?.toString() ??
                                                  ''),
                                          Text(
                                              supplier.phone?.toString() ?? ''),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 5),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          GestureDetector(
                                            onTap: () => showDialog(
                                                context: context,
                                                builder: (_) =>
                                                    AddSupplierDialog(
                                                        supplier: supplier)),
                                            child: const CircleAvatar(
                                              backgroundColor:
                                                  ColorManager.primaryColor,
                                              maxRadius: 15,
                                              child: Icon(
                                                Icons.edit,
                                                size: 20,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(
                                            height: 10,
                                          ),
                                          GestureDetector(
                                            onTap: () => showDialog(
                                                context: context,
                                                builder: (_) =>
                                                    DeleteSupplierDialog(
                                                        supplierId:
                                                            supplier.id)),
                                            child: const CircleAvatar(
                                              backgroundColor: Colors.red,
                                              maxRadius: 15,
                                              child: Icon(
                                                Icons.delete,
                                                size: 20,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  );
                })
          ],
        ),
      );
    });
  }
}
