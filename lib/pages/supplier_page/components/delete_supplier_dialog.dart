import 'package:flutter/material.dart';
import 'package:mandob/providers/suppliers_provider.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:provider/provider.dart';
import 'package:mandob/core/extensions/context_extensions.dart';

import '../../../utils/loading_widget.dart';

class DeleteSupplierDialog extends StatefulWidget {
  final String? supplierId;

  const DeleteSupplierDialog({
    super.key,
    required this.supplierId,
  });

  @override
  State<StatefulWidget> createState() => _DeleteProductDialog();
}

class _DeleteProductDialog extends State<DeleteSupplierDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  @override
  void initState() {
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  bool loading = false;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        Center(
          child: Material(
            color: Colors.transparent,
            child: ScaleTransition(
              scale: scaleAnimation,
              child: Container(
                  width: 250,
                  padding: const EdgeInsets.all(15),
                  decoration: ShapeDecoration(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15.0))),
                  child: SingleChildScrollView(
                    child: Wrap(
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.only(right: 4.0, left: 4),
                          child: Center(
                            child: Text(
                              context.isEng ? 'Do you want to delete this supplier?' : 'هل تريد حذف هذا المورد؟',
                              style: const TextStyle(
                                  color: Colors.black87,
                                  fontSize: 18,
                                  height: 1.5),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                              top: 25, bottom: 10, right: 10),
                          child: Row(
                            textDirection: TextDirection.rtl,
                            children: <Widget>[
                              const SizedBox(width: 10),
                              GestureDetector(
                                onTap: () => Navigator.pop(context),
                                child: Container(
                                  height: 35,
                                  width: 80,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(7),
                                      color: Colors.grey),
                                  child: Text(
                                    context.isEng ? 'Cancel' : 'إلغاء',
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 15),
                              GestureDetector(
                                onTap: () {
                                  setState(() {
                                    loading = true;
                                  });

                                  Provider.of<SuppliersProvider>(context,
                                      listen: false)
                                      .deleteSupplier(
                                      widget.supplierId.toString());

                                  setState(() {
                                    loading = false;
                                  });

                                  Navigator.pop(context);

                                  showBar(context, context.isEng ? 'Supplier deleted!' : 'تم حذف المورد!');
                                },
                                child: Container(
                                  height: 35,
                                  width: 80,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(7),
                                      color: Colors.red),
                                  child: Text(
                                    context.isEng ? 'Delete' : 'حذف',
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  )),
            ),
          ),
        ),
        loading
            ? const Center(
          child: LoadingWidget(),
        )
            : Container()
      ],
    );
  }
}