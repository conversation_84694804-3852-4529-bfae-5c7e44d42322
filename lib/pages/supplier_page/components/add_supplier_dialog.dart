import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/supplier_model.dart';
import 'package:mandob/providers/suppliers_provider.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:mandob/utils/submit_button.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../utils/color_manager.dart';
import 'add_supplier_form.dart';

var nameCtrl = TextEditingController();
var addressCtrl = TextEditingController();
var phoneCtrl = TextEditingController();

var formKey = GlobalKey<FormState>();

var nameFocus = FocusNode();
var addressFocus = FocusNode();
var phoneFocus = FocusNode();

class AddSupplierDialog extends StatefulWidget {
  final supplier;

  AddSupplierDialog({this.supplier});

  @override
  State<StatefulWidget> createState() => _AddDeliveryDialog();
}

class _AddDeliveryDialog extends State<AddSupplierDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  late SupplierModel supplier;

  @override
  void initState() {
    if (widget.supplier != null) {
      supplier = widget.supplier;

      nameCtrl.text = supplier.name.toString();
      phoneCtrl.text = supplier.phone.toString();
      addressCtrl.text = supplier.address.toString() == 'null'
          ? ''
          : supplier.address.toString();
    }
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  bool loading = false;

  @override
  Widget build(BuildContext context) {
    final isKeyboardOpened = MediaQuery.of(context).viewInsets.bottom != 0;

    return WillPopScope(
      onWillPop: () async {
        if (MediaQuery.of(context).viewInsets.bottom != 0) {
          nameFocus.unfocus();
          phoneFocus.unfocus();
          addressFocus.unfocus();
        } else
          Navigator.pop(context);
        return true;
      },
      child: Consumer<SuppliersProvider>(
        builder: (context, _suppliers, child) {
          return Scaffold(
            bottomNavigationBar: Padding(
              padding: EdgeInsets.only(
                  bottom: isKeyboardOpened ? 70 : 20.0, right: 20, left: 20),
              child: SubmitButton(
                color: ColorManager.brown,
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    formKey.currentState!.save();
                    setState(() {
                      loading = true;
                    });
                    if (widget.supplier == null) {
                      await _suppliers
                          .addSupplier(SupplierModel.fromJson({
                        'name': nameCtrl.text,
                        'phone': phoneCtrl.text,
                        'address': addressCtrl.text
                      }))
                          .then((value) async {
                        addressCtrl.clear();
                        nameCtrl.clear();
                        phoneCtrl.clear();

                        await _suppliers.fetchSuppliers(forceLoad: true);

                        setState(() {
                          loading = false;
                        });

                        context.back();

                        showBar(
                            context,
                            context.isEng
                                ? 'Added successfully'
                                : 'تمت الإضافة بنجاح',
                            backgroundColor: ColorManager.primaryColor,
                            indicatorColor: ColorManager.secondaryColor,
                            icon: Icons.done_all);
                      });
                    } else {
                      await Provider.of<SuppliersProvider>(context,
                              listen: false)
                          .editSupplier(
                              widget.supplier.id.toString(),
                              SupplierModel.fromJson({
                                'name': nameCtrl.text,
                                'phone': phoneCtrl.text,
                                'address': addressCtrl.text
                              }))
                          .then((value) {
                        setState(() {
                          loading = false;
                        });
                        Navigator.pop(context);
                        showBar(
                            context,
                            context.isEng
                                ? 'Edited successfully'
                                : 'تم التعديل بنجاح',
                            backgroundColor: ColorManager.primaryColor,
                            indicatorColor: ColorManager.secondaryColor,
                            icon: Icons.done_all);
                      });
                    }
                    await Provider.of<SuppliersProvider>(context, listen: false)
                        .fetchSuppliers();
                  }
                },
              ),
            ),
            appBar: AppBar(
              leading: const BackButton(
                color: Colors.white,
              ),
              backgroundColor: ColorManager.brown,
              title: Text(
                widget.supplier == null
                    ? context.isEng
                        ? 'Add Supplier'
                        : 'إضافة مورد'
                    : context.isEng
                        ? 'Edit Supplier'
                        : 'تعديل مورد',
                style: const TextStyle(
                  color: Colors.white,
                ),
              ),
            ),
            body: Form(
              key: formKey,
              child: Stack(
                children: <Widget>[
                  Container(
                      padding: const EdgeInsets.all(20),
                      decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15.0))),
                      child: const Column(
                        children: [
                          AddSupplierForm(),
                        ],
                      )),
                  loading
                      ? const Center(
                          child: LoadingWidget(),
                        )
                      : Container()
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
