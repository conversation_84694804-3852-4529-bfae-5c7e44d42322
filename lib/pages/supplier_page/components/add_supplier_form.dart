import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/text_field.dart';
import 'package:xr_helper/xr_helper.dart';

import 'add_supplier_dialog.dart';

class AddSupplierForm extends StatelessWidget {
  const AddSupplierForm({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TextFieldWidget(
          focusNode: nameFocus,
          controller: nameCtrl,
          labelColor: ColorManager.brown,
          label: context.isEng ? 'Supplier Name' : 'اسم المورد',
        ),
        AppGaps.gap16,
        TextFieldWidget(
          focusNode: addressFocus,
          controller: addressCtrl,
          label: context.isEng ? 'Address' : 'العنوان',
          labelColor: ColorManager.brown,
        ),
        AppGaps.gap16,
        TextFieldWidget(
          focusNode: phoneFocus,
          controller: phoneCtrl,
          label: context.isEng ? 'Phone Number' : 'رقم الهاتف',
          textInputType: TextInputType.number,
          labelColor: ColorManager.brown,
        ),
      ],
    );
  }
}
