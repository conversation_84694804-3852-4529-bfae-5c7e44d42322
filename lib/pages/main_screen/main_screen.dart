import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/company_info_model.dart';
import 'package:mandob/pages/drawer_menu.dart';
import 'package:mandob/providers/company_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:provider/provider.dart';

import 'home_screen.dart';

class MainScreen extends StatefulWidget {
  static const nv = "home-page";

  const MainScreen({super.key});

  @override
  _MainScreenState createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  @override
  Widget build(BuildContext context) {
    final activeUser = context.read<UserProvider>().activeUser;
    final isEng = context.isEng;

    return SafeArea(
      child: FutureBuilder<CompanyInfoModel?>(
          future: context.read<CompanyInfoProvider>().fetchCompanyInfo(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting &&
                !kDebugMode) {
              return const Material(
                child: Center(
                  child: LoadingWidget(),
                ),
              );
            }

            final isCompanyActive =
                snapshot.data != null && (snapshot.data?.isActive ?? true);

            if (isCompanyActive && activeUser?.isActive == true) {
              return const Scaffold(
                  drawer: DrawerMenu(),
                  resizeToAvoidBottomInset: false,
                  body: HomeScreen());
            } else {
              return Scaffold(
                body: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircleAvatar(
                            backgroundColor: Colors.blueGrey.shade200,
                            radius: 65,
                            child: const Icon(
                              Icons.policy,
                              size: 85,
                              color: Colors.white,
                            )),
                        const SizedBox(
                          height: 50,
                        ),
                        Text(
                          activeUser?.isActive == false
                              ? isEng
                                  ? 'Your account is not active, please contact technical support!'
                                  : 'حسابك غير مفعل،، برجاء التواصل مع الدعم الفني !'
                              : isEng
                                  ? 'The company is not active, please contact technical support!'
                                  : 'الشركة غير مفعلة،، برجاء التواصل مع الدعم الفني !',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }
          }),
    );
  }
}
