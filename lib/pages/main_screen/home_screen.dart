import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/core/shared/drop_down_fields/delivery_drop_down.dart';
import 'package:mandob/pages/customer_page/customer_page.dart';
import 'package:mandob/pages/dashboard/dashboard_page.dart';
import 'package:mandob/pages/drawer_menu.dart';
import 'package:mandob/pages/main_screen/widgets/bottom_navigation_bar.dart';
import 'package:mandob/pages/products_page/products_page.dart';
import 'package:mandob/pages/receipt_page/components/invoice_table/purchases_table/purchases_navigation_tabs.dart';
import 'package:mandob/pages/receipt_page/components/invoice_table/sales_table/sales_navigation_tabs.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:provider/provider.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../providers/user_provider.dart';
import '../login_page/login_page.dart';

class HomeScreen extends HookWidget {
  const HomeScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final isAdmin =
        Provider.of<UserProvider>(context, listen: false).activeUser?.isAdmin ==
            true;

    final selectedIndex = useState<int>(4);

    Widget selectedPage() {
      // if (isAdmin) {
      //   switch (selectedIndex.value) {
      //     case 0:
      //       return const DeliveryPage();
      //     case 1:
      //       return const RepositoriesPage();
      //     case 2:
      //       return const ProductsPage();
      //     case 3:
      //       return const MainSalesTable();
      //     case 4:
      //       return const MainPurchasesTable();
      //   }
      // } else {
      switch (selectedIndex.value) {
        case 0:
          return const ProductsPage();
        case 1:
          return const CustomerPage(
            withoutHeader: true,
          );
        case 2:
          return const MainSalesTable();
        case 3:
          return const MainPurchasesTable();
        case 4:
          return const DashboardPage();
      }
      // }
      return Container();
    }

    Widget titleWidget() {
      // if (isAdmin) {
      //   switch (selectedIndex.value) {
      //     case 0:
      //       return Text(context.isEng ? 'Delivery' : 'المندوبين',
      //           style: const TextStyle(color: Colors.black87));
      //     case 1:
      //       return Text(context.isEng ? 'Repositories' : 'المخازن',
      //           style: const TextStyle(color: Colors.black87));
      //     case 2:
      //       return Text(context.isEng ? 'Products' : 'المنتجات',
      //           style: const TextStyle(color: Colors.black87));
      //     case 3:
      //       return Text(context.isEng ? 'Sales' : 'المبيعات',
      //           style: const TextStyle(color: Colors.black87));
      //     case 4:
      //       return Text(context.isEng ? 'Purchases' : 'المشتريات',
      //           style: const TextStyle(color: Colors.black87));
      //   }
      // } else {
      switch (selectedIndex.value) {
        case 0:
          return Text(
              isAdmin
                  ? (context.isEng ? 'Products' : "المنتجات")
                  : (context.isEng ? 'My Store' : 'مخزني'),
              style: const TextStyle(color: Colors.black87));
        case 1:
          return Text(context.isEng ? 'Customers' : 'العملاء',
              style: const TextStyle(color: Colors.black87));
        case 2:
          return Text(context.isEng ? 'Sales' : 'المبيعات',
              style: const TextStyle(color: Colors.black87));
        case 3:
          return Text(context.isEng ? 'Purchases' : 'المشتريات',
              style: const TextStyle(color: Colors.black87));
        case 4:
          return Text(context.isEng ? 'Dashboard' : 'الرئيسية',
              style: const TextStyle(color: Colors.black87));
        // }
      }
      return Container();
    }

    final isHomeSelected = selectedIndex.value == 4;

    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.transparent,
        elevation: 0.0,
        centerTitle: true,
        actions: [
          if (isAdmin)
            // logout
            IconButton(
              onPressed: () => _logout(context),
              icon: const Icon(
                Icons.logout,
                color: Colors.white,
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorManager.errorColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
        ],
        leadingWidth: 40,
        title: titleWidget(),
      ),
      drawer:
          isAdmin && currentDelivery.value == null ? null : const DrawerMenu(),
      body: selectedPage(),
      floatingActionButton: FloatingActionButton(
        shape: const CircleBorder(),
        onPressed: () {
          selectedIndex.value = 4;
        },
        backgroundColor: isHomeSelected
            ? ColorManager.secondaryColor
            : ColorManager.primaryColor,
        child: const Icon(
          Icons.home,
          color: Colors.white,
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: BottomNavBarWidget(
        selectedIndex: selectedIndex,
      ),
    );
  }

  void _logout(BuildContext context) {
    QuickAlert.show(
      context: context,
      confirmBtnColor: ColorManager.errorColor,
      widget: Text(context.isEng
              ? "Are you sure you want to logout?"
              : "هل انت متأكد أنك تريد تسجيل الخروج؟")
          .paddingOnly(top: 15),
      type: QuickAlertType.error,
      showCancelBtn: true,
      title: context.isEng ? "Logout" : "تسجيل الخروج",
      titleAlignment: TextAlign.right,
      cancelBtnText: context.isEng ? "Cancel" : "إلغاء",
      confirmBtnText: context.isEng ? "Confirm" : "تأكيد",
      onConfirmBtnTap: () async {
        Provider.of<UserProvider>(context, listen: false).logOut();
        navService.back();
        const LoginPage(
          isLicenseView: false,
        ).navigateReplacement;
      },
    );
  }
}
