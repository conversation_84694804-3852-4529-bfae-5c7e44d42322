import 'package:animated_bottom_navigation_bar/animated_bottom_navigation_bar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/core/shared/drop_down_fields/delivery_drop_down.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:provider/provider.dart';

class BottomNavBarWidget extends HookWidget {
  final ValueNotifier<int> selectedIndex;

  const BottomNavBarWidget({super.key, required this.selectedIndex});

  @override
  Widget build(BuildContext context) {
    // final isAdmin = context.read<UserProvider>().activeUser?.isAdmin == true;

    return AnimatedBottomNavigationBar(
        icons:
            // isAdmin
            //     ? const [
            //         Icons.directions_car,
            //         Icons.store,
            //         Icons.bubble_chart,
            //         Icons.account_balance_wallet,
            //         CupertinoIcons.money_dollar_circle,
            //       ]
            //     :
            const [
          Icons.bubble_chart,
          CupertinoIcons.person_2,
          CupertinoIcons.money_dollar_circle,
          CupertinoIcons.cart_badge_plus,
        ],
        activeIndex: selectedIndex.value,
        gapLocation: GapLocation.center,
        notchSmoothness: NotchSmoothness.verySmoothEdge,
        activeColor: ColorManager.secondaryColor,
        inactiveColor: Colors.black,
        leftCornerRadius: 32,
        rightCornerRadius: 32,
        onTap: (index) {
          if (context.read<UserProvider>().activeUser?.isAdmin == true) {
            if (index == 0 || index == 4) {
              selectedIndex.value = index;
              return;
            }

            if (currentDelivery.value == null) {
              showBar(
                context,
                context.isEng
                    ? 'Please select a representatives first !'
                    : 'برجاء اختيار مندوب أولاً !',
              );
              return;
            }

            selectedIndex.value = index;
            return;
          }
          selectedIndex.value = index;
        });
  }
}
