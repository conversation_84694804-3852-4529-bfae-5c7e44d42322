import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mandob/appwrite_db/appwrite.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/company_info_model.dart';
import 'package:mandob/providers/company_provider.dart';
import 'package:provider/provider.dart';

Future<Uint8List> getLogo(String imageId) async {
  final unitFile = await AppwriteDB.getFile(fileId: imageId);
  return unitFile;
}

class CompanyInfoPage extends StatefulWidget {
  CompanyInfoPage({Key? key}) : super(key: key);

  @override
  _CompanyInfoPageState createState() => _CompanyInfoPageState();
}

class _CompanyInfoPageState extends State<CompanyInfoPage> {
  File? imageFile;
  late String? fileName;

  Future pickImage() async {
    final _imagePicker = ImagePicker();
    var imagePicked = await _imagePicker.pickImage(
        source: ImageSource.gallery, maxHeight: 200, maxWidth: 200);

    if (imagePicked != null) {
      imageId = null;
      setState(() {
        imageFile = File(imagePicked.path);
        fileName = (imageFile!.path);
      });
    } else {
      print('No image selected!');
    }
  }

  Future<String?> uploadFile(File? file) async {
    if (file == null) {
      debugPrint('no image selected !');
      return null;
    }
    final resData = await AppwriteDB.uploadFile(file: file);
    final String fileId = resData.$id;

    setState(() {
      imageId = fileId;
    });
    return fileId;
  }

  final GlobalKey<FormState> _formKey = GlobalKey();

  void _submit() async {
    if (!_formKey.currentState!.validate()) return;
    if (imageFile != null && imageFile!.path.isNotEmpty) {
      await uploadFile(imageFile);
    }
    final newInfo = CompanyInfoModel.fromJson({
      "companyName": companyName,
      "taxNumber": vatNumber,
      "commercialRegisterNumber": commercialRegisterNumber,
      "tax": double.parse(tax!),
      "logoUrl": imageId,
      "address": address,
      "bankName": bankName,
      "bankNumber": bankNumber,
      "taxPercent": 1.15,
      'isActive': isActive
    });

    await Provider.of<CompanyInfoProvider>(context, listen: false)
        .addCompanyInfo(newInfo)
        .then((value) => ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text(context.isEng
                ? "Data updated successfully"
                : "تم تعديل البيانات"))))
        .catchError((error) => throw error);

    Navigator.pop(context);
  }

  String? companyName;
  String? vatNumber;
  String? commercialRegisterNumber;
  String? tax;
  String? imageId;
  String? address;

  String? bankName;
  String? bankNumber;
  bool? isActive;

  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;
    CompanyInfoModel? compInfo =
        Provider.of<CompanyInfoProvider>(context, listen: false).companyInfo;
    companyName = compInfo!.companyName;
    vatNumber = compInfo.vatNumber;
    commercialRegisterNumber = compInfo.commercialRegisterNumber;
    tax = compInfo.tax?.toStringAsFixed(0);
    imageId = compInfo.logoUrl ?? '';
    address = compInfo.address;
    bankName = compInfo.bankName;
    bankNumber = compInfo.bankNumber;
    isActive = compInfo.isActive ?? true;

    return Scaffold(
      appBar: AppBar(
        actions: [
          IconButton(
            icon: const Icon(Icons.done_all),
            onPressed: () => _submit(),
          )
        ],
      ),
      body: Column(
        children: [
          Stack(
            children: [
              FutureBuilder(
                  future: getLogo(imageId!),
                  builder: (context, snapshot) {
                    if (imageFile != null) {
                      return Image.file(imageFile!);
                    }
                    if (snapshot.hasData && imageId != null && imageId != '') {
                      final Uint8List image = snapshot.data as Uint8List;
                      return Image.memory(
                        image,
                        fit: BoxFit.cover,
                      );
                    } else {
                      return SizedBox(
                        height: 200,
                        child: InkWell(
                          onTap: () => pickImage(),
                          child: const Icon(
                            Icons.camera,
                            size: 30,
                          ),
                        ),
                      );
                    }
                  }),
              if (imageFile != null)
                Positioned(
                    bottom: 15,
                    right: 15,
                    child: InkWell(
                      onTap: () => pickImage(),
                      child: const Icon(
                        Icons.camera,
                        size: 30,
                      ),
                    ))
            ],
          ),
          Expanded(
            child: Form(
              key: _formKey,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: ListView(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextFormField(
                          initialValue: companyName,
                          decoration: InputDecoration(
                              hintText: isEng ? "Company Name" : "اسم الشركه",
                              label:
                                  Text(isEng ? "Company Name" : "اسم الشركه")),
                          validator: (value) {
                            return value!.isEmpty
                                ? (isEng
                                    ? "Enter company name"
                                    : "ادخل اسم الشركة")
                                : null;
                          },
                          onChanged: (value) => companyName = value),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextFormField(
                          initialValue: address,
                          decoration: InputDecoration(
                              hintText:
                                  isEng ? "Company Address" : "عنوان الشركة",
                              label: Text(isEng ? "Address" : "العنوان")),
                          onChanged: (value) => address = value),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextFormField(
                          keyboardType: TextInputType.number,
                          initialValue: vatNumber,
                          decoration: InputDecoration(
                              hintText: isEng
                                  ? "xxxxx-xxxx-xxxxx"
                                  : "xxxxx-xxxx-xxxxx",
                              label: Text(
                                  isEng ? "Tax Number" : "الرقم الضريبى ")),
                          validator: (value) {
                            if (value!.isEmpty) {
                              return isEng
                                  ? "Enter tax number"
                                  : "ادخل الرقم الضريبى ";
                            }
                            if (value.length < 15) {
                              return isEng
                                  ? "Cannot be less than 15 digits"
                                  : "لا يمكن ان يقل عن 15 رقم";
                            }
                          },
                          onChanged: (value) => vatNumber = value),
                    ),
                    // commercialRegisterNumber
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextFormField(
                          keyboardType: TextInputType.number,
                          initialValue: commercialRegisterNumber,
                          decoration: InputDecoration(
                              hintText: isEng
                                  ? "xxxxx-xxxx-xxxxx"
                                  : "xxxxx-xxxx-xxxxx",
                              label: Text(isEng
                                  ? "Commercial Register Number"
                                  : "رقم السجل التجاري")),
                          onChanged: (value) =>
                              commercialRegisterNumber = value),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextFormField(
                          keyboardType: TextInputType.number,
                          initialValue: tax,
                          decoration: InputDecoration(
                            prefix: const Text(
                              "%",
                              style: TextStyle(fontSize: 15),
                            ),
                            hintText: isEng ? "Tax Value" : "قيمة الضريبه",
                            label: Text(
                                isEng ? "Tax Value" : "قيمة الضريبه المضافه"),
                          ),
                          validator: (value) {
                            return value!.isEmpty
                                ? (isEng
                                    ? "Enter tax value"
                                    : "ادخل قيمة الضريبه")
                                : null;
                          },
                          onChanged: (value) => tax = value),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextFormField(
                        keyboardType: TextInputType.number,
                        initialValue: bankName,
                        decoration: InputDecoration(
                          hintText: isEng ? "Bank Name" : "اسم البنك",
                          label: Text(isEng ? "Bank Name" : "اسم البنك"),
                        ),
                        onChanged: (value) => bankName = value,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextFormField(
                          keyboardType: TextInputType.number,
                          initialValue: bankNumber,
                          decoration: InputDecoration(
                              hintText: isEng
                                  ? "xxxxx-xxxx-xxxxx"
                                  : "xxxxx-xxxx-xxxxx",
                              label: Text(isEng
                                  ? "Bank Account Number"
                                  : "رقم الحساب البنكي")),
                          onChanged: (value) => bankNumber = value),
                    ),
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
