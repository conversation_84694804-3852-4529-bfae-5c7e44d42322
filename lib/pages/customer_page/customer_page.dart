import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/pages/customer_page/components/add_customer_screen.dart';
import 'package:mandob/pages/customer_page/components/user_list/user_list_widget.dart';
import 'package:mandob/providers/customers_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/app_bar.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

class CustomerPage extends StatefulWidget {
  final bool withoutHeader;

  const CustomerPage({super.key, this.withoutHeader = false});

  @override
  State<CustomerPage> createState() => _CustomerPageState();
}

class _CustomerPageState extends State<CustomerPage> {
  List<CustomerModel> customers = [];
  final _searchController = TextEditingController();
  String searchValue = '';

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      Provider.of<UserProvider>(context, listen: false).fetchUsers();
      await Provider.of<CustomersProvider>(context, listen: false)
          .fetchAllCustomers();
    });

    _searchController.addListener(() {
      setState(() {
        searchValue = _searchController.text;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final activeUser = userProvider.activeUser;
    bool isSearching = searchValue.isNotEmpty;

    return Consumer<CustomersProvider>(builder: (context, customerData, child) {
      if (activeUser!.isAdmin!) {
        if (selectedMandob == null) {
          customers = customerData.allCustomers;
        } else {
          customers = customerData.allCustomers
              .where((element) => element.mandobId == selectedMandob!.uid)
              .toList();
        }
      } else {
        customers = customerData.mandobCustomers;
      }

      if (isSearching) {
        customers = customers
            .where((element) => element.customerName!.contains(searchValue))
            .toList();
      }

      final searchCustomersField = Container(
        height: 55,
        margin: const EdgeInsets.only(top: 24, left: 24, right: 24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Colors.grey,
              blurRadius: 2,
              offset: Offset(0, 2),
            )
          ],
        ),
        child: TextField(
          controller: _searchController,
          textInputAction: TextInputAction.search,
          decoration: InputDecoration(
            hintText: isEng ? 'Search for a customer' : 'بحث عن عميل',
            hintStyle: const TextStyle(color: Colors.grey),
            prefixIcon: const Icon(CupertinoIcons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.grey),
            ),
          ),
        ),
      );

      return Scaffold(
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () => showDialog(
              context: context, builder: (_) => const AddCustomerScreen()),
          label: Text(isEng ? 'Add Customer' : 'إضافة عميل'),
          icon: const Icon(Icons.add),
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            selectedMandob = null;
            _searchController.clear();
            FocusScope.of(context).unfocus();
            await customerData.fetchAllCustomers();
          },
          child: Stack(
            children: [
              if (!widget.withoutHeader) ...[
                appBarWidget(context, title: isEng ? 'Customers' : 'العملاء'),
              ],
              Padding(
                padding: EdgeInsets.only(top: widget.withoutHeader ? 0 : 40),
                child: SafeArea(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      //! Search Field
                      searchCustomersField,

                      //! Admin only Filter
                      // if (activeUser.isAdmin!) ...[
                      //   Padding(
                      //     padding: const EdgeInsets.only(top: 14, right: 24),
                      //     child: customerFilterDropDown(userProvider.users!),
                      //   )
                      // ],

                      AppGaps.gap8,

                      //! User List
                      Expanded(child: UserListWidget(customers: customers)),
                    ],
                  ),
                ),
              ),
              if (isSearching && customers.isEmpty) ...[
                Center(
                  child: Text(isEng ? 'No results found' : 'لا يوجد نتائج'),
                )
              ],
              if (customerData.isLoading) ...[
                const Align(alignment: Alignment.center, child: LoadingWidget())
              ]
            ],
          ),
        ),
      );
    });
  }

  UserModel? selectedMandob;

  Widget customerFilterDropDown(List<UserModel> users) {
    final isEng = context.isEng;
    return DropdownButton<UserModel>(
        value: selectedMandob,
        hint: Text(isEng ? 'Select Representative' : 'اختر المندوب'),
        underline: Container(),
        items: [
          ...users.where((element) => !element.isAdmin!).map((value) {
            return DropdownMenuItem<UserModel>(
              value: value,
              child: Text(
                value.name ?? "",
                style: const TextStyle(color: Colors.black),
              ),
            );
          })
        ],
        onChanged: (val) {
          setState(() {
            selectedMandob = val!;
          });
        });
  }
}
