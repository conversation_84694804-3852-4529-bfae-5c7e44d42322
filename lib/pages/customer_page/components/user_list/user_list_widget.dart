import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/pages/customer_page/components/add_customer_screen.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

import '../delete_customer_dialog.dart';

class UserListWidget extends StatelessWidget {
  final List<CustomerModel> customers;

  const UserListWidget({Key? key, required this.customers}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      padding: const EdgeInsets.all(12),
      separatorBuilder: (context, custIndex) {
        return const Padding(
            padding: EdgeInsets.only(left: 80, right: 80), child: Divider());
      },
      itemCount: customers.length,
      itemBuilder: (context, custIndex) {
        var customer = customers[custIndex];

        return Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.grey.withOpacity(0.3)),
          ),
          child: Stack(
            alignment: context.isEng ? Alignment.topRight : Alignment.topLeft,
            children: [
              ListTile(
                isThreeLine: true,
                minVerticalPadding: 5,
                title: Row(
                  children: [
                    Flexible(
                      child: Text(
                        customer.customerName ?? '',
                      ),
                    ),
                    AppGaps.gap8,
                    Text('(${customer.customerType?.toString()})'),
                    const Spacer()
                  ],
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(customer.streetName?.toString() ?? ''),
                    Text(customer.customerPhone?.toString() ?? ''),
                  ],
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    GestureDetector(
                      onTap: () => showDialog(
                          context: context,
                          builder: (_) =>
                              AddCustomerScreen(customer: customer)),
                      child: const CircleAvatar(
                        maxRadius: 15,
                        backgroundColor: ColorManager.primaryColor,
                        child: Icon(
                          Icons.edit,
                          size: 20,
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    GestureDetector(
                      onTap: () => showDialog(
                          context: context,
                          builder: (_) =>
                              DeleteCustomerDialog(customerId: customer.docId)),
                      child: const CircleAvatar(
                        backgroundColor: Colors.red,
                        maxRadius: 15,
                        child: Icon(
                          Icons.delete,
                          size: 20,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
