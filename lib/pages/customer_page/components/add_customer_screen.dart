import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/providers/customers_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:mandob/utils/submit_button.dart';
import 'package:persian_number_utility/persian_number_utility.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import 'add_customer_form.dart';

class AddCustomerScreen extends StatefulWidget {
  final CustomerModel? customer;
  final bool fromInvoiceDialog;

  const AddCustomerScreen({this.customer, this.fromInvoiceDialog = false});

  @override
  State<StatefulWidget> createState() => _AddDeliveryDialog();
}

class _AddDeliveryDialog extends State<AddCustomerScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  late CustomerModel customer;

  @override
  void initState() {
    if (widget.customer != null) {
      customer = widget.customer!;

      nameCtrl.text = customer.customerName.toString();
      phoneCtrl.text = customer.customerPhone.toString();
      streetNameCtrl.text = customer.streetName.toString();
      vatNumberCtrl.text = customer.vatNumber.toString();
      commercialRegisterNumberCtrl.text =
          customer.commercialRegisterNumber.toString();
      openingBalanceCtrl.text = customer.openingBalance.toString();
      buildingNumberCtrl.text = customer.buildingNumber.toString();
      plotIdentificationCtrl.text = customer.plotIdentification.toString();
      citySubdivisionCtrl.text = customer.citySubdivision.toString();
      cityCtrl.text = customer.city.toString();
      postalNumberCtrl.text = customer.postalNumber.toString();
      customerTypeValue = customer.customerType.toString();
    }

    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  bool loading = false;

  var nameCtrl = TextEditingController();
  var phoneCtrl = TextEditingController();
  var streetNameCtrl = TextEditingController();
  var openingBalanceCtrl = TextEditingController();
  var vatNumberCtrl = TextEditingController();
  var commercialRegisterNumberCtrl = TextEditingController();
  var buildingNumberCtrl = TextEditingController();
  var plotIdentificationCtrl = TextEditingController();
  var citySubdivisionCtrl = TextEditingController();
  var cityCtrl = TextEditingController();
  var postalNumberCtrl = TextEditingController();

  var formKey = GlobalKey<FormState>();

  String customerTypeValue = 'فرد';

  String mandob = '';

  late UserModel user;

  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;
    final userProvider =
        Provider.of<UserProvider>(context, listen: false).activeUser;

    final isKeyboardOpened = MediaQuery.of(context).viewInsets.bottom != 0;

    return Material(
      child: Consumer<CustomersProvider>(
        builder: (context, _customers, child) {
          return WillPopScope(
            onWillPop: () async {
              if (MediaQuery.of(context).viewInsets.bottom != 0) {
              } else {
                Navigator.pop(context);
              }
              return true;
            },
            child: Form(
              key: formKey,
              child: Scaffold(
                bottomNavigationBar: Padding(
                  padding: EdgeInsets.only(
                      bottom: isKeyboardOpened ? 70 : 20.0,
                      right: 20,
                      left: 20),
                  child: SubmitButton(
                    label: widget.customer == null
                        ? (isEng ? 'Add' : 'إضافة')
                        : (isEng ? 'Edit' : 'تعديل'),
                    onPressed: () async {
                      if (!formKey.currentState!.validate()) return;
                      setState(() {
                        loading = true;
                      });
                      if (widget.customer == null) {
                        await _customers
                            .addCustomer(CustomerModel.fromJson({
                          'customerName': nameCtrl.text,
                          'customerPhone': phoneCtrl.text,
                          'address': streetNameCtrl.text,
                          'taxNumber': vatNumberCtrl.text,
                          'commercialRegisterNumber':
                              commercialRegisterNumberCtrl.text,
                          'openingBalance': double.tryParse(
                                  openingBalanceCtrl.text.toEnglishDigit()) ??
                              0.0,
                          'debit': double.tryParse(
                                  openingBalanceCtrl.text.toEnglishDigit()) ??
                              0.0,
                          'customerType': customerTypeValue,
                          'mandobId': !userProvider!.isAdmin!
                              ? userProvider!.uid
                              : user.uid,
                          'buildingNumber': buildingNumberCtrl.text,
                          'plotIdentification': plotIdentificationCtrl.text,
                          'citySubdivision': citySubdivisionCtrl.text,
                          'city': cityCtrl.text,
                          'postalNumber': postalNumberCtrl.text,
                        }))
                            .then((value) async {
                          await _customers.fetchAllCustomers(forceLoad: true);

                          setState(() {
                            loading = false;
                          });

                          context.back();

                          showBar(
                              context,
                              isEng
                                  ? 'Added successfully'
                                  : 'تمت الإضافة بنجاح',
                              backgroundColor: ColorManager.primaryColor,
                              indicatorColor: ColorManager.secondaryColor,
                              icon: Icons.done_all);
                        });
                      } else {
                        await _customers
                            .editCustomer(
                                widget.customer!.docId!,
                                CustomerModel.fromJson({
                                  'customerId': widget.customer!.id,
                                  'customerName': nameCtrl.text,
                                  'customerPhone': phoneCtrl.text,
                                  'address': streetNameCtrl.text,
                                  'taxNumber': vatNumberCtrl.text,
                                  'commercialRegisterNumber':
                                      commercialRegisterNumberCtrl.text,
                                  'openingBalance':
                                      double.tryParse(openingBalanceCtrl.text),
                                  'debit': widget.customer!.debit,
                                  'customerType': customerTypeValue,
                                  'mandobId': !userProvider!.isAdmin!
                                      ? userProvider?.uid
                                      : user.uid,
                                  'buildingNumber': buildingNumberCtrl.text,
                                  'plotIdentification':
                                      plotIdentificationCtrl.text,
                                  'citySubdivision': citySubdivisionCtrl.text,
                                  'city': cityCtrl.text,
                                  'postalNumber': postalNumberCtrl.text,
                                }))
                            .then((value) {
                          setState(() {
                            loading = false;
                          });
                          context.back();
                          showBar(
                              context,
                              isEng
                                  ? 'Edited successfully'
                                  : 'تم التعديل بنجاح',
                              backgroundColor: ColorManager.primaryColor,
                              indicatorColor: ColorManager.secondaryColor,
                              icon: Icons.done_all);
                        });
                      }
                    },
                  ),
                ),
                appBar: AppBar(
                  leading: const BackButton(
                    color: Colors.white,
                  ),
                  title: Text(
                    widget.customer == null
                        ? (isEng ? 'Add New Customer' : 'إضافة عميل جديد')
                        : (isEng
                            ? 'Edit Customer Data'
                            : 'تعديل بيانات العميل'),
                  ),
                  centerTitle: true,
                  backgroundColor: ColorManager.primaryColor,
                ),
                body: Stack(
                  children: <Widget>[
                    Container(
                        decoration: ShapeDecoration(
                            color: Colors.white,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(15.0))),
                        child: ListView(
                          padding: const EdgeInsets.all(20),
                          shrinkWrap: true,
                          children: [
                            AddCustomerForm(
                              isEditing: widget.customer != null,
                              nameCtrl: nameCtrl,
                              phoneCtrl: phoneCtrl,
                              customerTypeValue: customerTypeValue,
                              opneningBalanceCtrl: openingBalanceCtrl,
                              customerTypeOnChanged: (val) {
                                setState(() {
                                  customerTypeValue = val;
                                });
                              },
                              taxNumberCtrl: vatNumberCtrl,
                              commercialRegisterNumberCtrl:
                                  commercialRegisterNumberCtrl,
                              streetNameCtrl: streetNameCtrl,
                              buildingNumberCtrl: buildingNumberCtrl,
                              plotIdentificationCtrl: plotIdentificationCtrl,
                              citySubdivisionCtrl: citySubdivisionCtrl,
                              cityCtrl: cityCtrl,
                              postalNumberCtrl: postalNumberCtrl,
                            ),
                            const SizedBox(height: 5),
                            userProvider!.isAdmin!
                                ? Consumer<UserProvider>(
                                    builder: (context, mandobData, child) {
                                    if (mandobData.users!.isEmpty) {
                                      return const Center(
                                          child: LoadingWidget());
                                    }

                                    if (widget.customer != null &&
                                        mandob == '') {
                                      user =
                                          mandobData.users!.firstWhere((val) {
                                        return val.uid == customer.mandobId;
                                      });
                                      mandob = user.name.toString();
                                    }

                                    if (mandob == '' &&
                                        widget.customer == null) {
                                      mandob =
                                          mandobData.users![0].name.toString();

                                      user =
                                          mandobData.users!.firstWhere((val) {
                                        return val.name == mandob;
                                      });
                                    }

                                    return Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(isEng
                                            ? 'Representative'
                                            : 'المندوب'),
                                        const SizedBox(width: 5),
                                        DropdownButton<String>(
                                            value: mandob,
                                            underline: Container(),
                                            items: [
                                              ...mandobData.users!.map((value) {
                                                return DropdownMenuItem<String>(
                                                  value: value.name,
                                                  child: Text(
                                                    value.name ?? "",
                                                    style: const TextStyle(
                                                        color: Colors.black),
                                                  ),
                                                );
                                              })
                                            ],
                                            onChanged: (val) {
                                              setState(() {
                                                mandob = val!;

                                                user = mandobData.users!
                                                    .firstWhere((val) {
                                                  return val.name == mandob;
                                                });
                                              });
                                            }),
                                      ],
                                    );
                                  })
                                : Container(),
                            const SizedBox(height: 40),
                          ],
                        )),
                    loading
                        ? const Center(
                            child: LoadingWidget(),
                          )
                        : Container()
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// import 'package:flutter/material.dart';
// import 'package:mandob/core/extensions/context_extensions.dart';
// import 'package:mandob/models/customer_model.dart';
// import 'package:mandob/models/user_model.dart';
// import 'package:mandob/providers/customers_provider.dart';
// import 'package:mandob/providers/user_provider.dart';
// import 'package:mandob/utils/color_manager.dart';
// import 'package:mandob/utils/loading_widget.dart';
// import 'package:mandob/utils/show_bar/flush_bar.dart';
// import 'package:mandob/utils/submit_button.dart';
// import 'package:persian_number_utility/persian_number_utility.dart';
// import 'package:provider/provider.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// import 'add_customer_form.dart';
//
// class AddCustomerScreen extends StatefulWidget {
//   final CustomerModel? customer;
//   final bool fromInvoiceDialog;
//
//   const AddCustomerScreen({this.customer, this.fromInvoiceDialog = false});
//
//   @override
//   State<StatefulWidget> createState() => _AddDeliveryDialog();
// }
//
// class _AddDeliveryDialog extends State<AddCustomerScreen>
//     with SingleTickerProviderStateMixin {
//   late AnimationController controller;
//   late Animation<double> scaleAnimation;
//
//   late CustomerModel customer;
//
//   @override
//   void initState() {
//     if (widget.customer != null) {
//       customer = widget.customer!;
//
//       nameCtrl.text = customer.customerName.toString();
//       phoneCtrl.text = customer.customerPhone.toString();
//       addressCtrl.text = customer.streetName.toString();
//       taxNumberCtrl.text = customer.vatNumber.toString();
//       openingBalanceCtrl.text = customer.openingBalance.toString();
//       customerTypeValue = customer.customerType.toString();
//     }
//
//     controller = AnimationController(
//         vsync: this, duration: const Duration(milliseconds: 500));
//     scaleAnimation =
//         CurvedAnimation(parent: controller, curve: Curves.elasticInOut);
//
//     controller.addListener(() {
//       setState(() {});
//     });
//
//     controller.forward();
//
//     super.initState();
//   }
//
//   bool loading = false;
//
//   var nameCtrl = TextEditingController();
//   var phoneCtrl = TextEditingController();
//   var addressCtrl = TextEditingController();
//   var openingBalanceCtrl = TextEditingController();
//   var taxNumberCtrl = TextEditingController();
//
//   var formKey = GlobalKey<FormState>();
//
//   String customerTypeValue = 'فرد';
//
//   String mandob = '';
//
//   late UserModel user;
//
//   @override
//   Widget build(BuildContext context) {
//     final isEng = context.isEng;
//     final userProvider =
//         Provider.of<UserProvider>(context, listen: false).activeUser;
//
//     final isKeyboardOpened = MediaQuery.of(context).viewInsets.bottom != 0;
//
//     return Material(
//       child: Consumer<CustomersProvider>(
//         builder: (context, _customers, child) {
//           return WillPopScope(
//             onWillPop: () async {
//               if (MediaQuery.of(context).viewInsets.bottom != 0) {
//               } else {
//                 Navigator.pop(context);
//               }
//               return true;
//             },
//             child: Form(
//               key: formKey,
//               child: Scaffold(
//                 bottomNavigationBar: Padding(
//                   padding: EdgeInsets.only(
//                       bottom: isKeyboardOpened ? 70 : 20.0,
//                       right: 20,
//                       left: 20),
//                   child: SubmitButton(
//                     label: widget.customer == null
//                         ? (isEng ? 'Add' : 'إضافة')
//                         : (isEng ? 'Edit' : 'تعديل'),
//                     onPressed: () async {
//                       if (!formKey.currentState!.validate()) return;
//                       setState(() {
//                         loading = true;
//                       });
//                       if (widget.customer == null) {
//                         await _customers
//                             .addCustomer(CustomerModel.fromJson({
//                           'customerName': nameCtrl.text,
//                           'customerPhone': phoneCtrl.text,
//                           'address': addressCtrl.text,
//                           'taxNumber': taxNumberCtrl.text,
//                           'openingBalance': double.tryParse(
//                                   openingBalanceCtrl.text.toEnglishDigit()) ??
//                               0.0,
//                           'debit': double.tryParse(
//                                   openingBalanceCtrl.text.toEnglishDigit()) ??
//                               0.0,
//                           'customerType': customerTypeValue,
//                           'mandobId': !userProvider!.isAdmin!
//                               ? userProvider!.uid
//                               : user.uid,
//                         }))
//                             .then((value) async {
//                           await _customers.fetchAllCustomers(forceLoad: true);
//
//                           setState(() {
//                             loading = false;
//                           });
//
//                           context.back();
//
//                           showBar(
//                               context,
//                               isEng
//                                   ? 'Added successfully'
//                                   : 'تمت الإضافة بنجاح',
//                               backgroundColor: ColorManager.primaryColor,
//                               indicatorColor: ColorManager.secondaryColor,
//                               icon: Icons.done_all);
//                         });
//                       } else {
//                         await _customers
//                             .editCustomer(
//                                 widget.customer!.docId!,
//                                 CustomerModel.fromJson({
//                                   'customerId': widget.customer!.id,
//                                   'customerName': nameCtrl.text,
//                                   'customerPhone': phoneCtrl.text,
//                                   'address': addressCtrl.text,
//                                   'taxNumber': taxNumberCtrl.text,
//                                   'openingBalance':
//                                       double.tryParse(openingBalanceCtrl.text),
//                                   'debit': widget.customer!.debit,
//                                   'customerType': customerTypeValue,
//                                   'mandobId': !userProvider!.isAdmin!
//                                       ? userProvider?.uid
//                                       : user.uid,
//                                 }))
//                             .then((value) {
//                           setState(() {
//                             loading = false;
//                           });
//                           context.back();
//                           showBar(
//                               context,
//                               isEng
//                                   ? 'Edited successfully'
//                                   : 'تم التعديل بنجاح',
//                               backgroundColor: ColorManager.primaryColor,
//                               indicatorColor: ColorManager.secondaryColor,
//                               icon: Icons.done_all);
//                         });
//                       }
//                     },
//                   ),
//                 ),
//                 appBar: AppBar(
//                   leading: const BackButton(
//                     color: Colors.white,
//                   ),
//                   title: Text(
//                     widget.customer == null
//                         ? (isEng ? 'Add New Customer' : 'إضافة عميل جديد')
//                         : (isEng
//                             ? 'Edit Customer Data'
//                             : 'تعديل بيانات العميل'),
//                   ),
//                   centerTitle: true,
//                   backgroundColor: ColorManager.primaryColor,
//                 ),
//                 body: Stack(
//                   children: <Widget>[
//                     Container(
//                         padding: const EdgeInsets.all(20),
//                         decoration: ShapeDecoration(
//                             color: Colors.white,
//                             shape: RoundedRectangleBorder(
//                                 borderRadius: BorderRadius.circular(15.0))),
//                         child: ListView(
//                           shrinkWrap: true,
//                           children: [
//                             AddCustomerForm(
//                               isEditing: widget.customer != null,
//                               nameCtrl: nameCtrl,
//                               phoneCtrl: phoneCtrl,
//                               customerTypeValue: customerTypeValue,
//                               opneningBalanceCtrl: openingBalanceCtrl,
//                               customerTypeOnChanged: (val) {
//                                 setState(() {
//                                   customerTypeValue = val;
//                                 });
//                               },
//                               taxNumberCtrl: taxNumberCtrl,
//                               streetNameCtrl: addressCtrl,
//                             ),
//                             const SizedBox(
//                               height: 5,
//                             ),
//                             userProvider!.isAdmin!
//                                 ? Consumer<UserProvider>(
//                                     builder: (context, mandobData, child) {
//                                     if (mandobData.users!.isEmpty) {
//                                       return const Center(
//                                           child: LoadingWidget());
//                                     }
//
//                                     if (widget.customer != null &&
//                                         mandob == '') {
//                                       user =
//                                           mandobData.users!.firstWhere((val) {
//                                         return val.uid == customer.mandobId;
//                                       });
//                                       mandob = user.name.toString();
//                                     }
//
//                                     if (mandob == '' &&
//                                         widget.customer == null) {
//                                       mandob =
//                                           mandobData.users![0].name.toString();
//
//                                       user =
//                                           mandobData.users!.firstWhere((val) {
//                                         return val.name == mandob;
//                                       });
//                                     }
//
//                                     return Row(
//                                       mainAxisAlignment:
//                                           MainAxisAlignment.spaceBetween,
//                                       children: [
//                                         Text(isEng
//                                             ? 'Representative'
//                                             : 'المندوب'),
//                                         const SizedBox(
//                                           width: 5,
//                                         ),
//                                         DropdownButton<String>(
//                                             value: mandob,
//                                             underline: Container(),
//                                             items: [
//                                               ...mandobData.users!.map((value) {
//                                                 return DropdownMenuItem<String>(
//                                                   value: value.name,
//                                                   child: Text(
//                                                     value.name ?? "",
//                                                     style: const TextStyle(
//                                                         color: Colors.black),
//                                                   ),
//                                                 );
//                                               })
//                                             ],
//                                             onChanged: (val) {
//                                               setState(() {
//                                                 mandob = val!;
//
//                                                 user = mandobData.users!
//                                                     .firstWhere((val) {
//                                                   return val.name == mandob;
//                                                 });
//                                               });
//                                             }),
//                                       ],
//                                     );
//                                   })
//                                 : Container(),
//                             const SizedBox(
//                               height: 40,
//                             ),
//                           ],
//                         )),
//                     loading
//                         ? const Center(
//                             child: LoadingWidget(),
//                           )
//                         : Container()
//                   ],
//                 ),
//               ),
//             ),
//           );
//         },
//       ),
//     );
//   }
// }
