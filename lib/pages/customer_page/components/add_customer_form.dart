import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/utils/text_field.dart';

class AddCustomerForm extends StatelessWidget {
  final bool isEditing;
  final TextEditingController nameCtrl;
  final TextEditingController phoneCtrl;
  final TextEditingController streetNameCtrl;
  final TextEditingController taxNumberCtrl;
  final TextEditingController commercialRegisterNumberCtrl;
  final TextEditingController opneningBalanceCtrl;
  final TextEditingController buildingNumberCtrl;
  final TextEditingController plotIdentificationCtrl;
  final TextEditingController citySubdivisionCtrl;
  final TextEditingController cityCtrl;
  final TextEditingController postalNumberCtrl;
  final String customerTypeValue;
  final customerTypeOnChanged;

  const AddCustomerForm({
    super.key,
    required this.isEditing,
    required this.nameCtrl,
    required this.phoneCtrl,
    required this.streetNameCtrl,
    required this.taxNumberCtrl,
    required this.commercialRegisterNumberCtrl,
    required this.customerTypeValue,
    required this.customerTypeOnChanged,
    required this.opneningBalanceCtrl,
    required this.buildingNumberCtrl,
    required this.plotIdentificationCtrl,
    required this.citySubdivisionCtrl,
    required this.cityCtrl,
    required this.postalNumberCtrl,
  });

  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;

    return Column(
      children: [
        const SizedBox(height: 5),
        TextFieldWidget(
          controller: nameCtrl,
          label: isEng ? 'Customer Name' : 'اسم العميل',
        ),
        const SizedBox(height: 15),
        TextFieldWidget(
          controller: phoneCtrl,
          label: isEng ? 'Phone Number' : 'رقم الهاتف',
          textInputType: TextInputType.number,
        ),
        const SizedBox(height: 15),
        TextFieldWidget(
          controller: streetNameCtrl,
          label: isEng ? 'Street Name' : 'اسم الشارع',
          textInputType: TextInputType.text,
        ),
        const SizedBox(height: 5),
        Row(
          children: [
            Expanded(
              child: TextFieldWidget(
                controller: plotIdentificationCtrl,
                label: isEng ? 'Plot Identification' : 'القطعة',
                textInputType: TextInputType.text,
                required: false,
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(top: 20.0),
                child: TextFieldWidget(
                  controller: buildingNumberCtrl,
                  maxLength: 4,
                  label: isEng ? 'Building Number' : 'رقم المبنى',
                  textInputType: TextInputType.text,
                  validator: (val) {
                    if (val != null && val.isNotEmpty && val.length != 4) {
                      return isEng
                          ? 'Must be 4 digits'
                          : 'يجب أن يكون من 4 أرقام';
                    }
                    return null;
                  },
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 15),
        Row(
          children: [
            Expanded(
              child: TextFieldWidget(
                controller: citySubdivisionCtrl,
                label: isEng ? 'City Subdivision' : 'الحيّ',
                textInputType: TextInputType.text,
                required: false,
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: TextFieldWidget(
                controller: cityCtrl,
                label: isEng ? 'City' : 'المدينة',
                textInputType: TextInputType.text,
                required: false,
              ),
            ),
          ],
        ),
        const SizedBox(height: 15),
        TextFieldWidget(
          controller: postalNumberCtrl,
          label: isEng ? 'Postal Number' : 'الرمز البريدي',
          maxLength: 5,
          textInputType: TextInputType.text,
          validator: (val) {
            if (val != null && val.isNotEmpty && val.length != 5) {
              return isEng
                  ? 'Postal Number must be 5 digits'
                  : 'يجب أن يكون الرمز البريدي مكونًا من 5 أرقام';
            }
            return null;
          },
        ),
        const SizedBox(height: 15),
        if (customerTypeValue == (isEng ? 'Company' : 'مؤسسة')) ...[
          TextFieldWidget(
            controller: taxNumberCtrl,
            label: isEng ? 'VAT NO.' : 'الرقم الضريبي',
            textInputType: TextInputType.text,
            maxLength: 15,
            required: customerTypeValue == (isEng ? 'Company' : 'مؤسسة'),
            validator: (val) {
              if (val != null && (val.length < 13 || val.length > 15)) {
                return isEng
                    ? 'VAT Number must be between 13 and 15 digits'
                    : 'يجب أن يكون الرقم الضريبي بين 13 و 15 رقمًا';
              }
              return null;
            },
          ),
          const SizedBox(height: 15),
          TextFieldWidget(
            controller: commercialRegisterNumberCtrl,
            label: isEng ? 'Commercial Register Number' : 'رقم السجل التجاري',
            textInputType: TextInputType.text,
            required: customerTypeValue == (isEng ? 'Company' : 'مؤسسة'),
          ),
          const SizedBox(height: 15),
        ],
        if (!isEditing) ...[
          TextFieldWidget(
            controller: opneningBalanceCtrl,
            label: isEng ? 'Opening Balance' : 'الرصيد الإفتتاحي',
            textInputType: TextInputType.number,
          ),
          const SizedBox(height: 15),
        ],
        Row(
          children: [
            Expanded(child: Text(isEng ? 'Customer Type' : 'نوع العميل')),
            DropdownButton<String>(
              value: customerTypeValue,
              hint: Text(isEng ? 'Select' : 'اختر'),
              underline: Container(),
              items: <String>[
                isEng ? 'Individual' : 'فرد',
                isEng ? 'Company' : 'مؤسسة',
              ].map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(
                    value,
                    style: const TextStyle(color: Colors.black),
                  ),
                );
              }).toList(),
              onChanged: customerTypeOnChanged,
            ),
          ],
        ),
      ],
    );
  }
}

// import 'package:flutter/material.dart';
// import 'package:mandob/core/extensions/context_extensions.dart';
// import 'package:mandob/utils/text_field.dart';
//
// class AddCustomerForm extends StatelessWidget {
//   final bool isEditing;
//   final TextEditingController nameCtrl;
//   final TextEditingController phoneCtrl;
//   final TextEditingController streetNameCtrl;
//   final TextEditingController taxNumberCtrl;
//   final TextEditingController opneningBalanceCtrl;
//   final String customerTypeValue;
//   final customerTypeOnChanged;
//
//   const AddCustomerForm({
//     super.key,
//     required this.isEditing,
//     required this.nameCtrl,
//     required this.phoneCtrl,
//     required this.streetNameCtrl,
//     required this.taxNumberCtrl,
//     required this.customerTypeValue,
//     required this.customerTypeOnChanged,
//     required this.opneningBalanceCtrl,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final isEng = context.isEng;
//
//     return Column(
//       children: [
//         TextFieldWidget(
//           controller: nameCtrl,
//           label: isEng ? 'Customer Name' : 'اسم العميل',
//         ),
//         const SizedBox(
//           height: 15,
//         ),
//         TextFieldWidget(
//           controller: phoneCtrl,
//           label: isEng ? 'Phone Number' : 'رقم الهاتف',
//           textInputType: TextInputType.number,
//         ),
//         const SizedBox(
//           height: 15,
//         ),
//         TextFieldWidget(
//           controller: streetNameCtrl,
//           label: isEng ? 'Address' : 'العنوان',
//           textInputType: TextInputType.text,
//         ),
//         const SizedBox(
//           height: 15,
//         ),
//         TextFieldWidget(
//           controller: taxNumberCtrl,
//           label: isEng ? 'VAT NO.' : 'الرقم الضريبي',
//           textInputType: TextInputType.text,
//         ),
//         isEditing
//             ? const SizedBox()
//             : Column(
//                 children: [
//                   const SizedBox(
//                     height: 15,
//                   ),
//                   TextFieldWidget(
//                     controller: opneningBalanceCtrl,
//                     label: isEng ? 'Opening Balance' : 'الرصيد الإفتتاحي',
//                     textInputType: TextInputType.number,
//                   ),
//                 ],
//               ),
//         const SizedBox(
//           height: 15,
//         ),
//         Row(
//           children: [
//             Expanded(child: Text(isEng ? 'Customer Type' : 'نوع العميل')),
//             DropdownButton<String>(
//                 value: customerTypeValue,
//                 hint: Text(isEng ? 'Select' : 'اختر'),
//                 underline: Container(),
//                 items: <String>[
//                   isEng ? 'Individual' : 'فرد',
//                   isEng ? 'Company' : 'مؤسسة',
//                 ].map((String value) {
//                   return new DropdownMenuItem<String>(
//                     value: value,
//                     child: new Text(
//                       value,
//                       style: const TextStyle(color: Colors.black),
//                     ),
//                   );
//                 }).toList(),
//                 onChanged: customerTypeOnChanged),
//           ],
//         )
//       ],
//     );
//   }
// }
