import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/pages/delivery_page/components/add_delivery_dialog.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/app_bar.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import 'components/user_card.dart';

class DeliveryPage extends StatefulWidget {
  const DeliveryPage({super.key});

  @override
  State<DeliveryPage> createState() => _DeliveryPageState();
}

class _DeliveryPageState extends State<DeliveryPage> {
  String filter = "";

  onChangeFilter(String value) {
    setState(() {
      filter = value;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;

    final searchSuppliersField = Container(
      height: 55,
      margin: const EdgeInsets.only(top: 24, left: 24, right: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Colors.grey,
            blurRadius: 2,
            offset: Offset(0, 2),
          )
        ],
      ),
      child: TextField(
        // controller: _searchController,
        onChanged: (value) {
          onChangeFilter(value);
        },
        textInputAction: TextInputAction.search,
        decoration: InputDecoration(
          hintText:
              context.isEng ? 'Search for Representatives' : 'بحث عن مندوب',
          hintStyle: const TextStyle(color: Colors.grey),
          prefixIcon: const Icon(CupertinoIcons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Colors.grey),
          ),
        ),
      ),
    );

    return Scaffold(
      resizeToAvoidBottomInset: false,
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => showDialog(
            context: context, builder: (_) => const AddDeliveryDialog()),
        label: Text(isEng ? 'Add Representative' : 'إضافة مندوب'),
        icon: const Icon(Icons.add),
      ),
      body: Stack(
        children: [
          appBarWidget(
            context,
            title: context.isEng ? 'Representatives' : 'المندوبين',
          ),
          Padding(
            padding: const EdgeInsets.only(top: 60),
            child: Consumer<UserProvider>(builder: (context, users, child) {
              return FutureBuilder(
                  future: users.fetchUsers(),
                  builder: (context, snapshot) {
                    if (users.isLoading) {
                      return const Center(
                        child: LoadingWidget(),
                      );
                    }
                    return Column(
                      children: [
                        searchSuppliersField,
                        AppGaps.gap12,
                        Expanded(
                          child: Consumer<RepositoryProvider>(
                              builder: (context, repos, child) {
                            return FutureBuilder(
                                future: repos.fetchRepositories(),
                                builder: (context, AsyncSnapshot snapshot) {
                                  if (snapshot.connectionState ==
                                      ConnectionState.waiting) {
                                    return const Center(
                                      child: LoadingWidget(),
                                    );
                                  } else {
                                    return RefreshIndicator(
                                      onRefresh: () => users.fetchUsers(),
                                      child: ListView(
                                        padding:
                                            const EdgeInsets.only(bottom: 50),
                                        physics: const ScrollPhysics(
                                            parent: BouncingScrollPhysics()),
                                        children: users.users!
                                            .where(
                                                (element) => !element.isAdmin!)
                                            .where((element) => element.name!
                                                .toLowerCase()
                                                .startsWith(
                                                    filter.toLowerCase()))
                                            .map((mandob) {
                                          final singleUser = mandob;
                                          String? storeName = "";
                                          storeName = repos.repositories
                                                  .firstWhere(
                                                      (element) =>
                                                          element.id ==
                                                          singleUser.storeId,
                                                      orElse: () =>
                                                          RepositoryModel())
                                                  .name ??
                                              "";
                                          return UserCard(
                                              singleUser: singleUser,
                                              storeName: storeName);
                                        }).toList(),
                                      ),
                                    );
                                  }
                                });
                          }),
                        ),
                      ],
                    );
                  });
            }),
          ),
        ],
      ),
    );
  }
}
