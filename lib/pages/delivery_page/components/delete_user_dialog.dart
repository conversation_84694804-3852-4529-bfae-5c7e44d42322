import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/providers/invoices_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:provider/provider.dart';

class DeleteRepresentativeDialog extends StatefulWidget {
  final UserModel user;

  const DeleteRepresentativeDialog({super.key, required this.user});

  @override
  State<StatefulWidget> createState() => _DeleteRepresentativeDialogState();
}

class _DeleteRepresentativeDialogState extends State<DeleteRepresentativeDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  @override
  void initState() {
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  bool loading = false;

  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;

    return Stack(
      children: <Widget>[
        Center(
          child: Material(
            color: Colors.transparent,
            child: ScaleTransition(
              scale: scaleAnimation,
              child: Container(
                width: 250,
                padding: const EdgeInsets.all(15),
                decoration: ShapeDecoration(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15.0))),
                child: SingleChildScrollView(
                  child: Wrap(
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.only(right: 4.0, left: 4),
                        child: Center(
                          child: Text(
                            isEng
                                ? 'Do you want to delete this representative?'
                                : 'هل تريد حذف هذا المندوب !',
                            style: const TextStyle(
                                color: Colors.black87,
                                fontSize: 18,
                                height: 1.5),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                            top: 25, bottom: 10, right: 10),
                        child: Row(
                          textDirection: TextDirection.rtl,
                          children: <Widget>[
                            const SizedBox(width: 10),
                            GestureDetector(
                              onTap: () => Navigator.pop(context),
                              child: Container(
                                height: 35,
                                width: 80,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(7),
                                    color: Colors.grey),
                                child: Text(
                                  isEng ? 'Cancel' : 'إلغاء',
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ),
                            ),
                            const SizedBox(width: 15),
                            Consumer<InvoicesProvider>(
                              builder: (context, invoicesProvider, child) {
                                return GestureDetector(
                                  onTap: () async {
                                    setState(() {
                                      loading = true;
                                    });
                                    await invoicesProvider.fetchInvoices(
                                      userId: widget.user.uid,
                                    );
                                    final allInvoices =
                                        invoicesProvider.invoices;

                                    final ableToDelete = allInvoices.isEmpty;

                                    if (ableToDelete) {
                                      Provider.of<UserProvider>(context,
                                              listen: false)
                                          .deleteUser(widget.user);
                                      setState(() {
                                        loading = false;
                                      });
                                      Navigator.pop(context);

                                      showBar(
                                          context,
                                          isEng
                                              ? 'Representative deleted!'
                                              : 'تم حذف المندوب !');
                                    } else {
                                      setState(() {
                                        loading = false;
                                      });
                                      Navigator.pop(context);

                                      showDialog(
                                        context: context,
                                        builder: (context) => AlertDialog(
                                          content: Text(isEng
                                              ? "You cannot delete this representative because it is linked to other operations"
                                              : "لا يمكنك حذف هذا المندوب لارتباطه بعمليات اخرى"),
                                        ),
                                      );
                                    }
                                  },
                                  child: Container(
                                    height: 35,
                                    width: 80,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(7),
                                        color: Colors.red),
                                    child: Text(
                                      isEng ? 'Delete' : 'حذف',
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        loading
            ? const Center(
                child: LoadingWidget(),
              )
            : Container()
      ],
    );
  }
}
