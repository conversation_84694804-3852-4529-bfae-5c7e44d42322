import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/utils/loading_widget.dart';

class ShareDialog extends StatefulWidget {
  final String email;
  final String pass;

  ShareDialog({required this.email, required this.pass});

  @override
  State<StatefulWidget> createState() => _ShareDialog();
}

class _ShareDialog extends State<ShareDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  @override
  void initState() {
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  bool loading = false;

  var formKey = GlobalKey<FormState>();

  var priceFocus = FocusNode();

  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;

    return WillPopScope(
      onWillPop: () async {
        if (MediaQuery.of(context).viewInsets.bottom != 0) {
          priceFocus.unfocus();
        } else {
          Navigator.pop(context);
        }
        return true;
      },
      child: Form(
        key: formKey,
        child: Stack(
          children: <Widget>[
            Center(
              child: Material(
                color: Colors.transparent,
                child: ScaleTransition(
                  scale: scaleAnimation,
                  child: Container(
                      width: 300,
                      padding: const EdgeInsets.all(20),
                      decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15.0))),
                      child: ListView(
                        shrinkWrap: true,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                  flex: 4,
                                  child: Text(isEng
                                      ? 'Share Representative Data'
                                      : 'مشاركة البيانات الخاصة بالمندوب')),
                              const Spacer(),
                              IconButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                  },
                                  icon: const Icon(Icons.close))
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(isEng
                                  ? 'Email: ${widget.email}'
                                  : 'البريد: ${widget.email}'),
                              Text(isEng
                                  ? 'Password: ${widget.pass}'
                                  : 'كلمة المرور: ${widget.pass}'),
                            ],
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          //   children: [
                          //     SubmitButton(
                          //       onPressed: () async {
                          //         var response = await FlutterShareMe()
                          //             .shareToSystem(
                          //                 msg:
                          //                     'Your new account details is:\nEmail: ${widget.email}\nPass: ${widget.pass}');
                          //         if (response == 'success') {
                          //           debugPrint('error while sharing !');
                          //
                          //           print('share success');
                          //         } else {}
                          //       },
                          //       label: isEng ? 'Share' : 'مشاركة',
                          //     ),
                          //     Padding(
                          //       padding: const EdgeInsets.only(bottom: 3.0),
                          //       child: InkWell(
                          //         onTap: () {
                          //           FlutterShareMe()
                          //               .shareToWhatsApp(
                          //                   msg:
                          //                       'Your new account details is:\nEmail: ${widget.email}\nPass: ${widget.pass}')
                          //               .catchError((_) {
                          //             debugPrint('error while sharing !');
                          //           });
                          //         },
                          //         child: const Icon(
                          //           FontAwesomeIcons.whatsapp,
                          //           size: 35,
                          //           color: Colors.green,
                          //         ),
                          //       ),
                          //     ),
                          //   ],
                          // ),
                        ],
                      )),
                ),
              ),
            ),
            loading
                ? const Center(
                    child: LoadingWidget(),
                  )
                : Container()
          ],
        ),
      ),
    );
  }
}
