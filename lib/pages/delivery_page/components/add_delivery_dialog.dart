import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/product_model.dart';
import 'package:mandob/models/repository_model.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/pages/delivery_page/delivery_page.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:mandob/utils/submit_button.dart';
import 'package:mandob/utils/text_field.dart';
import 'package:provider/provider.dart';

import '../../../utils/color_manager.dart';

class AddDeliveryDialog extends StatefulWidget {
  final UserModel? user;
  final String? storeName;

  const AddDeliveryDialog({Key? key, this.user, this.storeName})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _AddDeliveryDialog();
}

class _AddDeliveryDialog extends State<AddDeliveryDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  final TextEditingController nameCtrl = TextEditingController();
  final TextEditingController emailCtrl = TextEditingController();
  final TextEditingController passCtrl = TextEditingController();
  final TextEditingController phoneCtrl = TextEditingController();
  final TextEditingController repoNameCtrl = TextEditingController();

  var formKey = GlobalKey<FormState>();

  var nameFocus = FocusNode();
  var emailFocus = FocusNode();
  var passFocus = FocusNode();
  var phoneFocus = FocusNode();
  var repoNameFocus = FocusNode();
  String storeName = '';

  bool _isAdmin = false;
  bool _createNewRepo = false;

  @override
  void initState() {
    Provider.of<RepositoryProvider>(context, listen: false)
        .fetchRepositories()
        .then((value) {
      if (widget.storeName != null && widget.storeName != '') {
        if (storeName == '') {
          storeName = widget.storeName!;
        }
      } else {
        if (storeName == '') {
          storeName = value.isEmpty ? '' : value[0].name.toString();
        }
      }

      setState(() {});
    });

    // Fetch products for new repository creation
    Provider.of<ProductsProvider>(context, listen: false).fetchProducts();

    if (widget.user != null) {
      nameCtrl.text = widget.user!.name!;
      emailCtrl.text = widget.user!.email!;
      passCtrl.text = widget.user!.password!;
      phoneCtrl.text = widget.user!.phone!;
    }

    // Add listener to name controller to auto-update repo name
    nameCtrl.addListener(() {
      if (_createNewRepo) {
        repoNameCtrl.text = 'مخزن ${nameCtrl.text}';
      }
    });

    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  bool loading = false;

  Future<String?> createNewRepository() async {
    try {
      final productsProvider =
          Provider.of<ProductsProvider>(context, listen: false);
      final repoProvider =
          Provider.of<RepositoryProvider>(context, listen: false);
      final quantitiesProvider =
          Provider.of<QuantitiesProvider>(context, listen: false);

      // Get all products
      final products = productsProvider.products ?? [];

      // Create products map with all products having quantity 0
      Map<String, dynamic> storeProducts = {};
      for (ProductModel product in products) {
        storeProducts[product.id!] = product.price ?? 0.0;
      }

      // Create repository model
      final repoModel = RepositoryModel.fromJson({
        'name': repoNameCtrl.text,
        'createdAt': DateTime.now().toIso8601String(),
        'location': 'Saudi Arabia',
        "products": jsonEncode(storeProducts)
      });

      // Create repository
      await repoProvider.createRepository(repoModel);
      await repoProvider.fetchRepositories();

      // Get the created repository
      final createdRepo = repoProvider.repositories
          .firstWhere((element) => element.name == repoNameCtrl.text);

      // Set quantities for all products to 0
      for (ProductModel product in products) {
        await quantitiesProvider.modifyQuantity(
          productId: product.id!,
          storeId: createdRepo.id!,
          quantity: 0,
        );
      }

      return createdRepo.id;
    } catch (e) {
      debugPrint('Error creating new repository: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isKeyboardOpened = MediaQuery.of(context).viewInsets.bottom != 0;

    return WillPopScope(
      onWillPop: () async {
        if (MediaQuery.of(context).viewInsets.bottom != 0) {
          nameFocus.unfocus();
          emailFocus.unfocus();
          passFocus.unfocus();
          phoneFocus.unfocus();
          repoNameFocus.unfocus();
        } else {
          Navigator.pop(context);
        }
        return true;
      },
      child: Consumer<RepositoryProvider>(
        builder: (context, repo, child) {
          return Scaffold(
            bottomNavigationBar: Padding(
              padding: EdgeInsets.only(
                  bottom: isKeyboardOpened ? 70 : 20.0, right: 20, left: 20),
              child: SubmitButton(
                label: context.isEng ? "Save" : "حفظ",
                color: ColorManager.primaryColor,
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    formKey.currentState!.save();
                    setState(() {
                      loading = true;
                    });

                    String? storeId;

                    // Handle new repository creation
                    if (_createNewRepo) {
                      storeId = await createNewRepository();
                      if (storeId == null) {
                        setState(() {
                          loading = false;
                        });
                        showBar(
                            context,
                            context.isEng
                                ? 'Failed to create repository'
                                : 'فشل في إنشاء المخزن',
                            backgroundColor: Colors.red,
                            indicatorColor: Colors.white,
                            icon: Icons.error);
                        return;
                      }
                    } else {
                      storeId = repo.repositories
                          .firstWhere((element) => element.name == storeName)
                          .id!;
                    }

                    if (widget.user == null) {
                      await Provider.of<UserProvider>(context, listen: false)
                          .createUser(
                        email: emailCtrl.text.trim(),
                        name: nameCtrl.text,
                        password: passCtrl.text,
                        phone: phoneCtrl.text,
                        storeId: storeId,
                      )
                          .then((value) async {
                        await Provider.of<UserProvider>(context, listen: false)
                            .fetchUsers(withLoading: true);

                        nameCtrl.clear();
                        emailCtrl.clear();
                        passCtrl.clear();
                        phoneCtrl.clear();
                        repoNameCtrl.clear();

                        setState(() {
                          loading = false;
                        });

                        Navigator.pop(context);

                        showBar(
                            context,
                            context.isEng
                                ? 'Added successfully'
                                : 'تمت الإضافة بنجاح',
                            backgroundColor: ColorManager.primaryColor,
                            indicatorColor: ColorManager.secondaryColor,
                            icon: Icons.done_all);
                      });
                    } else {
                      await Provider.of<UserProvider>(context, listen: false)
                          .editUser(
                        UserModel.fromJson({
                          "uid": widget.user!.uid,
                          "email": widget.user!.email,
                          "isAdmin": _isAdmin,
                          "name": nameCtrl.text,
                          "password": passCtrl.text,
                          "phone": phoneCtrl.text,
                          "storeId": storeId,
                        }),
                      )
                          .then((value) {
                        setState(() {
                          loading = false;
                        });
                        Navigator.pop(context);
                        showBar(
                            context,
                            context.isEng
                                ? 'Edited successfully'
                                : 'تم التعديل بنجاح',
                            backgroundColor: ColorManager.primaryColor,
                            indicatorColor: ColorManager.secondaryColor,
                            icon: Icons.done_all);
                      });
                    }
                  }
                },
              ),
            ),
            appBar: AppBar(
              leading: const BackButton(
                color: Colors.white,
              ),
              actions: [
                IconButton(
                  icon: const Icon(
                    Icons.people,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const DeliveryPage(),
                      ),
                    );
                  },
                ),
              ],
              backgroundColor: ColorManager.primaryColor,
              title: Text(
                widget.user == null
                    ? context.isEng
                        ? 'Add Representative'
                        : 'إضافة مندوب'
                    : context.isEng
                        ? 'Edit Representative'
                        : 'تعديل مندوب',
                style: const TextStyle(
                  color: Colors.white,
                ),
              ),
            ),
            body: Form(
              key: formKey,
              child: Stack(
                children: <Widget>[
                  Container(
                      padding: const EdgeInsets.all(20),
                      decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15.0))),
                      child: ListView(
                        shrinkWrap: true,
                        children: [
                          _formFields(),
                          const SizedBox(
                            height: 10,
                          ),

                          // New Repository Checkbox
                          if (widget.user == null)
                            CheckboxListTile(
                              title: Text(context.isEng
                                  ? 'Create New Repository'
                                  : 'إنشاء مخزن جديد'),
                              value: _createNewRepo,
                              onChanged: (bool? value) {
                                setState(() {
                                  _createNewRepo = value ?? false;
                                  if (_createNewRepo) {
                                    repoNameCtrl.text = 'مخزن ${nameCtrl.text}';
                                  } else {
                                    repoNameCtrl.clear();
                                  }
                                });
                              },
                            ),

                          // Repository Name Field (shown when checkbox is checked)
                          if (_createNewRepo && widget.user == null)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12.0),
                              child: TextFieldWidget(
                                focusNode: repoNameFocus,
                                controller: repoNameCtrl,
                                label: context.isEng
                                    ? "Repository Name"
                                    : "اسم المخزن",
                                hint: context.isEng
                                    ? 'Enter repository name'
                                    : 'أدخل اسم المخزن',
                                validator: (value) {
                                  if (_createNewRepo &&
                                      (value == null || value.isEmpty)) {
                                    return context.isEng
                                        ? 'Please enter the repository name'
                                        : 'الرجاء ادخال اسم المخزن';
                                  }
                                  return null;
                                },
                              ),
                            ),

                          if (_createNewRepo && widget.user == null)
                            const SizedBox(height: 10),

                          // Existing repositories dropdown (hidden when creating new repo)
                          if (!_createNewRepo)
                            if (repo.repositories.isEmpty)
                              Center(
                                child: Text(context.isEng
                                    ? 'No repositories available!'
                                    : 'لا يوجد مخازن !'),
                              )
                            else
                              Center(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12),
                                  child: DropdownButton<String>(
                                      isExpanded: true,
                                      value: storeName.isEmpty
                                          ? repo.repositories[0].name
                                          : storeName,
                                      underline: Container(),
                                      items: repo.repositories.map((value) {
                                        return DropdownMenuItem<String>(
                                          value: value.name,
                                          child: Text(
                                            value.name ?? "",
                                            style: const TextStyle(
                                                color: Colors.black),
                                          ),
                                        );
                                      }).toList(),
                                      onChanged: (val) {
                                        setState(() {
                                          storeName = val!;
                                        });
                                      }),
                                ),
                              ),

                          //? Add repo
                          // if (widget.user == null && !_createNewRepo)
                          //   ElevatedButton(
                          //       onPressed: () {
                          //         Navigator.push(
                          //             context,
                          //             MaterialPageRoute(
                          //                 builder: (context) =>
                          //                     const RepositoriesPage()));
                          //       },
                          //       child: Text(context.isEng
                          //           ? 'Add Repository'
                          //           : 'اضافة مخزن'))
                          // else
                          //   Container(),

                          const SizedBox(
                            height: 40,
                          ),
                        ],
                      )),
                  loading
                      ? const Center(
                          child: LoadingWidget(),
                        )
                      : Container()
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _formFields() {
    final isEng = context.isEng;

    return Column(
      children: [
        TextFieldWidget(
          focusNode: nameFocus,
          controller: nameCtrl,
          // decoration: InputDecoration(
          label: isEng ? "Name" : "الاسم",
          hint: isEng ? 'Enter representative name' : 'أدخل اسم المندوب',
          // ),
          validator: (value) {
            if (value!.isEmpty) {
              return isEng
                  ? 'Please enter the representative name'
                  : 'الرجاء ادخال اسم المندوب';
            }
            return null;
          },
        ),
        const SizedBox(
          height: 10,
        ),
        TextFieldWidget(
          readOnly: widget.user != null,
          // decoration: InputDecoration(
          label: isEng ? "Email" : "البريد",
          hint: isEng ? 'Enter representative email' : 'أدخل بريد المندوب',
          // ),
          focusNode: emailFocus,
          controller: emailCtrl,
          validator: (value) {
            if (value!.isEmpty) {
              return isEng
                  ? 'Please enter the email'
                  : 'برجاء ادخال البريد الالكتروني';
            }
            return null;
          },
        ),
        const SizedBox(
          height: 10,
        ),
        TextFieldWidget(
          readOnly: widget.user != null,
          focusNode: passFocus,
          controller: passCtrl,
          validator: (value) {
            if (value!.isEmpty) {
              return isEng
                  ? 'Please enter the password'
                  : 'يجب ادخال كلمة المرور';
            } else if (value.length < 8) {
              return isEng
                  ? 'Password must be more than 8 characters'
                  : 'يجب ان تكون كلمة المرور اكثر من 8 احرف';
            }
            return null;
          },
          // decoration: InputDecoration(
          label: isEng ? "Password" : "كلمة المرور",
          hint: isEng ? 'Enter password' : ' ادخل كلمة المرور',
          // ),
        ),
        const SizedBox(
          height: 10,
        ),
        TextFieldWidget(
          focusNode: phoneFocus,
          controller: phoneCtrl,
          textInputType: TextInputType.number,
          // decoration: InputDecoration(
          label: isEng ? "Phone Number" : "رقم الهاتف",
          hint: isEng ? 'Enter phone number' : 'أدخل رقم الهاتف',
          // ),
        ),
      ],
    );
  }
}
