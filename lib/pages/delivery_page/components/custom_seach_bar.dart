import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';

class CustomSearchBar extends StatelessWidget {
  final String? hintText;
  final Function(String)? onChangeFilter;
  final TextEditingController? controller;
  const CustomSearchBar(
      {Key? key, required this.onChangeFilter, this.hintText, this.controller})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        icon: const Icon(
          Icons.search,
          color: Colors.black,
        ),
        hintText: hintText ?? (context.isEng ? 'Search' : 'بحث'),
        fillColor: Colors.transparent,
        contentPadding: const EdgeInsets.fromLTRB(10.0, 15.0, 10.0, 10.0),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(10.0)),
        filled: true,
      ),
      onChanged: onChangeFilter,
    );
  }
}
