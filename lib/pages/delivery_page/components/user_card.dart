import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/pages/delivery_page/components/delete_user_dialog.dart';

import 'add_delivery_dialog.dart';

class UserCard extends StatelessWidget {
  const UserCard({
    Key? key,
    required this.singleUser,
    required this.storeName,
  }) : super(key: key);

  final UserModel singleUser;
  final String? storeName;

  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;

    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 12,
              blurRadius: 4,
              offset: const Offset(0, 3), // changes position of shadow
            ),
          ],
        ),
        child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                height: 10,
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(isEng ? 'Name' : 'الاسم : '),
                    const Spacer(),
                    Text(
                      singleUser.name!,
                      maxLines: 1,
                      style: const TextStyle(fontSize: 15),
                    ),
                  ],
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(isEng ? 'Store' : 'المخزن : '),
                    const Spacer(),
                    Text(
                      storeName!,
                      maxLines: 1,
                      style: const TextStyle(fontSize: 15),
                    ),
                  ],
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(isEng ? 'Email :' : 'البريد : '),
                    Flexible(
                        child: Text(
                      singleUser.email.toString(),
                    ))
                  ],
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(isEng ? 'Phone :' : 'رقم الهاتف : '),
                    const Spacer(),
                    Text(singleUser.phone.toString())
                  ],
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(isEng ? 'Password :' : 'كلمة المرور : '),
                    const Spacer(),
                    PasswordWidget(pass: singleUser.password!)
                  ],
                ),
              ),
              Row(
                children: [
                  Expanded(
                      child: InkWell(
                    onTap: () async {
                      await showDialog(
                          context: context,
                          builder: (_) => AddDeliveryDialog(
                                user: singleUser,
                                storeName: storeName,
                              ));
                    },
                    child: const Material(
                      color: Colors.blue,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(15),
                          bottomRight: Radius.circular(15)),
                      child: Center(
                        child: Padding(
                          padding: EdgeInsets.all(5.0),
                          child: Icon(
                            Icons.edit,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  )),
                  const SizedBox(width: 20),
                  Expanded(
                    child: InkWell(
                      onTap: () async => await showDialog(
                        context: context,
                        builder: (ctx) =>
                            DeleteRepresentativeDialog(user: singleUser),
                        //     AlertDialog(
                        //   content: Text(isEng
                        //       ? "This representative's data will be permanently deleted"
                        //       : " سيتم حذف بيانات المندوب نهائياً"),
                        //   actions: [
                        //     TextButton(
                        //         onPressed: () {
                        //           Navigator.pop(ctx);
                        //           return;
                        //         },
                        //         child: Text(isEng ? "Cancel" : "الغاء")),
                        //     Consumer<InvoicesProvider>(
                        //         builder: (context, invoices, child) {
                        //       return TextButton(
                        //           onPressed: () async {
                        //             await invoices.fetchInvoices(
                        //               userId: singleUser.uid,
                        //             );
                        //             final allInvoices = invoices.invoices;
                        //             bool ableToDelete = true;
                        //             for (var element in allInvoices) {
                        //               if (singleUser.uid == element.mandobId) {
                        //                 ableToDelete = false;
                        //               }
                        //             }
                        //             if (ableToDelete) {
                        //               Provider.of<UserProvider>(context,
                        //                       listen: false)
                        //                   .deleteUser(singleUser);
                        //               Navigator.pop(context);
                        //             } else {
                        //               Navigator.pop(context);
                        //
                        //               showDialog(
                        //                 context: context,
                        //                 builder: (context) => AlertDialog(
                        //                   content: Text(isEng
                        //                       ? "You cannot delete this representative because it is linked to other operations"
                        //                       : "لا يمكنك حذف هذا المندوب لارتباطه بعمليات اخرى"),
                        //                 ),
                        //               );
                        //             }
                        //           },
                        //           child: Text(isEng ? "Agree" : "اوافق"));
                        //     })
                        //   ],
                        // ),
                      ),
                      child: const Material(
                        elevation: 2,
                        color: Colors.red,
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(15),
                            topRight: Radius.circular(15)),
                        child: Center(
                          child: Padding(
                            padding: EdgeInsets.all(5.0),
                            child: Icon(
                              Icons.delete,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ]),
      ),
    );
  }
}

class PasswordWidget extends StatefulWidget {
  final String pass;

  const PasswordWidget({Key? key, required this.pass}) : super(key: key);

  @override
  State<PasswordWidget> createState() => _PasswordWidgetState();
}

class _PasswordWidgetState extends State<PasswordWidget> {
  bool _passHide = true;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          _passHide ? "***********" : widget.pass,
          style: const TextStyle(fontSize: 18),
        ),
        const SizedBox(
          width: 2,
        ),
        InkWell(
            onTap: () => setState(() {
                  _passHide = !_passHide;
                }),
            child: Icon(_passHide
                ? Icons.remove_red_eye
                : Icons.remove_red_eye_outlined)),
      ],
    );
  }
}
