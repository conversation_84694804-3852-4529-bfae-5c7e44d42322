import 'dart:convert';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mandob/appwrite_db/appwrite.dart';
import 'package:mandob/appwrite_db/db_consts.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/company_info_model.dart';
import 'package:mandob/pages/main_screen/main_screen.dart';
import 'package:mandob/providers/company_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/shared_preferences.dart';
import 'package:mandob/utils/show_bar/flush_bar.dart';
import 'package:provider/provider.dart';

import 'components/license_view.dart';
import 'components/login_view.dart';

class LoginPage extends HookWidget {
  final bool isLicenseView;

  const LoginPage({
    super.key,
    this.isLicenseView = true,
  });

  @override
  Widget build(BuildContext context) {
    final loading = useState<bool>(false);
    final formKey = useState<GlobalKey<FormState>>(GlobalKey());
    final loginFormKey = useState<GlobalKey<FormState>>(GlobalKey());
    final emailController =
        useTextEditingController(text: kDebugMode ? DbConsts.testUser : '');
    final passwordController =
        useTextEditingController(text: kDebugMode ? DbConsts.testPass : '');
    final databaseIdController =
        useTextEditingController(text: kDebugMode ? DbConsts.mandobDB : '');
    final isDatabaseIdField = useState<bool>(isLicenseView);
    final companyData = useState<CompanyInfoModel?>(
        loadString(key: DbConsts.companyDataPref) != null
            ? CompanyInfoModel.fromJson(
                jsonDecode(loadString(key: DbConsts.companyDataPref)!))
            : null);
    void saveDatabaseId() async {
      if (formKey.value.currentState!.validate()) {
        loading.value = true;
        await saveString(
            key: DbConsts.databaseIdPref, value: databaseIdController.text);
        AppwriteDB.init();

        companyData.value =
            await context.read<CompanyInfoProvider>().fetchCompanyInfo();

        await saveString(
            key: DbConsts.companyDataPref,
            value: jsonEncode(companyData.value?.toJson()));

        log('asfaf ${loadString(key: DbConsts.companyDataPref)}');

        formKey.value.currentState!.reset();
        isDatabaseIdField.value = false;
        loading.value = false;
      }
    }

    Future<void> login() async {
      if (!loginFormKey.value.currentState!.validate()) return;
      loginFormKey.value.currentState!.save();
      loading.value = true;

      try {
        await Provider.of<UserProvider>(context, listen: false).loginUser(
            email: emailController.text, password: passwordController.text);

        Navigator.pushReplacement(context,
            MaterialPageRoute(builder: (context) => const MainScreen()));
      } catch (e) {
        showBar(
            context,
            context.isEng
                ? 'An error occurred, please make sure the entered data is correct'
                : 'حدث خطأ، برجاء التأكد ان البيانات المدخلة صحيحه');
      }
      loading.value = false;
    }

    return Scaffold(
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 500),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(opacity: animation, child: child);
        },
        child: isDatabaseIdField.value
            ? LicenseView(
                key: const ValueKey('LicenseView'),
                formKey: formKey,
                databaseIdController: databaseIdController,
                isDatabaseIdField: isDatabaseIdField,
                loading: loading,
                saveDatabaseId: saveDatabaseId,
              )
            : LoginView(
                key: const ValueKey('LoginView'),
                isDatabaseIdField: isDatabaseIdField,
                formKey: loginFormKey,
                emailController: emailController,
                passwordController: passwordController,
                loading: loading,
                login: login,
                company: companyData.value,
              ),
      ),
    );
  }
}
