import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/core/shared/language/change_language.widget.dart';
import 'package:mandob/models/company_info_model.dart';
import 'package:mandob/pages/company_page/company_info_page.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:xr_helper/xr_helper.dart';

class LoginView extends HookWidget {
  final ValueNotifier<bool> isDatabaseIdField;
  final ValueNotifier<bool> loading;
  final TextEditingController emailController;
  final TextEditingController passwordController;
  final ValueNotifier<GlobalKey<FormState>> formKey;
  final VoidCallback login;
  final CompanyInfoModel? company;

  const LoginView({
    super.key,
    required this.isDatabaseIdField,
    required this.loading,
    required this.emailController,
    required this.passwordController,
    required this.formKey,
    required this.login,
    required this.company,
  });

  @override
  Widget build(BuildContext context) {
    final isKeyboardVisible =
        KeyboardVisibilityProvider.isKeyboardVisible(context);
    final width = isKeyboardVisible ? context.width * 0.2 : context.width * 0.5;
    final isEng = context.isEng;

    // Memoize the future to avoid rebuilding
    final futureLogo =
        useMemoized(() => getLogo(company?.logoUrl ?? ''), [company?.logoUrl]);

    return SafeArea(
      child: Scaffold(
        bottomNavigationBar: Container(
          width: context.width,
          decoration: const BoxDecoration(
            color: ColorManager.primaryColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(30),
              topRight: Radius.circular(30),
            ),
          ),
          child: Form(
            key: formKey.value,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  if (isKeyboardVisible) AppGaps.gap12 else AppGaps.gap24,
                  SizedBox(
                    width: context.width * 0.7,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            isEng ? 'Username' : 'اسم المستخدم',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          AppGaps.gap12,
                          TextFormField(
                            controller: emailController,
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                            decoration: InputDecoration(
                              hintText: isEng
                                  ? 'Enter username'
                                  : 'ادخل اسم المستخدم',
                              filled: true,
                              fillColor: ColorManager.fieldColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  AppGaps.gap12,
                  SizedBox(
                    width: context.width * 0.7,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isEng ? 'Password' : 'كلمة المرور',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        AppGaps.gap12,
                        TextFormField(
                          controller: passwordController,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                          decoration: InputDecoration(
                            hintText:
                                isEng ? 'Enter password' : 'ادخل كلمة المرور',
                            filled: true,
                            fillColor: ColorManager.fieldColor,
                          ),
                          obscureText: true,
                        ),
                      ],
                    ),
                  ),
                  AppGaps.gap24,
                  SizedBox(
                    width: context.width * 0.7,
                    child: Button(
                      color: ColorManager.secondaryColor,
                      isLoading: loading.value,
                      loadingWidget: const LoadingWidget(
                        color: ColorManager.secondaryColor,
                      ),
                      label: isEng ? 'Login' : 'تسجيل الدخول',
                      onPressed: login,
                    ),
                  ),
                  AppGaps.gap24,
                ],
              ),
            ),
          ),
        ),
        body: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () {
                    isDatabaseIdField.value = true;
                  },
                  child: Container(
                    padding: const EdgeInsets.all(25),
                    decoration: BoxDecoration(
                      color: ColorManager.brown,
                      borderRadius: context.isEng
                          ? const BorderRadius.only(
                              bottomRight: Radius.circular(500),
                              topRight: Radius.circular(500),
                            )
                          : const BorderRadius.only(
                              bottomLeft: Radius.circular(500),
                              topLeft: Radius.circular(500),
                            ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(
                          Icons.local_police,
                          size: 24,
                          color: Colors.white,
                        ),
                        AppGaps.gap8,
                        Padding(
                          padding: const EdgeInsets.only(bottom: 0),
                          child: Text(
                            isEng ? 'Change Activation' : 'تغير التفعيل',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () {
                    showChangeLanguageDialog(context);
                  },
                  icon: const Icon(CupertinoIcons.globe, size: 35),
                ),
              ],
            ),
            if (!isKeyboardVisible) AppGaps.gap48,
            if (company?.logoUrl != null &&
                company?.logoUrl?.isNotEmpty == true)
              FutureBuilder(
                future: futureLogo,
                builder: (context, snapshot) {
                  if (snapshot.hasData && company!.logoUrl != '') {
                    final Uint8List image = snapshot.data as Uint8List;
                    return ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        width: width,
                        child: Image.memory(
                          image,
                          fit: BoxFit.cover,
                        ),
                      ),
                    );
                  } else {
                    return AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      width: width,
                      child: Image.asset(
                        'assets/images/logo.png',
                      ),
                    );
                  }
                },
              )
            else
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: width,
                child: Image.asset(
                  'assets/images/logo.png',
                ),
              ),
            if (!isKeyboardVisible) AppGaps.gap48,
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                company?.companyName ??
                    (isEng ? 'Mandob App' : 'تطبيق المندوب'),
                style: TextStyle(
                  fontSize: isKeyboardVisible ? 20 : 32,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
