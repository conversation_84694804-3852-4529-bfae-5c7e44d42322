import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/core/shared/language/change_language.widget.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

class LicenseView extends HookWidget {
  final ValueNotifier<bool> loading;
  final TextEditingController databaseIdController;
  final ValueNotifier<bool> isDatabaseIdField;
  final ValueNotifier<GlobalKey<FormState>> formKey;
  final VoidCallback saveDatabaseId;

  const LicenseView({
    super.key,
    required this.loading,
    required this.databaseIdController,
    required this.isDatabaseIdField,
    required this.formKey,
    required this.saveDatabaseId,
  });

  @override
  Widget build(BuildContext context) {
    final isKeyboardVisible =
        KeyboardVisibilityProvider.isKeyboardVisible(context);
    final isEng = context.isEng;

    return SafeArea(
      child: Form(
        key: formKey.value,
        child: Scaffold(
          resizeToAvoidBottomInset: true,
          bottomNavigationBar: Container(
            width: context.width,
            decoration: const BoxDecoration(
              color: ColorManager.brown,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(30),
                topRight: Radius.circular(30),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isKeyboardVisible) AppGaps.gap12 else AppGaps.gap96,
                SizedBox(
                  width: context.width * 0.7,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isEng ? 'Activation Code' : 'كود التفعيل',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      AppGaps.gap12,
                      TextFormField(
                        validator: (value) {
                          if (value!.isEmpty) {
                            return isEng
                                ? 'Please enter the activation code'
                                : 'برجاء ادخال كود التفعيل';
                          }
                          return null;
                        },
                        controller: databaseIdController,
                        style: const TextStyle(
                          color: Colors.white,
                        ),
                        decoration: InputDecoration(
                          hintText: isEng
                              ? 'Enter the activation code'
                              : 'ادخل كود التفعيل',
                          hintStyle: const TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                AppGaps.gap48,
                SizedBox(
                  width: context.width * 0.7,
                  child: Button(
                    isLoading: loading.value,
                    color: ColorManager.primaryColor,
                    loadingWidget: const LoadingWidget(),
                    label: isEng ? 'Confirm' : 'تأكيد',
                    onPressed: saveDatabaseId,
                  ),
                ),
                AppGaps.gap48,
              ],
            ),
          ),
          body: SingleChildScrollView(
            physics: isKeyboardVisible
                ? const BouncingScrollPhysics()
                : const NeverScrollableScrollPhysics(),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        onPressed: () {
                          showDialog(
                            context: context,
                            builder: (context) {
                              return AlertDialog(
                                title:
                                    Text(isEng ? 'Contact Us' : 'تواصل معنا'),
                                content: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    IconButton(
                                      onPressed: () {
                                        launchUrl(
                                            Uri.parse('tel:+966502846260'));
                                      },
                                      icon: const Icon(Icons.call,
                                          color: ColorManager.secondaryColor,
                                          size: 35),
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        launchUrl(Uri.parse(
                                            'https://wa.me/+966502846260'));
                                      },
                                      icon: const Icon(
                                        FontAwesomeIcons.whatsapp,
                                        color: Colors.green,
                                        size: 35,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          );
                        },
                        icon: const Icon(
                          Icons.contact_support,
                          color: ColorManager.secondaryColor,
                          size: 35,
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          showChangeLanguageDialog(context);
                        },
                        icon: const Icon(CupertinoIcons.globe, size: 35),
                      ),
                    ],
                  ),
                  // AppGaps.gap48,
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    width: isKeyboardVisible
                        ? context.width * 0.3
                        : context.width * 0.7,
                    child: Image.asset('assets/images/logo.png'),
                  ),
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    height: isKeyboardVisible ? 12 : 48,
                  ),
                  Text(
                    isEng ? 'Activate the Program' : 'تفعيل البرنامج',
                    style: TextStyle(
                      fontSize: isKeyboardVisible ? 24 : 32,
                      fontWeight: FontWeight.bold,
                      color: ColorManager.lightBrown,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
