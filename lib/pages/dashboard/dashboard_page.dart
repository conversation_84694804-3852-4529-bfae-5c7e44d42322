import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/core/shared/drop_down_fields/delivery_drop_down.dart';
import 'package:mandob/pages/collections_page/collections_page.dart';
import 'package:mandob/pages/collections_page/components/add_collection_form.dart';
import 'package:mandob/pages/customer_page/components/add_customer_screen.dart';
import 'package:mandob/pages/customer_statement_page/customer_statement_page.dart';
import 'package:mandob/pages/expenses_page/components/add_expense_dialog.dart';
import 'package:mandob/pages/expenses_page/expenses_page.dart';
import 'package:mandob/pages/receipt_page/components/invoice_dialog/main_invoice_dialog.dart';
import 'package:mandob/pages/reports_page/reports_page.dart';
import 'package:mandob/providers/collections_provider.dart';
import 'package:mandob/providers/invoices_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../providers/customers_provider.dart';
import '../receipt_page/components/invoice_table/sales_table/main_sales_table.dart';

class DashboardPage extends HookWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    final activeUser = context.read<UserProvider>().activeUser;

    useEffect(() {
      context.read<CustomersProvider>().fetchAllCustomers();
      context.read<InvoicesProvider>().fetchTodayInvoices();

      return () {};
    }, []);

    return ListView(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          child: Text(
            context.isEng
                ? 'Hello, ${activeUser?.name ?? ''}'
                : 'مرحباً, ${activeUser?.name ?? ''}',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 10),
        if (activeUser?.isAdmin == true) ...[
          const DeliveryDropDownButton().paddingSymmetric(horizontal: 12),
          const SizedBox(height: 10),
        ],
        Consumer<InvoicesProvider>(
          builder: (context, invoicesProvider, child) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: ColorManager.primaryColor,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 12),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                CupertinoIcons.money_dollar_circle,
                                color: ColorManager.primaryColor,
                                size: 36,
                              ),
                              const SizedBox(width: 10),
                              Flexible(
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    context.isEng
                                        ? 'Today\'s Sales'
                                        : 'مبيعات اليوم',
                                    style: const TextStyle(
                                      color: ColorManager.primaryColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Text(
                            '${invoicesProvider.totalSales.toStringAsFixed(2)} ${context.isEng ? 'SAR' : 'ريال'}',
                            style: const TextStyle(
                              color: ColorManager.primaryColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  AppGaps.gap12,
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: ColorManager.errorColor,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 12),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                CupertinoIcons.cart_badge_plus,
                                color: ColorManager.errorColor,
                                size: 36,
                              ),
                              const SizedBox(width: 10),
                              Flexible(
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    context.isEng
                                        ? 'Today\'s Purchases'
                                        : 'مشتريات اليوم',
                                    style: const TextStyle(
                                      color: ColorManager.errorColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Text(
                            '${invoicesProvider.totalPurchases.toStringAsFixed(2)} ${context.isEng ? 'SAR' : 'ريال'}',
                            style: const TextStyle(
                              color: ColorManager.errorColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        const SizedBox(height: 20),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            context.isEng ? 'Daily Operations' : 'العمليات اليومية',
            style: const TextStyle(
              fontSize: 20,
            ),
          ),
        ),
        const SizedBox(height: 10),
        Container(
          alignment: Alignment.centerRight,
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Wrap(
            alignment: WrapAlignment.start,
            runSpacing: 10,
            spacing: 10,
            children: [
              DashboardCardWidget(
                title: context.isEng ? 'New Sales' : 'مبيعات جديدة',
                icon: CupertinoIcons.money_dollar_circle,
                onTap: () {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: false,
                    barrierLabel: MaterialLocalizations.of(context)
                        .modalBarrierDismissLabel,
                    pageBuilder: (context, _, __) => const ItemInvoiceDialog(
                      isReturned: false,
                      isPurchase: false,
                    ),
                  );
                },
              ),
              DashboardCardWidget(
                title: context.isEng ? 'New Return' : 'مرتجع جديد',
                icon: CupertinoIcons.arrow_turn_up_left,
                color: Colors.amber,
                onTap: () {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: false,
                    barrierLabel: MaterialLocalizations.of(context)
                        .modalBarrierDismissLabel,
                    pageBuilder: (context, _, __) => const SalesInvoicesTable(
                      isReturned: false,
                      title: 'مرتجع جديد',
                    ),
                  );
                },
              ),
              DashboardCardWidget(
                title: context.isEng ? 'New Purchases' : 'مشتريات جديدة',
                icon: CupertinoIcons.cart_badge_plus,
                color: Colors.cyan,
                onTap: () {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: false,
                    barrierLabel: MaterialLocalizations.of(context)
                        .modalBarrierDismissLabel,
                    pageBuilder: (context, _, __) => const ItemInvoiceDialog(
                      isReturned: false,
                      isPurchase: true,
                    ),
                  );
                },
              ),
              DashboardCardWidget(
                title: context.isEng ? 'New Collection' : 'تحصيل جديد',
                icon: CupertinoIcons.money_dollar_circle,
                color: Colors.purple,
                onTap: () {
                  final collectionsP =
                      Provider.of<CollectionsProvider>(context, listen: false);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => const AddCollectionForm(),
                    ),
                  );
                },
              ),
              DashboardCardWidget(
                title: context.isEng ? 'New Expense' : 'مصروف جديد',
                icon: CupertinoIcons.money_dollar_circle,
                color: Colors.red,
                onTap: () {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: false,
                    barrierLabel: MaterialLocalizations.of(context)
                        .modalBarrierDismissLabel,
                    pageBuilder: (context, _, __) => const AddExpensesDialog(),
                  );
                },
              ),
              DashboardCardWidget(
                title: context.isEng ? 'New Customer' : 'عميل جديد',
                icon: CupertinoIcons.person_badge_plus,
                color: Colors.blue,
                onTap: () {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: false,
                    barrierLabel: MaterialLocalizations.of(context)
                        .modalBarrierDismissLabel,
                    pageBuilder: (context, _, __) => const AddCustomerScreen(
                      fromInvoiceDialog: true,
                    ),
                  );
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            context.isEng ? 'Reports' : 'التقارير',
            style: const TextStyle(
              fontSize: 20,
            ),
          ),
        ),
        const SizedBox(height: 10),
        Container(
          alignment: Alignment.centerRight,
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Wrap(
            alignment: WrapAlignment.start,
            runSpacing: 10,
            spacing: 10,
            children: [
              DashboardCardWidget(
                title: context.isEng ? 'Sales Report' : 'تقرير مبيعات',
                icon: CupertinoIcons.doc_chart,
                color: Colors.redAccent,
                onTap: () {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: false,
                    barrierLabel: MaterialLocalizations.of(context)
                        .modalBarrierDismissLabel,
                    pageBuilder: (context, _, __) => const ReportsPage(
                      isSales: true,
                    ),
                  );
                },
              ),
              DashboardCardWidget(
                title: context.isEng ? 'Purchase Report' : 'تقرير مشتريات',
                icon: CupertinoIcons.doc_chart_fill,
                color: Colors.blueGrey,
                onTap: () {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: false,
                    barrierLabel: MaterialLocalizations.of(context)
                        .modalBarrierDismissLabel,
                    pageBuilder: (context, _, __) => const ReportsPage(
                      isSales: false,
                    ),
                  );
                },
              ),
              DashboardCardWidget(
                title: context.isEng ? 'Customer Statements' : 'كشف حساب عملاء',
                icon: CupertinoIcons.doc_on_clipboard,
                color: Colors.blue.shade700,
                onTap: () {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: false,
                    barrierLabel: MaterialLocalizations.of(context)
                        .modalBarrierDismissLabel,
                    pageBuilder: (context, _, __) =>
                        const CustomerPageStatement(),
                  );
                },
              ),
              DashboardCardWidget(
                title:
                    context.isEng ? 'Supplier Statements' : 'كشف حساب موردين',
                icon: CupertinoIcons.doc_on_clipboard,
                color: Colors.deepPurpleAccent,
                onTap: () {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: false,
                    barrierLabel: MaterialLocalizations.of(context)
                        .modalBarrierDismissLabel,
                    pageBuilder: (context, _, __) =>
                        const CustomerPageStatement(
                      isPurchase: true,
                    ),
                  );
                },
              ),
              DashboardCardWidget(
                title: context.isEng ? 'Collections' : 'التحصيلات',
                icon: CupertinoIcons.money_dollar_circle,
                color: Colors.lightBlue,
                onTap: () {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: false,
                    barrierLabel: MaterialLocalizations.of(context)
                        .modalBarrierDismissLabel,
                    pageBuilder: (context, _, __) => const CollectionsPage(),
                  );
                },
              ),
              DashboardCardWidget(
                title: context.isEng ? 'Expenses' : 'المصروفات',
                icon: CupertinoIcons.money_dollar_circle,
                color: Colors.deepOrangeAccent,
                onTap: () {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: false,
                    barrierLabel: MaterialLocalizations.of(context)
                        .modalBarrierDismissLabel,
                    pageBuilder: (context, _, __) => ExpensesPage(),
                  );
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
      ],
    );
  }
}

class DashboardCardWidget extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color color;
  final GestureTapCallback? onTap;

  const DashboardCardWidget({
    super.key,
    required this.title,
    required this.icon,
    this.onTap,
    this.color = ColorManager.primaryColor,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: MediaQuery.sizeOf(context).width / 3 - 15,
        padding: const EdgeInsets.symmetric(horizontal: 6),
        height: 120,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 36,
            ),
            const SizedBox(height: 10),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
