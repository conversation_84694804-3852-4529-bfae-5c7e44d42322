import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import '../../../theme.dart';

class TopSupplierStatmentSection extends StatelessWidget {
  final bool isInvoice;
  final bool isReturn;
  const TopSupplierStatmentSection(
      {super.key, required this.isInvoice, required this.isReturn});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Table(
          columnWidths: !isInvoice || isReturn
              ? {
            0: const FlexColumnWidth(0.7),
            1: const FlexColumnWidth(2),
            2: const FlexColumnWidth(1.2),
            3: const FlexColumnWidth(1),
          }
              : {
            0: const FlexColumnWidth(0.8),
            1: const FlexColumnWidth(1),
            2: const FlexColumnWidth(0.8),
            3: const FlexColumnWidth(1.3),
            4: const FlexColumnWidth(0.7),
          },
          children: [
            TableRow(
              children: [
                Text(
                  context.isEng ? 'Date' : 'التاريخ',
                  style: Them.tableHeader,
                  textAlign: TextAlign.center,
                ),
                Text(
                  context.isEng ? 'Supplier Name' : 'اسم المورد',
                  style: Them.tableHeader,
                  textAlign: TextAlign.center,
                ),
                if (isInvoice && !isReturn) ...[
                  Text(
                    context.isEng ? 'Remaining' : 'المتبقي',
                    style: Them.tableHeader,
                    textAlign: TextAlign.center,
                  ),
                ],
                Text(
                  context.isEng ? 'Amount' : 'المبلغ',
                  style: Them.tableHeader,
                  textAlign: TextAlign.center,
                ),
                Text(
                  context.isEng ? 'Payment Method' : 'طريقة الدفع',
                  style: Them.tableHeader,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ]),
    );
  }
}

class InvoiceTableWidget extends StatelessWidget {
  final List data;
  final bool isSupplierPayment;
  final bool isReturn;

  const InvoiceTableWidget(
      {super.key,
        required this.data,
        required this.isSupplierPayment,
        required this.isReturn});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        child: Table(
            defaultVerticalAlignment: TableCellVerticalAlignment.middle,
            columnWidths: isSupplierPayment
                ? {
              0: const FlexColumnWidth(0.6),
              1: const FlexColumnWidth(1),
              2: const FlexColumnWidth(1.2),
              3: const FlexColumnWidth(0.5),
            }
                : isReturn
                ? {
              0: const FlexColumnWidth(1.6),
              1: const FlexColumnWidth(1.2),
              2: const FlexColumnWidth(0.4),
              3: const FlexColumnWidth(2),
            }
                : {
              0: const FlexColumnWidth(1.2),
              1: const FlexColumnWidth(1),
              2: const FlexColumnWidth(1),
              3: const FlexColumnWidth(1.8),
              4: const FlexColumnWidth(0.7),
            },
            children: data
                .map((state) => TableRow(children: [
              Text(
                state.createdAt == null
                    ? ''
                    : state.createdAt!.toString().split(" ").first,
                style: Them.tableCell,
                overflow: TextOverflow.ellipsis,
              ),
              if (isSupplierPayment) ...[
                Text(
                  state.supplierName == null ? '' : state.supplierName!,
                  style: Them.tableCell
                      .copyWith(fontWeight: FontWeight.normal),
                  textAlign: TextAlign.center,
                ),
                Text(
                  state.value == null
                      ? ''
                      : state.value!.toStringAsFixed(2),
                  style: Them.tableCell,
                  textAlign: TextAlign.center,
                ),
                Text(
                  context.isEng ? 'Cash' : 'نقداََ',
                  style: Them.tableCell,
                  overflow: TextOverflow.ellipsis,
                )
              ] else ...[
                Text(
                  state.customerName == null ? '' : state.customerName!,
                  style: Them.tableCell
                      .copyWith(fontWeight: FontWeight.normal),
                  textAlign: TextAlign.center,
                ),
                isReturn
                    ? Container()
                    : Text(
                  state.totalPrice == null ||
                      state.paidCash == null
                      ? ''
                      : double.tryParse(
                      (state.totalPrice - state.paidCash!)
                          .toString())!
                      .toStringAsFixed(2),
                  style: Them.tableCell,
                  textAlign: TextAlign.center,
                ),
                Text(
                  state.totalPrice == null
                      ? ''
                      : state.totalPrice!.toStringAsFixed(2),
                  style: Them.tableCell,
                  textAlign: TextAlign.center,
                ),
                state.invoiceType == null
                    ? Text(
                  context.isEng ? 'Cash' : 'نقداََ',
                  style: Them.tableCell,
                  overflow: TextOverflow.ellipsis,
                )
                    : state.invoiceType == 'cash' || isReturn
                    ? Text(
                  context.isEng ? 'Cash' : 'نقداََ',
                  style: Them.tableCell,
                  overflow: TextOverflow.ellipsis,
                )
                    : Text(
                  context.isEng ? 'Credit' : 'آجل',
                  style: Them.tableCell,
                  overflow: TextOverflow.ellipsis,
                )
              ]
            ]))
                .toList()));
  }
}