import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/invoice_model.dart';
import 'package:mandob/models/supplier_model.dart';
import 'package:mandob/models/supplier_payment_model.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/providers/reports_provider.dart';
import 'package:mandob/providers/supplier_payments_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/widgets/date_widget.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../core/shared/drop_down_fields/suppliers_drop_down.dart';
import '../../utils/app_bar.dart';
import '../../utils/loading_widget.dart';
import 'components/widgets.dart';

class SupplierStatementPage extends StatefulWidget {
  @override
  _SupplierStatementPageState createState() => _SupplierStatementPageState();
}

class _SupplierStatementPageState extends State<SupplierStatementPage> {
  String query = "";
  var sDate = ValueNotifier(DateTime.now());
  var eDate = ValueNotifier(DateTime.now());

  var totalSalesWithoutPaid = 0.0;
  var totalSales = 0.0;
  var totalReturns = 0.0;
  var totalSuppliersCollections = 0.0;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void onSelectionChanged() {
    setState(() {});
  }

  int? expenseTypeId;

  late List<SupplierModel> suppliersList;

  SupplierModel? supplier;

  String mandob = '';

  late UserModel mandobData;

  List<InvoiceModel> filteredSalesInvoices = [];
  List<InvoiceModel> filteredReturnsInvoices = [];
  List<SupplierPaymentModel> filteredSupplierPayments = [];

  int index = 0;

  double allPrice = 0.0;

  @override
  Widget build(BuildContext context) {
    return Consumer<ReportsProvider>(
      builder: (context, invoicesData, child) {
        Future<void> fetch() async {
          if (supplier == null) return;

          if (index == 0) {
            filteredSalesInvoices = await invoicesData.fetchReportInvoices(
              isSales: false,
              isReturned: index == 1,
              notifyListener: false,
              startDate: sDate.value,
              endDate: eDate.value,
              customerId: supplier?.id,
            );
          } else if (index == 1) {
            filteredReturnsInvoices = await invoicesData.fetchReportInvoices(
              isSales: false,
              isReturned: index == 1,
              notifyListener: false,
              startDate: sDate.value,
              endDate: eDate.value,
              customerId: supplier?.id,
            );
          } else if (index == 2) {
            filteredSupplierPayments =
                await Provider.of<SupplierPaymentsProvider>(context,
                        listen: false)
                    .fetchPayments(
              startDate: sDate.value,
              endDate: eDate.value,
              supplierId: supplier?.id,
            );
          }

          totalSales = 0.0;
          totalReturns = 0.0;
          totalSalesWithoutPaid = 0.0;
          totalSuppliersCollections = 0.0;
          allPrice = 0.0;

          for (var element in filteredSalesInvoices) {
            totalSales += (element.totalPrice! - element.paidCash!);
            totalSalesWithoutPaid += element.totalPrice!;
          }

          for (var element in filteredReturnsInvoices) {
            totalReturns += element.totalPrice!;
          }

          for (var element in filteredSupplierPayments) {
            totalSuppliersCollections += element.value!;
          }

          allPrice = totalSales - totalReturns + totalSuppliersCollections;
        }

        return Scaffold(
          resizeToAvoidBottomInset: false,
          body: SingleChildScrollView(
            child: Column(
              children: [
                appBarWidget(
                  context,
                  title: context.isEng
                      ? 'Supplier Statement'
                      : 'كشف حساب الموردين',
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 10.0, vertical: 8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SuppliersDropDownButton(
                        width: context.width,
                        supplier: supplier,
                        onChanged: (value) {
                          setState(() {
                            supplier = value;
                          });
                        },
                      ),
                      AppGaps.gap12,
                      UiWidgets.datePicker(
                        context,
                        onSelectionChanged,
                        startDate: sDate,
                        endDate: eDate,
                      )
                    ],
                  ),
                ),
                AppGaps.gap12,
                totalTables(),
                AppGaps.gap12,
                const Divider(
                  color: ColorManager.lightFieldColor,
                  thickness: 1,
                  height: 0,
                ).paddingSymmetric(horizontal: 10),
                AppGaps.gap12,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          index = 0;
                        });
                      },
                      child: Column(
                        children: [
                          Text(
                            context.isEng ? 'Purchases' : 'المشتريات',
                            style: TextStyle(
                                color: index == 0
                                    ? ColorManager.primaryColor
                                    : Colors.black,
                                fontSize: 18,
                                fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(
                            height: 3,
                          ),
                          index == 0
                              ? Container(
                                  width: 70,
                                  height: 3,
                                  decoration: BoxDecoration(
                                      color: index == 0
                                          ? ColorManager.primaryColor
                                          : Colors.black,
                                      borderRadius: BorderRadius.circular(50)),
                                )
                              : Container(),
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: () async {
                        setState(() {
                          index = 1;
                        });
                      },
                      child: Column(
                        children: [
                          Text(
                            context.isEng ? 'Returns' : 'المرتجعات',
                            style: TextStyle(
                                color: index == 1
                                    ? ColorManager.primaryColor
                                    : Colors.black87,
                                fontSize: 18,
                                fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(
                            height: 3,
                          ),
                          index == 1
                              ? Container(
                                  width: 75,
                                  height: 3,
                                  decoration: BoxDecoration(
                                      color: index == 1
                                          ? ColorManager.primaryColor
                                          : Colors.black87,
                                      borderRadius: BorderRadius.circular(50)),
                                )
                              : Container(),
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          index = 2;
                        });
                      },
                      child: Column(
                        children: [
                          Text(
                            context.isEng ? 'Payments' : 'السدادات',
                            style: TextStyle(
                                color: index == 2
                                    ? ColorManager.primaryColor
                                    : Colors.black87,
                                fontSize: 18,
                                fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(
                            height: 3,
                          ),
                          index == 2
                              ? Container(
                                  width: 75,
                                  height: 3,
                                  decoration: BoxDecoration(
                                      color: index == 2
                                          ? ColorManager.primaryColor
                                          : Colors.black,
                                      borderRadius: BorderRadius.circular(50)),
                                )
                              : Container(),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 15,
                ),
                FutureBuilder(
                    future: fetch(),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Center(child: LoadingWidget());
                      }

                      return Padding(
                        padding: const EdgeInsets.all(6.0),
                        child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 15.0),
                            child: Column(
                              children: [
                                TopSupplierStatmentSection(
                                  isReturn: index == 1 ? true : false,
                                  isInvoice: index == 2 ? false : true,
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: 10, bottom: 10),
                                  child: InvoiceTableWidget(
                                      isReturn: index == 1 ? true : false,
                                      data: index == 0
                                          ? filteredSalesInvoices
                                          : index == 1
                                              ? filteredReturnsInvoices
                                              : filteredSupplierPayments,
                                      isSupplierPayment:
                                          index == 2 ? true : false),
                                )
                              ],
                            )),
                      );
                    }),
                const SizedBox(
                  height: 15,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget totalTables() {
    if (supplier == null) return const SizedBox();

    if (index == 0) {
      return Column(
        children: [
          Text(
            context.isEng
                ? 'Total Remaining: ${totalSales.toStringAsFixed(2)}'
                : 'إجمالي المتبقي: ${totalSales.toStringAsFixed(2)}',
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          Text(
            context.isEng
                ? 'Total Amount: ${totalSalesWithoutPaid.toStringAsFixed(2)}'
                : 'إجمالي المبلغ: ${totalSalesWithoutPaid.toStringAsFixed(2)}',
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
        ],
      );
    } else if (index == 1) {
      return Text(
        context.isEng
            ? 'Total: ${totalReturns.toStringAsFixed(2)}'
            : 'الإجمالي: ${totalReturns.toStringAsFixed(2)}',
        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
      );
    } else if (index == 2) {
      return Text(
        context.isEng
            ? 'Total: ${totalSuppliersCollections.toStringAsFixed(2)}'
            : 'الإجمالي: ${totalSuppliersCollections.toStringAsFixed(2)}',
        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
      );
    }
    return const SizedBox();
  }
}
