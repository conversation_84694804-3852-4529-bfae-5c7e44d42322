import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseSwitchTabBar extends HookWidget {
  final List<String> tabs;
  final Function(int) onTabChange;

  const BaseSwitchTabBar({
    super.key,
    required this.tabs,
    required this.onTabChange,
  });

  @override
  Widget build(BuildContext context) {
    final selectedTab = useState<int>(0);
    return Center(
      child: DefaultTabController(
        initialIndex: selectedTab.value,
        length: 2,
        child: Container(
          height: 55,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.0),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                spreadRadius: 2,
                blurRadius: 5,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: TabBar(
            dividerHeight: 0,
            onTap: (index) {
              selectedTab.value = index;

              onTabChange(index);
            },
            tabAlignment: TabAlignment.start,
            isScrollable: true,
            padding: const EdgeInsets.all(AppSpaces.padding8),
            indicator: BoxDecoration(
              color: selectedTab.value == 0
                  ? ColorManager.primaryColor
                  : ColorManager.secondaryColor,
              borderRadius: BorderRadius.circular(10.0),
            ),
            labelColor: Colors.white,
            indicatorSize: TabBarIndicatorSize.tab,
            // unselectedLabelColor: Colors.black,
            // tabs: [
            //   Tab(text: context.tr.issue),
            //   Tab(text: context.tr.request),
            // ],
            tabs: tabs.map((e) => Tab(text: e)).toList(),
          ),
        ),
      ),
    );
  }
}
