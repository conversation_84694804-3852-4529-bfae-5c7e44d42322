import 'package:xr_helper/xr_helper.dart';

Future<bool> saveString({required String key, required String value}) async {
  try {
    return await GetStorageService.setData(key: key, value: value);
  } catch (e) {
    Log.e("save data error is $e");
    return false;
  }
}

String? loadString({required String key}) {
  try {
    return GetStorageService.getData(key: key);
  } catch (e) {
    Log.e("load data error is $e");
    return null;
  }
}

Future<bool> removeData({required String key}) async {
  try {
    return await GetStorageService.removeKey(key: key);
  } catch (e) {
    Log.e("remove data error is $e");
    return false;
  }
}
