import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';

import 'color_manager.dart';

PreferredSizeWidget appBarWidget(
  BuildContext context, {
  required final String title,
  final subtitle,
  final Widget? action,
  final onTap,
}) {
  return AppBar(
    title: Directionality(
      textDirection: context.isEng ? TextDirection.ltr : TextDirection.rtl,
      child: Stack(
        alignment: Alignment.center,
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Align(
              alignment:
                  context.isEng ? Alignment.centerLeft : Alignment.centerRight,
              child: const CircleAvatar(
                backgroundColor: ColorManager.primaryColor,
                radius: 20,
                child: Icon(
                  Icons.arrow_back_ios_outlined,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          subtitle == null
              ? const SizedBox()
              : Align(
                  alignment: context.isEng
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  child: TextButton(
                      onPressed: onTap,
                      child: Text(
                        subtitle,
                        style: const TextStyle(
                            color: ColorManager.secondaryColor,
                            fontWeight: FontWeight.bold),
                      )),
                ),
          Center(
            child: Text(
              title,
              style: const TextStyle(
                  color: Colors.black,
                  fontSize: 20,
                  fontWeight: FontWeight.bold),
            ),
          ),
          if (action != null)
            Positioned(
                left: context.isEng ? null : 0,
                right: context.isEng ? 0 : null,
                child: action!),
        ],
      ),
    ),
    automaticallyImplyLeading: false,
    backgroundColor: Colors.transparent,
    elevation: 0,
  );
}
