// import 'package:adobe_xd/pinned.dart';
// import 'package:flutter/material.dart';
//
// class GradientBackground extends StatelessWidget {
//   const GradientBackground({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return const SizedBox.shrink();
//     return Stack(
//       children: [
//         Pinned.fromPins(
//           Pin(size: 99.0, start: 24.0),
//           Pin(size: 100.0, middle: 0.1831),
//           child: Container(
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(72.0),
//               gradient: const LinearGradient(
//                 begin: Alignment(0.87, 0.0),
//                 end: Alignment(-1.0, 0.0),
//                 colors: [Color(0x0006c4f1), Color(0x30349fb9)],
//                 stops: [0.0, 1.0],
//               ),
//             ),
//           ),
//         ),
//         Pinned.fromPins(
//           Pin(size: 35.9, middle: 0.6428),
//           Pin(size: 36.2, middle: 0.7821),
//           child: Transform.rotate(
//             angle: 3.159,
//             child: Container(
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(72.0),
//                 gradient: const LinearGradient(
//                   begin: Alignment(0.87, 0.0),
//                   end: Alignment(-1.0, 0.0),
//                   colors: [Color(0x0006c4f1), Color(0x30349fb9)],
//                   stops: [0.0, 1.0],
//                 ),
//               ),
//             ),
//           ),
//         ),
//         Pinned.fromPins(
//           Pin(size: 51.0, end: 49.5),
//           Pin(size: 51.6, middle: 0.258),
//           child: Transform.rotate(
//             angle: 3.159,
//             child: Container(
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(72.0),
//                 gradient: const LinearGradient(
//                   begin: Alignment(0.87, 0.0),
//                   end: Alignment(-1.0, 0.0),
//                   colors: [Color(0x0006c4f1), Color(0x30349fb9)],
//                   stops: [0.0, 1.0],
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }
