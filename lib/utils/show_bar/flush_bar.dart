import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/material.dart';

Flushbar showBar(
  BuildContext context,
  String msg, {
  Color backgroundColor = Colors.redAccent,
  Color iconColor = Colors.white,
  Color indicatorColor = Colors.red,
  IconData icon = Icons.info_outline,
}) {
  return Flushbar(
    backgroundColor: backgroundColor,
    message: msg,
    icon: Icon(icon, size: 28.0, color: iconColor),
    duration: const Duration(seconds: 4),
    leftBarIndicatorColor: indicatorColor,
    flushbarStyle: FlushbarStyle.FLOATING,
    flushbarPosition: FlushbarPosition.TOP,
    margin: const EdgeInsets.all(16),
    borderRadius: BorderRadius.circular(10),
  )..show(context);
}
