import 'package:flutter/material.dart';

import '../../pages/repositories_page/transfer_repository_items/transfer_table/main_transfer_table.dart';
import '../color_manager.dart';
import 'flush_bar.dart';

void showDoneMessageAndClose(BuildContext context, [bool isEdit = false]) {
  Navigator.pop(context);

  showBar(context, isEdit ? 'تمت التعديل بنجاح' : 'تمت الإضافة بنجاح',
      backgroundColor: ColorManager.primaryColor,
      indicatorColor: ColorManager.secondaryColor,
      icon: Icons.done_all);
}

void showErrorMessageAndClose(BuildContext context) {
  showBar(context, 'حدث خطأ ما',
      backgroundColor: Colors.red,
      indicatorColor: Colors.redAccent,
      icon: Icons.error);
}

void showDoneInvoiceMessageAndClose(context) {
  addedItems.clear();
  addedMap.clear();

  Navigator.pop(context);

  showBar(context, 'تم عمل الفاتورة بنجاح',
      backgroundColor: ColorManager.primaryColor,
      indicatorColor: ColorManager.secondaryColor,
      icon: Icons.done_all);
}

void showInvoiceErrorMessage(BuildContext context) {
  Navigator.pop(context);

  showBar(
    context,
    'حدث خطأ أثناء عمل الفاتورة',
  );
}
