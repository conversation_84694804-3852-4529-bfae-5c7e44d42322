import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/utils/color_manager.dart';

class TextFieldWidget extends StatelessWidget {
  final focusNode;
  final controller;
  final TextInputType textInputType;
  final onChanged;
  final initialValue;
  final textAlign;
  final contentPadding;
  final bool enabled;
  final bool readOnly;
  final bool withoutEnter;
  final bool required;
  final String label;
  final String? hint;
  final Color labelColor;
  final int? maxLength;
  final String? Function(String?)? validator;

  const TextFieldWidget(
      {super.key,
      this.focusNode,
      this.controller,
      required this.label,
      this.hint,
      this.onChanged,
      this.validator,
      this.enabled = true,
      this.withoutEnter = false,
      this.readOnly = false,
      this.required = true,
      this.initialValue,
      this.maxLength,
      this.textAlign = TextAlign.right,
      this.contentPadding,
      this.labelColor = ColorManager.primaryColor,
      this.textInputType = TextInputType.text});

  @override
  Widget build(BuildContext context) {
    final isEng = context.isEng;

    return TextFormField(
      focusNode: focusNode,
      controller: controller,
      keyboardType: textInputType,
      initialValue: initialValue,
      maxLength: maxLength,
      textAlign: isEng ? TextAlign.left : textAlign,
      onChanged: onChanged,
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(
          fontSize: 13,
          color: labelColor,
        ),
        border: const OutlineInputBorder(),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.grey,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: labelColor,
          ),
        ),
        hintText: withoutEnter
            ? hint
            : '${isEng ? 'Enter' : 'أدخل'} ${hint ?? label}',
        hintStyle: const TextStyle(
          fontSize: 16,
          color: Colors.grey,
        ),
        contentPadding: contentPadding,
      ),
      enabled: enabled,
      readOnly:readOnly,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: !required
          ? null
          : validator ??
              (String? value) {
                if (value!.isEmpty) {
                  return "${isEng ? 'Please enter' : 'من فضلك أدخل'} $label";
                }
                return null;
              },
    );
  }
}
