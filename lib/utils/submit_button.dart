import 'package:flutter/material.dart';
import 'package:mandob/utils/color_manager.dart';

class SubmitButton extends StatelessWidget {
  final String label;
  final void Function()? onPressed;
  final double? width;
  final Color color;

  const SubmitButton(
      {Key? key,
      this.label = 'إضافة',
      required this.onPressed,
      this.width,
      this.color = ColorManager.primaryColor})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        alignment: Alignment.center,
        height: 45,
        width: width ?? MediaQuery.of(context).size.width / 2,
        margin: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom != 0 ? 260 : 0.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: color,
          // const Color(0xFF186fc9)
        ),
        child: Text(
          label,
          style: const TextStyle(
              fontSize: 16, fontWeight: FontWeight.w500, color: Colors.white),
        ),
      ),
    );
  }
}
