import 'dart:io';
import 'dart:math';

// import 'package:drago_blue_printer/drago_blue_printer.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

Future<void> writeToFile(ByteData data, String path) {
  final buffer = data.buffer;
  return File(path)
      .writeAsBytes(buffer.asUint8List(data.offsetInBytes, data.lengthInBytes));
}

Future saveImage(Uint8List bytes) async {
  final directory = await getApplicationDocumentsDirectory();
  final random = Random().nextInt(10000);

  final image = File('${directory.path}/image$random.png');

  final buffer = bytes.buffer;

  await image.writeAsBytes(
    buffer.asUint8List(
      bytes.offsetInBytes,
      bytes.lengthInBytes,
    ),
  );

  return image.path;
}

// class PrintInvoiceService {
//   DragoBluePrinter bluetooth = DragoBluePrinter.instance;
//
//   Future printImageInvoice(String pathImage) async {
//     Future<void> print() async {
//       await bluetooth.printImage(pathImage);
//       await bluetooth.printNewLine();
//       await bluetooth.printNewLine();
//       await bluetooth.printNewLine();
//       await bluetooth.paperCut();
//     }
//
//     bluetooth.isConnected.then((isConnected) {
//       if (isConnected ?? false) {
//         print();
//       } else {
//         //? connect
//         final savedDevice = GetStorageService.getData(key: LocalKeys.printer);
//
//         Log.i('savedDevice: $savedDevice');
//
//         if (savedDevice == null) {
//           return;
//         }
//
//         final bluetoothDevice = BluetoothDevice.fromMap(savedDevice ?? {});
//
//         Log.i('bluetoothDevice ${bluetoothDevice.toMap()}');
//
//         bluetooth.connect(bluetoothDevice).then((value) {
//           bluetooth.isConnected.then((isConnected) {
//             if (isConnected ?? false) {
//               print();
//             }
//           });
//         }).catchError((error) {});
//       }
//     });
//   }
//
//   Future<void> printInvoice(
//     BuildContext context, {
//     required InvoiceModel receiptData,
//     required CompanyInfoModel info,
//     required bool isEnglish,
//     bool isArabic2 = false,
//   }) async {
//     bluetooth.isConnected.then((isConnected) {
//       if (isConnected ?? false) {
//         if (isArabic2) {
//           _printArabicInvoice2(receiptData, info, context);
//         } else {
//           if (isEnglish) {
//             _printEnglishInvoice(receiptData, info, context);
//           } else {
//             _printArabicInvoice(receiptData, info, context);
//           }
//         }
//       } else {
//         //? connect
//         final savedDevice = GetStorageService.getData(key: LocalKeys.printer);
//
//         Log.i('savedDevice: $savedDevice');
//
//         if (savedDevice == null) {
//           return;
//         }
//
//         final bluetoothDevice = BluetoothDevice.fromMap(savedDevice ?? {});
//
//         Log.i('bluetoothDevice ${bluetoothDevice.toMap()}');
//
//         bluetooth.connect(bluetoothDevice).then((value) {
//           bluetooth.isConnected.then((isConnected) {
//             if (isConnected ?? false) {
//               if (isArabic2) {
//                 _printArabicInvoice2(receiptData, info, context);
//               } else {
//                 if (isEnglish) {
//                   _printEnglishInvoice(receiptData, info, context);
//                 } else {
//                   _printArabicInvoice(receiptData, info, context);
//                 }
//               }
//             }
//           });
//         }).catchError((error) {});
//       }
//     });
//   }
//
//   Future<void> _printEnglishInvoice(InvoiceModel receiptData,
//       CompanyInfoModel info, BuildContext context) async {
//     await bluetooth.printCustom("فاتورة", 2, 1, charset: "UTF-8");
//     await bluetooth.printCustom("الشركة: ${info.companyName}", 1, 2,
//         charset: "UTF-8");
//     await bluetooth.printCustom("الرقم الضريبي: ${info.vatNumber}", 1, 2,
//         charset: "UTF-8");
//     await bluetooth.printCustom("العنوان: ${info.address}", 1, 2,
//         charset: "UTF-8");
//     await bluetooth.printNewLine();
//     await bluetooth.printCustom(
//       "رقم الفاتورة: ${receiptData.invoiceNumber ?? receiptData.invoiceId.toString().substring(0, 7)}",
//       1,
//       2,
//       charset: "UTF-8",
//     );
//     await bluetooth.printCustom(
//         "التاريخ: ${DateFormat("dd-MM-yyyy hh:mm:ss").format(DateTime.parse(receiptData.createdAt!))}",
//         1,
//         2,
//         charset: "UTF-8");
//     await bluetooth.printCustom("العميل: ${receiptData.customerName}", 1, 2,
//         charset: "UTF-8");
//     await bluetooth.printCustom(
//         "الرقم الضريبي للعميل: ${receiptData.customerVatNumber ?? ''}", 1, 2,
//         charset: "UTF-8");
//     await bluetooth.printNewLine();
//     await bluetooth.printCustom("المنتجات:", 1, 2, charset: "UTF-8");
//     for (var product in receiptData.products!) {
//       final num total = product!["price"] * product["quantity"];
//       await bluetooth.printCustom(
//           "${product["name"]} - ${product["quantity"]} x ${product["price"].toStringAsFixed(2)} = ${total.toStringAsFixed(2)}",
//           1,
//           2,
//           charset: "UTF-8");
//     }
//     await bluetooth.printNewLine();
//     await bluetooth.printCustom(
//         "المبلغ قبل الضريبة: ${receiptData.totalWithoutTax!.toStringAsFixed(2)}${context.currency}",
//         1,
//         2,
//         charset: "UTF-8");
//     await bluetooth.printCustom(
//         "الخصم: ${receiptData.discount!.toStringAsFixed(2)}${context.currency}",
//         1,
//         2,
//         charset: "UTF-8");
//     await bluetooth.printCustom(
//         "المبلغ بعد الضريبة: ${receiptData.totalPrice!.toStringAsFixed(2)}${context.currency}",
//         1,
//         2,
//         charset: "UTF-8");
//     await bluetooth.printCustom(
//         "مدفوع نقدا: ${receiptData.paidCash!.toStringAsFixed(2)}${context.currency}",
//         1,
//         2,
//         charset: "UTF-8");
//     if (receiptData.totalPrice != receiptData.paidCash) {
//       await bluetooth.printCustom(
//           "آجل: ${(receiptData.totalPrice! - receiptData.paidCash!).toStringAsFixed(2)}${context.currency}",
//           1,
//           2,
//           charset: "UTF-8");
//     }
//     await bluetooth.printCustom(
//         "نوع الفاتورة: ${receiptData.invoiceType == 'cash' ? "نقدا" : "آجل"}",
//         1,
//         2,
//         charset: "UTF-8");
//
//     await bluetooth.printNewLine();
//     await bluetooth.printNewLine();
//     await bluetooth.printNewLine();
//
//     await bluetooth.paperCut();
//     // await bluetooth.printCustom("Invoice", 2, 1);
//     // await bluetooth.printCustom("Company: ${info.companyName}", 1, 0);
//     // await bluetooth.printCustom("VAT #: ${info.taxNumber}", 1, 0);
//     // await bluetooth.printCustom("Address: ${info.address}", 1, 0);
//     // await bluetooth.printNewLine();
//     // await bluetooth.printCustom(
//     //     "Invoice Number: ${receiptData.invoiceNumber ?? receiptData.invoiceId.toString().substring(0, 7)}",
//     //     1,
//     //     0);
//     // await bluetooth.printCustom(
//     //     "Date: ${DateFormat("dd-MM-yyyy hh:mm:ss").format(DateTime.parse(receiptData.createdAt!))}",
//     //     1,
//     //     0);
//     // await bluetooth.printCustom("Customer: ${receiptData.customerName}", 1, 0);
//     // await bluetooth.printCustom(
//     //     "Customer VAT #: ${receiptData.customerVatNumber ?? ''}", 1, 0);
//     // await bluetooth.printNewLine();
//     // await bluetooth.printCustom("Products:", 1, 0);
//     // for (var product in receiptData.products!) {
//     //   final num total = product!["price"] * product["quantity"];
//     //   await bluetooth.printCustom(
//     //       "${product["name"]} - ${product["quantity"]} x ${product["price"].toStringAsFixed(2)} = ${total.toStringAsFixed(2)}",
//     //       1,
//     //       0);
//     // }
//     // await bluetooth.printNewLine();
//     // await bluetooth.printCustom(
//     //     "Total Before Tax: ${receiptData.totalWithoutTax!.toStringAsFixed(2)}${context.currency}",
//     //     1,
//     //     0);
//     // await bluetooth.printCustom(
//     //     "Discount: ${receiptData.discount!.toStringAsFixed(2)}${context.currency}",
//     //     1,
//     //     0);
//     // await bluetooth.printCustom(
//     //     "Total After Tax: ${receiptData.totalPrice!.toStringAsFixed(2)}${context.currency}",
//     //     1,
//     //     0);
//     // await bluetooth.printCustom(
//     //     "Paid Cash: ${receiptData.paidCash!.toStringAsFixed(2)}${context.currency}",
//     //     1,
//     //     0);
//     // if (receiptData.totalPrice != receiptData.paidCash) {
//     //   await bluetooth.printCustom(
//     //       "Due: ${(receiptData.totalPrice! - receiptData.paidCash!).toStringAsFixed(2)}${context.currency}",
//     //       1,
//     //       0);
//     // }
//     // await bluetooth.printCustom(
//     //     "Invoice Type: ${receiptData.invoiceType == 'cash' ? "Cash" : "Credit"}",
//     //     1,
//     //     0);
//     //
//     // await bluetooth.printNewLine();
//     // await bluetooth.printNewLine();
//     // await bluetooth.printNewLine();
//     //
//     // await bluetooth.paperCut();
//   }
//
//   Future<void> _printArabicInvoice(InvoiceModel receiptData,
//       CompanyInfoModel info, BuildContext context) async {
//     await bluetooth.printCustom("فاتورة", 2, 1, charset: "windows-1256");
//     await bluetooth.printCustom("الشركة: ${info.companyName}", 1, 2,
//         charset: "windows-1256");
//     await bluetooth.printCustom("الرقم الضريبي: ${info.vatNumber}", 1, 2,
//         charset: "windows-1256");
//     await bluetooth.printCustom("العنوان: ${info.address}", 1, 2,
//         charset: "windows-1256");
//     await bluetooth.printNewLine();
//     await bluetooth.printCustom(
//       "رقم الفاتورة: ${receiptData.invoiceNumber ?? receiptData.invoiceId.toString().substring(0, 7)}",
//       1,
//       2,
//       charset: "windows-1256",
//     );
//     await bluetooth.printCustom(
//         "التاريخ: ${DateFormat("dd-MM-yyyy hh:mm:ss").format(DateTime.parse(receiptData.createdAt!))}",
//         1,
//         2,
//         charset: "windows-1256");
//     await bluetooth.printCustom("العميل: ${receiptData.customerName}", 1, 2,
//         charset: "windows-1256");
//     await bluetooth.printCustom(
//         "الرقم الضريبي للعميل: ${receiptData.customerVatNumber ?? ''}", 1, 2,
//         charset: "windows-1256");
//     await bluetooth.printNewLine();
//     await bluetooth.printCustom("المنتجات:", 1, 2, charset: "windows-1256");
//     for (var product in receiptData.products!) {
//       final num total = product!["price"] * product["quantity"];
//       await bluetooth.printCustom(
//           "${product["name"]} - ${product["quantity"]} x ${product["price"].toStringAsFixed(2)} = ${total.toStringAsFixed(2)}",
//           1,
//           2,
//           charset: "windows-1256");
//     }
//     await bluetooth.printNewLine();
//     await bluetooth.printCustom(
//         "المبلغ قبل الضريبة: ${receiptData.totalWithoutTax!.toStringAsFixed(2)}${context.currency}",
//         1,
//         2,
//         charset: "windows-1256");
//     await bluetooth.printCustom(
//         "الخصم: ${receiptData.discount!.toStringAsFixed(2)}${context.currency}",
//         1,
//         2,
//         charset: "windows-1256");
//     await bluetooth.printCustom(
//         "المبلغ بعد الضريبة: ${receiptData.totalPrice!.toStringAsFixed(2)}${context.currency}",
//         1,
//         2,
//         charset: "windows-1256");
//     await bluetooth.printCustom(
//         "مدفوع نقدا: ${receiptData.paidCash!.toStringAsFixed(2)}${context.currency}",
//         1,
//         2,
//         charset: "windows-1256");
//     if (receiptData.totalPrice != receiptData.paidCash) {
//       await bluetooth.printCustom(
//           "آجل: ${(receiptData.totalPrice! - receiptData.paidCash!).toStringAsFixed(2)}${context.currency}",
//           1,
//           2,
//           charset: "windows-1256");
//     }
//     await bluetooth.printCustom(
//         "نوع الفاتورة: ${receiptData.invoiceType == 'cash' ? "نقدا" : "آجل"}",
//         1,
//         2,
//         charset: "windows-1256");
//
//     await bluetooth.printNewLine();
//     await bluetooth.printNewLine();
//     await bluetooth.printNewLine();
//
//     await bluetooth.paperCut();
//   }
//
//   Future<void> _printArabicInvoice2(InvoiceModel receiptData,
//       CompanyInfoModel info, BuildContext context) async {
//     await bluetooth.printCustom(encodeArabic("فاتورة"), 2, 1, charset: "UTF-8");
//     await bluetooth.printCustom(
//         encodeArabic("الشركة: ${info.companyName}"), 1, 2,
//         charset: "UTF-8");
//     await bluetooth.printCustom(
//         encodeArabic("الرقم الضريبي: ${info.vatNumber}"), 1, 2,
//         charset: "UTF-8");
//     await bluetooth.printCustom(encodeArabic("العنوان: ${info.address}"), 1, 2,
//         charset: "UTF-8");
//     await bluetooth.printNewLine();
//     await bluetooth.printCustom(
//       encodeArabic(
//           "رقم الفاتورة: ${receiptData.invoiceNumber ?? receiptData.invoiceId.toString().substring(0, 7)}"),
//       1,
//       2,
//       charset: "UTF-8",
//     );
//     await bluetooth.printCustom(
//         encodeArabic(
//             "التاريخ: ${DateFormat("dd-MM-yyyy hh:mm:ss").format(DateTime.parse(receiptData.createdAt!))}"),
//         1,
//         2,
//         charset: "UTF-8");
//     await bluetooth.printCustom(
//         encodeArabic("العميل: ${receiptData.customerName}"), 1, 2,
//         charset: "UTF-8");
//     await bluetooth.printCustom(
//         encodeArabic(
//             "الرقم الضريبي للعميل: ${receiptData.customerVatNumber ?? ''}"),
//         1,
//         2,
//         charset: "UTF-8");
//     await bluetooth.printNewLine();
//     await bluetooth.printCustom(encodeArabic("المنتجات:"), 1, 2,
//         charset: "UTF-8");
//
//     for (var product in receiptData.products!) {
//       final num total = product!["price"] * product["quantity"];
//       await bluetooth.printCustom(
//           encodeArabic(
//               "${product["name"]} - ${product["quantity"]} x ${product["price"].toStringAsFixed(2)} = ${total.toStringAsFixed(2)}"),
//           1,
//           2,
//           charset: "UTF-8");
//     }
//
//     await bluetooth.printNewLine();
//     await bluetooth.printCustom(
//         encodeArabic(
//             "المبلغ قبل الضريبة: ${receiptData.totalWithoutTax!.toStringAsFixed(2)}${context.currency}"),
//         1,
//         2,
//         charset: "UTF-8");
//     await bluetooth.printCustom(
//         encodeArabic(
//             "الخصم: ${receiptData.discount!.toStringAsFixed(2)}${context.currency}"),
//         1,
//         2,
//         charset: "UTF-8");
//     await bluetooth.printCustom(
//         encodeArabic(
//             "المبلغ بعد الضريبة: ${receiptData.totalPrice!.toStringAsFixed(2)}${context.currency}"),
//         1,
//         2,
//         charset: "UTF-8");
//     await bluetooth.printCustom(
//         encodeArabic(
//             "مدفوع نقدا: ${receiptData.paidCash!.toStringAsFixed(2)}${context.currency}"),
//         1,
//         2,
//         charset: "UTF-8");
//
//     if (receiptData.totalPrice != receiptData.paidCash) {
//       await bluetooth.printCustom(
//           encodeArabic(
//               "آجل: ${(receiptData.totalPrice! - receiptData.paidCash!).toStringAsFixed(2)}${context.currency}"),
//           1,
//           2,
//           charset: "UTF-8");
//     }
//
//     await bluetooth.printCustom(
//         encodeArabic(
//             "نوع الفاتورة: ${receiptData.invoiceType == 'cash' ? "نقدا" : "آجل"}"),
//         1,
//         2,
//         charset: "UTF-8");
//
//     await bluetooth.printNewLine();
//     await bluetooth.printNewLine();
//     await bluetooth.printNewLine();
//
//     await bluetooth.paperCut();
//   }
//
//   String encodeArabic(String text) {
//     return utf8.decode(text.runes.toList());
//   }
// }
