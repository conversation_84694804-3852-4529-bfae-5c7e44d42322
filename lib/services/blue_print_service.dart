// import 'dart:convert';
//
// import 'package:blue_print_pos_arabic/receipt/receipt.dart';
// import 'package:blue_print_pos_arabic/blue_print_pos.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:intl/intl.dart';
// import 'package:mandob/core/extensions/context_extensions.dart';
// import 'package:mandob/models/company_info_model.dart';
// import 'package:mandob/models/invoice_model.dart';
//
// class BluePrintInvoiceService {
//   final BluePrintPos _bluePrintPos = BluePrintPos.instance;
//
//   Future<void> printImageInvoice(Uint8List imageBytes) async {
//     final ReceiptSectionText receiptText = ReceiptSectionText();
//     receiptText.addImage(
//       base64.encode(imageBytes),
//       width: 150,
//     );
//     receiptText.addSpacer(count: 3);
//     await _bluePrintPos.printReceiptText(receiptText);
//   }
//
//   Future<void> printInvoice(
//     BuildContext context, {
//     required InvoiceModel receiptData,
//     required CompanyInfoModel info,
//     required bool isEnglish,
//   }) async {
//     if (isEnglish) {
//       await _printEnglishInvoice(receiptData, info, context);
//     } else {
//       await _printArabicInvoice(receiptData, info, context);
//     }
//   }
//
//   Future<void> _printEnglishInvoice(InvoiceModel receiptData,
//       CompanyInfoModel info, BuildContext context) async {
//     final ReceiptSectionText receiptText = ReceiptSectionText();
//     receiptText.addText("Invoice",
//         size: ReceiptTextSizeType.large, style: ReceiptTextStyleType.bold);
//     receiptText.addText("Company: ${info.companyName}",
//         size: ReceiptTextSizeType.medium);
//     receiptText.addText("VAT #: ${info.taxNumber}",
//         size: ReceiptTextSizeType.medium);
//     receiptText.addText("Address: ${info.address}",
//         size: ReceiptTextSizeType.medium);
//     receiptText.addSpacer();
//     receiptText.addLeftRightText(
//         "Invoice Number:",
//         receiptData.invoiceNumber ??
//             receiptData.invoiceId.toString().substring(0, 7));
//     receiptText.addLeftRightText(
//         "Date:",
//         DateFormat("dd-MM-yyyy")
//             .format(DateTime.parse(receiptData.createdAt!)));
//     receiptText.addLeftRightText("Customer:", receiptData.customerName ?? '');
//     receiptText.addLeftRightText(
//         "Customer VAT #:", receiptData.customerVatNumber ?? '');
//     receiptText.addSpacer();
//     receiptText.addText("Products:", size: ReceiptTextSizeType.medium);
//     for (var product in receiptData.products!) {
//       final num total = product!["price"] * product["quantity"];
//       receiptText.addLeftRightText(
//         "${product["name"]} - ${product["quantity"]} x ${product["price"].toStringAsFixed(2)}",
//         total.toStringAsFixed(2),
//       );
//     }
//     receiptText.addSpacer();
//     receiptText.addLeftRightText("Total Before Tax:",
//         "${receiptData.totalWithoutTax!.toStringAsFixed(2)}${context.currency}");
//     receiptText.addLeftRightText("Discount:",
//         "${receiptData.discount!.toStringAsFixed(2)}${context.currency}");
//     receiptText.addLeftRightText("Total After Tax:",
//         "${receiptData.totalPrice!.toStringAsFixed(2)}${context.currency}");
//     receiptText.addLeftRightText("Paid Cash:",
//         "${receiptData.paidCash!.toStringAsFixed(2)}${context.currency}");
//     if (receiptData.totalPrice != receiptData.paidCash) {
//       receiptText.addLeftRightText("Due:",
//           "${(receiptData.totalPrice! - receiptData.paidCash!).toStringAsFixed(2)}${context.currency}");
//     }
//     receiptText.addLeftRightText(
//         "Invoice Type:", receiptData.invoiceType == 'cash' ? "Cash" : "Credit");
//     receiptText.addSpacer(count: 3);
//     await _bluePrintPos.printReceiptText(receiptText);
//     // await _bluePrintPos.paperCut();
//   }
//
//   Future<void> _printArabicInvoice(InvoiceModel receiptData,
//       CompanyInfoModel info, BuildContext context) async {
//     final ReceiptSectionText receiptText = ReceiptSectionText();
//     receiptText.addText(
//       "فاتورة",
//       size: ReceiptTextSizeType.large,
//       alignment: ReceiptAlignment.right,
//       style: ReceiptTextStyleType.bold,
//     );
//     receiptText.addText(
//       "الشركة: ${info.companyName}",
//       size: ReceiptTextSizeType.medium,
//       alignment: ReceiptAlignment.right,
//     );
//     receiptText.addText(
//       "الرقم الضريبي: ${info.taxNumber}",
//       size: ReceiptTextSizeType.medium,
//       alignment: ReceiptAlignment.right,
//     );
//     receiptText.addText(
//       "العنوان: ${info.address}",
//       size: ReceiptTextSizeType.medium,
//       alignment: ReceiptAlignment.right,
//     );
//     receiptText.addSpacer();
//     receiptText.addLeftRightText(
//       "رقم الفاتورة:",
//       receiptData.invoiceNumber ??
//           receiptData.invoiceId.toString().substring(0, 7),
//     );
//     receiptText.addLeftRightText(
//       "التاريخ:",
//       DateFormat("dd-MM-yyyy").format(DateTime.parse(receiptData.createdAt!)),
//     );
//     receiptText.addLeftRightText(
//       "العميل:",
//       receiptData.customerName ?? '',
//     );
//     receiptText.addLeftRightText(
//       "الرقم الضريبي للعميل:",
//       receiptData.customerVatNumber ?? '',
//     );
//     receiptText.addSpacer();
//     receiptText.addText(
//       "المنتجات:",
//       size: ReceiptTextSizeType.medium,
//     );
//     for (var product in receiptData.products!) {
//       final num total = product!["price"] * product["quantity"];
//       receiptText.addLeftRightText(
//         "${product["name"]} - ${product["quantity"]} x ${product["price"].toStringAsFixed(2)}",
//         total.toStringAsFixed(2),
//       );
//     }
//     receiptText.addSpacer();
//     receiptText.addLeftRightText(
//       "المبلغ قبل الضريبة:",
//       "${receiptData.totalWithoutTax!.toStringAsFixed(2)}${context.currency}",
//     );
//     receiptText.addLeftRightText(
//       "الخصم:",
//       "${receiptData.discount!.toStringAsFixed(2)}${context.currency}",
//     );
//     receiptText.addLeftRightText(
//       "المبلغ بعد الضريبة:",
//       "${receiptData.totalPrice!.toStringAsFixed(2)}${context.currency}",
//     );
//     receiptText.addLeftRightText(
//       "مدفوع نقدا:",
//       "${receiptData.paidCash!.toStringAsFixed(2)}${context.currency}",
//     );
//     if (receiptData.totalPrice != receiptData.paidCash) {
//       receiptText.addLeftRightText(
//         "آجل:",
//         "${(receiptData.totalPrice! - receiptData.paidCash!).toStringAsFixed(2)}${context.currency}",
//       );
//     }
//     receiptText.addLeftRightText(
//       "نوع الفاتورة:",
//       receiptData.invoiceType == 'cash' ? "نقدا" : "آجل",
//     );
//     receiptText.addSpacer(count: 3);
//     await _bluePrintPos.printReceiptText(receiptText);
//     // await _bluePrintPos.paperCut();
//   }
// }
