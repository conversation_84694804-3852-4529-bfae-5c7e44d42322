import 'package:flutter/material.dart';

import 'utils/color_manager.dart';

class Them {
  static TextStyle tableHeader = const TextStyle(
    fontWeight: FontWeight.w900,
    fontSize: 12,
  );
  static TextStyle tableCell =
      const TextStyle(fontWeight: FontWeight.bold, fontSize: 12);
  static TextStyle pageTitle = const TextStyle(
      color: Colors.black,
      fontSize: 22,
      fontWeight: FontWeight.bold,
      shadows: [
        Shadow(
            offset: Offset(0.0, 5),
            blurRadius: 4,
            color: ColorManager.primaryColor)
      ]);
  static TextStyle drawerItemText = const TextStyle(
    color: Colors.black,
    fontSize: 12,
    fontStyle: FontStyle.italic,
    fontWeight: FontWeight.w900,
  );
}
