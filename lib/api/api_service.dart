// import 'dart:async';
//
// import 'package:chopper/chopper.dart';
// import 'package:http/http.dart' as http;
// import 'package:http/retry.dart';
// import 'package:mandob/models/collection_model.dart';
// import 'package:mandob/models/company_info_model.dart';
// import 'package:mandob/models/customer_model.dart';
// import 'package:mandob/models/expense_model.dart';
// import 'package:mandob/models/invoice_model.dart';
// import 'package:mandob/models/operation_model.dart';
// import 'package:mandob/models/product_model.dart';
// import 'package:mandob/models/quantity_model.dart';
// import 'package:mandob/models/repository_model.dart';
// import 'package:mandob/models/supplier_model.dart';
// import 'package:mandob/models/supplier_payment_model.dart';
// import 'package:mandob/models/transition_model.dart';
// import 'package:mandob/models/user_model.dart';
//
// import 'utils.dart';
//
// part 'api_service.chopper.dart';
//
// @ChopperApi()
// abstract class ApiService extends ChopperService {
//   //User
//   // @Put(path: 'users/{id}.json')
//   // Future<Response<UserModel>> addUser(
//   //     @Body() Map<String, dynamic> body, @Path("id") String id);
//   //
//   // @Get(path: 'users/{id}.json')
//   // Future<Response<UserModel>> getUser(@Path("id") String id);
//   //
//   // @Get(path: 'users.json')
//   // Future<Response> getUsers();
//
//   // @Delete(path: 'users/{id}.json')
//   // Future<Response> deleteUser(@Path("id") String id);
//
//   //Products
//   // @Post(path: 'products.json')
//   // Future<Response<ProductModel>> addProduct(@Body() Map<String, dynamic> body);
//   //
//   // @Get(path: 'products.json')
//   // Future<Response> fetchProducts();
//
//   // @Patch(path: 'products/{id}.json')
//   // Future<Response> editProduct(
//   //     @Path("id") String id, @Body() Map<String, dynamic> body);
//   //
//   // @Delete(path: 'products/{id}.json')
//   // Future<Response> deleteProduct(@Path("id") String id);
//
//   //Invoices
//   // @Post(path: 'invoices.json')
//   // Future<Response<InvoiceModel>> addInvoice(@Body() Map<String, dynamic> body);
//   //
//   // @Get(path: 'invoices.json')
//   // Future<Response> fetchInvoices();
//
//   // @Get(path: 'quantities.json')
//   // Future<Response> getQuantities();
//   //
//   // @Delete(path: 'invoices/{id}.json')
//   // Future<Response> deleteInvoice(@Path("id") String id);
//
//   //Quantities
//   // @Put(path: 'quantities/{id}.json')
//   // Future<Response> setQuantity(
//   //     @Body() Map<String, dynamic> body, @Path("id") String id);
//   //
//
//   //collections
//   // @Post(path: 'collections.json')
//   // Future<Response<CollectionModel>> addcollection(
//   //     @Body() Map<String, dynamic> body);
//   //
//   // @Get(path: 'collections.json')
//   // Future<Response> getCollections();
//
//   //cashier
//   // @Post(path: 'cachier.json')
//   // Future<Response<OperationModel>> cashierOperation(
//   //     @Body() Map<String, dynamic> body);
//   //
//   // @Get(path: 'cachier.json')
//   // Future<Response> fetchCachier();
//
//   // supplier payments
//   // @Post(path: 'suppliers-pays.json')
//   // Future<Response<SupplierPaymentModel>> paySupplier(
//   //     @Body() Map<String, dynamic> body);
//   //
//   // @Get(path: 'suppliers-pays.json')
//   // Future<Response> fetchSupplierPayments();
//   //
//   // @Get(path: 'expenses.json')
//   // Future<Response> fetchExpenses();
//
//   @Get(path: 'customer.json')
//   Future<Response> fetchCustomers();
//
//   static ApiService create() {
//     final JsonSerializableConverter converter =
//         JsonSerializableConverter(const {
//       // UserModel: UserModel.fromJsonFactory,
//       // RepositoryModel: RepositoryModel.fromJsonFactory,
//       // ProductModel: ProductModel.fromJsonFactory,
//       // InvoiceModel: InvoiceModel.fromJsonFactory,
//       // SupplierModel: SupplierModel.fromJsonFactory,
//       // TransitionModel: TransitionModel.fromJsonFactory,
//       // QuantityModel: QuantityModel.fromJsonFactory,
//       // CompanyInfoModel: CompanyInfoModel.fromJsonFactory,
//       // CollectionModel: CollectionModel.fromJsonFactory,
//       // OperationModel: OperationModel.fromJsonFactory,
//       // SupplierPaymentModel: SupplierPaymentModel.fromJsonFactory,
//       // CustomerModel: CustomerModel.fromJsonFactory,
//       // ExpenseModel: ExpenseModel.fromJsonFactory,
//     });
//     final chopperClint = ChopperClient(
//         baseUrl:
//             // 'https://mandob-optimum-default-rtdb.firebaseio.com',
//             "https://mandob-app-default-rtdb.firebaseio.com/",
//         services: [
//           _$ApiService(),
//         ],
//         converter: converter,
//         errorConverter: converter,
//         client: RetryClient(http.Client()),
//         interceptors: [
//           // onRequest,
//           loggingInterceptors,
//           HttpLoggingInterceptor(),
//         ]);
//
//     return _$ApiService(chopperClint);
//   }
// }
//
// Future<Request> loggingInterceptors(Request request) async {
//   if (request.parts.isNotEmpty)
//     print(
//         "request.parts = ${request.parts.map((p) => '${p.name} : ${p.value}').toList()}");
//   return request;
// }
