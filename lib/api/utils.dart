// import 'package:chopper/chopper.dart';
// // import 'package:flutter_toolbox/flutter_toolbox.dart';
//
// typedef T JsonFactory<T>(Map<String, dynamic> json);
//
// class JsonSerializableConverter extends JsonConverter {
//   final Map<Type, JsonFactory> factories;
//
//   JsonSerializableConverter(this.factories);
//
//   T? _decodeMap<T>(Map<String, dynamic> values) {
//     /// Get jsonFactory using Type parameters
//     /// if not found or invalid, throw error or return null
//     final jsonFactory = factories[T];
//     if (jsonFactory == null || jsonFactory is! JsonFactory<T>) {
//       /// throw serializer not found error;
//       return null;
//     }
//
//     return jsonFactory(values);
//   }
//
//   List<T> _decodeList<T>(List values) =>
//       values.where((v) => v != null).map<T>((v) => _decode<T>(v)).toList();
//
//   dynamic _decode<T>(entity) {
//     if (entity is Iterable) return _decodeList<T>(entity as List<dynamic>);
//
//     if (entity is Map) return _decodeMap<T>(entity as Map<String, dynamic>);
//
//     return entity;
//   }
//
//   @override
//   Response<ResultType> convertResponse<ResultType, Item>(Response response) {
//     // use [JsonConverter] to decode json
//     final jsonRes = super.convertResponse(response);
//
//     return jsonRes.copyWith<ResultType>(body: _decode<Item>(jsonRes.body));
//   }
//
//   @override
//   // all objects should implements toJson method
//   Request convertRequest(Request request) => super.convertRequest(request);
//
//   @override
//   Response convertError<ResultType, Item>(Response response) {
//     print("Error response.body = ${response.body}");
//
//     // use [JsonConverter] to decode json
//     final Response jsonRes = super.convertError(response);
//
//     return jsonRes.copyWith<ResultType>(body: _decode<Item>(jsonRes.body));
//     //jsonRes.copyWith<ErrorResponse>(
//     //       body: ErrorResponse.fromJsonFactory({"error": jsonRes.body}),
//     //     )
//   }
// }
