import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'quantity_model.g.dart';

@JsonSerializable()
class QuantityModel {
  String? productId;
  Map<String, dynamic>? quantities = {};

  // factory QuantityModel.fromJson(Map<String, dynamic> json) =>
  //     _$QuantityModelFromJson(json);

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productId'] = productId;
    data['quantities'] = jsonEncode(quantities);
    return data;
  }

  factory QuantityModel.fromJson(Map<String, dynamic> json) {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productId'] = json['productId'];
    data['quantities'] = json['quantities'] != null && json['quantities'] != ''
        ? jsonDecode(json['quantities'])
        : {};
    return _$QuantityModelFromJson(data);
  }

  static const fromJsonFactory = _$QuantityModelFromJson;

  QuantityModel();

  @override
  String toString() {
    return 'QuantityModel{productId: $productId,quantities: $quantities}';
  }
}
