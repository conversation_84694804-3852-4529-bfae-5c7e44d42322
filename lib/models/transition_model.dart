import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
part 'transition_model.g.dart';

@JsonSerializable()
class TransitionModel {
  String? id;
  String? from;
  String? to;
  DateTime? time;
  List<dynamic>? products;
  // Map<String, dynamic>?
  //     quantity; // key : productId , value : transformed quantity

  TransitionModel({
    this.id,
    this.from,
    this.to,
    this.time,
    this.products,
  });

  factory TransitionModel.fromJson(Map<String, dynamic> json) {
    return TransitionModel(
      // id: json['id'],
      from: json['from'],
      to: json['to'],
      time: json['time'] != null ? DateTime.parse(json['time']) : null,
      products: json["products"] != null && json["products"] != ''
          ? jsonDecode(json["products"])
          : {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // 'id': id,
      'from': from,
      'to': to,
      'time': time?.toIso8601String(),
      'products': jsonEncode(products),
    };
  }

  @override
  String toString() {
    return 'TransitionModel{id: $id,from: $from, to: $to, time: $time , products: $products }';
  }
}
