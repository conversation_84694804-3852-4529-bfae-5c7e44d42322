import 'package:json_annotation/json_annotation.dart';

part 'expense_model.g.dart';

@JsonSerializable()
class ExpenseModel {
  String? id;
  num? value;
  String? source;
  String? notes;
  String? mandobId;
  DateTime? createdAt;

  // factory ExpenseModel.fromJson(Map<String, dynamic> json) =>
  //     _$ExpenseModelFromJson(json);
  //
  // Map<String, dynamic> toJson() => _$ExpenseModelToJson(this);
  //
  // static const fromJsonFactory = _$ExpenseModelFromJson;

  ExpenseModel({
    this.id,
    this.value,
    this.source,
    this.notes,
    this.mandobId,
    this.createdAt,
  });

  factory ExpenseModel.fromJson(Map<String, dynamic> json) {
    return ExpenseModel(
      value: json['value'],
      source: json['source'],
      notes: json['notes'],
      mandobId: json['mandobId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'value': value,
      'source': source,
      'notes': notes,
      'mandobId': mandobId,
    };
  }

  @override
  String toString() {
    return 'ExpenseModel{mandobId: $mandobId, id: $id,notes: $notes,value: $value,source: $source, createdAt: $createdAt}';
  }
}
