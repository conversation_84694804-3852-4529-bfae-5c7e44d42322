import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
part 'repository_model.g.dart';

@JsonSerializable()
class RepositoryModel {
  String? id;
  String? name;
  String? createdAt;
  String? location;
  Map<String, dynamic>? products; //Each key = productId & value = quantity

  // factory RepositoryModel.fromJson(Map<String, dynamic> json) =>
  //     _$RepositoryModelFromJson(json);

  // Map<String, dynamic> toJson() => _$RepositoryModelToJson(this);

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['location'] = location;
    data['products'] = jsonEncode(products);
    return data;
  }

  factory RepositoryModel.fromJson(Map<String, dynamic> json) =>
      RepositoryModel(
        id: json['id'],
        name: json["name"],
        location: json["location"],
        createdAt: json["createdAt"],
        products: json["products"] != null && json["products"] != ''
            ? jsonDecode(json["products"])
            : {},
      );

  // static const fromJsonFactory = _$RepositoryModelFromJson;

  RepositoryModel(
      {this.id, this.name, this.createdAt, this.location, this.products});

  @override
  String toString() {
    return 'RepositoryModel{id: $id, name: $name,createdAt: $createdAt, location: $location, products: $products}';
  }
}
