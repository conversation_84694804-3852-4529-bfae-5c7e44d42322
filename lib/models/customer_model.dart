import 'package:json_annotation/json_annotation.dart';

part 'customer_model.g.dart';

@JsonSerializable()
class CustomerModel {
  String? id;
  String? docId;
  String? mandobId;
  String? customerName;
  String? customerPhone;
  String? streetName;
  String? customerType;
  String? vatNumber;
  String? commercialRegisterNumber;
  double? openingBalance;
  double? debit;

  String? buildingNumber;
  String? plotIdentification;
  String? citySubdivision;
  String? city;
  String? postalNumber;

  factory CustomerModel.fromJson(Map<String, dynamic> json) {
    return CustomerModel(
      // id: json['customerId'],
      mandobId: json['mandobId'],
      customerName: json['customerName'],
      customerPhone: json['customerPhone'],
      streetName: json['address'],
      customerType: json['customerType'],
      vatNumber: json['taxNumber'],
      commercialRegisterNumber: json['commercialRegisterNumber'],
      openingBalance: double.tryParse(json['openingBalance'].toString()),
      debit: double.tryParse(json['debit'].toString()) ?? 0,
      buildingNumber: json['buildingNumber'],
      plotIdentification: json['plotIdentification'],
      citySubdivision: json['citySubdivision'],
      city: json['city'],
      postalNumber: json['postalNumber'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'customerId': docId ?? '',
      'mandobId': mandobId ?? '',
      'customerName': customerName ?? '',
      'customerPhone': customerPhone ?? '',
      'address': streetName ?? '',
      'customerType': customerType ?? '',
      'taxNumber': vatNumber ?? '',
      'openingBalance': openingBalance ?? 0,
      'debit': debit ?? 0,
      'commercialRegisterNumber': commercialRegisterNumber,
      'buildingNumber': buildingNumber,
      'plotIdentification': plotIdentification,
      'citySubdivision': citySubdivision,
      'city': city,
      'postalNumber': postalNumber,
    };
  }

  CustomerModel({
    this.id,
    this.mandobId,
    this.customerName,
    this.customerPhone,
    this.streetName,
    this.customerType,
    this.vatNumber,
    this.openingBalance,
    this.debit,
    this.docId,
    this.commercialRegisterNumber,
    this.buildingNumber,
    this.plotIdentification,
    this.citySubdivision,
    this.city,
    this.postalNumber,
  });

  @override
  String toString() {
    return 'CustomerModel{customerName: $customerName,mandobId: $mandobId,debit: $debit,customerType: $customerType, customerPhone: $customerPhone, address: $streetName, taxNumber: $vatNumber, openingBalance: $openingBalance}';
  }
}
