// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'company_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CompanyInfoModel _$CompanyInfoModelFromJson(Map<String, dynamic> json) =>
    CompanyInfoModel()
      ..companyName = json['companyName'] as String?
      ..vatNumber = json['taxNumber'] as String?
      ..address = json['address'] as String?
      ..logoUrl = json['logoUrl'] as String?
      ..tax = (json['tax'] as num?)?.toDouble()
      ..taxPercent = (json['taxPercent'] as num?)?.toDouble()
      ..bankName = json['bankName'] as String?
      ..bankNumber = json['bankNumber'] as String?;

Map<String, dynamic> _$CompanyInfoModelToJson(CompanyInfoModel instance) =>
    <String, dynamic>{
      'companyName': instance.companyName,
      'taxNumber': instance.vatNumber,
      'address': instance.address,
      'logoUrl': instance.logoUrl,
      'tax': instance.tax,
      'taxPercent': instance.taxPercent,
      'bankName': instance.bankName,
      'bankNumber': instance.bankNumber,
    };
