part 'company_info_model.g.dart';

class CompanyInfoModel {
  String? companyName;
  String? vatNumber;
  String? commercialRegisterNumber;
  String? commercial;
  String? address;
  String? logoUrl;
  double? tax;
  double? taxPercent;
  String? bankName;
  String? bankNumber;
  bool? isActive;

  factory CompanyInfoModel.fromJson(Map<String, dynamic> json) =>
      CompanyInfoModel()
        ..companyName = json['companyName'] as String?
        ..vatNumber = json['taxNumber'] as String?
        ..commercialRegisterNumber = json['commercialRegisterNumber'] as String?
        ..address = json['address'] as String?
        ..logoUrl = json['logoUrl'] as String?
        ..tax = (json['tax'] as num?)?.toDouble()
        ..taxPercent = (json['taxPercent'] as num?)?.toDouble()
        ..bankName = json['bankName'] as String?
        ..bankNumber = json['bankNumber'] as String?
        ..isActive = json['isActive'] as bool?;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'companyName': companyName,
        'taxNumber': vatNumber,
        'commercialRegisterNumber': commercialRegisterNumber,
        'address': address,
        'logoUrl': logoUrl,
        'tax': tax,
        'taxPercent': taxPercent,
        'bankName': bankName,
        'bankNumber': bankNumber,
        'isActive': isActive
      };

  CompanyInfoModel();

  @override
  String toString() {
    return 'CompanyInfoModel{companyName: $companyName, isActive: $isActive, taxNumber: $vatNumber, logoUrl: $logoUrl, tax: $tax, taxPercent: $taxPercent, address: $address}';
  }
}

//? code for generate
// flutter pub run build_runner build --delete-conflicting-outputs
