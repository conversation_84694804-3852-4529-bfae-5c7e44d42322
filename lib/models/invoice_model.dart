import 'dart:convert';

import 'package:equatable/equatable.dart';

class InvoiceModel extends Equatable {
  String? storeId;
  String? customerId;
  String? customerVatNumber;
  String? mandobId;
  String? customerName;
  String? mainInvoiceId;
  String? createdAt;
  List? products;
  num? totalPrice;
  num? totalWithoutTax;
  num? taxValPercent;
  String? invoiceId;
  String? invoiceNumber;
  String? invoiceType;
  num? paidCash;
  num? discount;
  bool? isReturned;
  bool? isSales; // true > Sales Invo , false > purchases invoc
  String? docId;

  //get tax
  num get taxValue {
    return totalPrice! - totalWithoutTax!;
  }

  Map<String, dynamic> toJson() => {
        'storeId': storeId,
        'customerId': customerId,
        'customerVatNumber': customerVatNumber,
        'mandobId': mandobId,
        'customerName': customerName,
        'mainInvoiceId': mainInvoiceId,
        'products': jsonEncode(products),
        'totalPrice': totalPrice,
        'totalWithoutTax': totalWithoutTax,
        'taxValPercent': taxValPercent,
        'invoiceId': invoiceId,
        'invoiceNumber': invoiceNumber,
        'invoiceType': invoiceType,
        'paidCash': paidCash,
        'isReturned': isReturned,
        'isSales': isSales,
        'createdAt': createdAt,
        'discount': discount,
      };

  factory InvoiceModel.fromJson(Map<String, dynamic> json) {
    return InvoiceModel(
      storeId: json['storeId'] ?? '',
      customerId: json['customerId'] ?? '',
      customerVatNumber: json['customerVatNumber'] ?? '',
      mandobId: json['mandobId'],
      customerName: json['customerName'] ?? '',
      mainInvoiceId: json['mainInvoiceId'] ?? '',
      createdAt: json['createdAt'],
      products: json['products'] != null && json['products'] != ''
          ? jsonDecode(json['products'])
          : [],
      totalPrice: json['totalPrice'] ?? 0,
      totalWithoutTax: json['totalWithoutTax'] ?? 0,
      taxValPercent: json['taxValPercent'] ?? 0,
      invoiceId: json['invoiceId'] ?? '',
      invoiceNumber: json['invoiceNumber'] ?? '',
      invoiceType: json['invoiceType'] ?? '',
      paidCash: json['paidCash'] ?? 0,
      isReturned: json['isReturned'] ?? false,
      isSales: json['isSales'] ?? true,
      discount: json['discount'] ?? 0,
    );
  }

  InvoiceModel({
    this.storeId,
    this.customerId,
    this.customerVatNumber,
    this.mandobId,
    this.customerName,
    this.mainInvoiceId,
    this.createdAt,
    this.products,
    this.totalPrice,
    this.totalWithoutTax,
    this.taxValPercent,
    this.invoiceId,
    this.invoiceNumber,
    this.invoiceType,
    this.paidCash,
    this.isReturned,
    this.isSales,
    this.discount,
  });

  @override
  List<Object?> get props => [
        storeId,
        customerId,
        customerVatNumber,
        mandobId,
        customerName,
        mainInvoiceId,
        createdAt,
        products,
        totalPrice,
        totalWithoutTax,
        taxValPercent,
        invoiceId,
        invoiceNumber,
        invoiceType,
        paidCash,
        isReturned,
        isSales,
        discount,
      ];

  InvoiceModel.empty() {
    storeId = '';
    customerId = '';
    customerVatNumber = '';
    mandobId = '';
    customerName = '';
    mainInvoiceId = '';
    createdAt = '2023-01-01 09:47:45.604161';
    products = [];
    totalPrice = 0;
    totalWithoutTax = 0;
    taxValPercent = 0;
    invoiceId = '';
    invoiceNumber = '';
    invoiceType = '';
    paidCash = 0;
    isReturned = false;
    isSales = false;
    discount = 0;
  }
}
