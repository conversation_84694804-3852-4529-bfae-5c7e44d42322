import 'package:json_annotation/json_annotation.dart';

part 'operation_model.g.dart';

@JsonSerializable()
class OperationModel {
  String? id;
  bool? isIncome;
  num? value;
  String? source;
  String? userId;
  DateTime? opTime;

  // factory OperationModel.fromJson(Map<String, dynamic> json) =>
  //     _$OperationModelFromJson(json);

  // Map<String, dynamic> toJson() => _$OperationModelToJson(this);

  OperationModel({
    this.id,
    this.isIncome,
    this.value,
    this.source,
    this.userId,
    this.opTime,
  });

  // static const fromJsonFactory = _$OperationModelFromJson;

  // OperationModel();

  factory OperationModel.fromJson(Map<String, dynamic> json) {
    return OperationModel(
      isIncome: json['isIncome'],
      value: json['value'],
      source: json['source'],
      userId: json['userId'],
      opTime: DateTime.tryParse(json['opTime'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isIncome': isIncome ?? false,
      'value': value ?? 0,
      'source': source ?? '',
      'userId': userId ?? '',
      'opTime': opTime?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'OperationModel{ userId: $userId,id: $id,isIncome: $isIncome,value: $value,source: $source, opTime: $opTime}';
  }
}
