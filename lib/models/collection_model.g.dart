// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'collection_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CollectionModel _$CollectionModelFromJson(Map<String, dynamic> json) =>
    CollectionModel()
      ..id = json['id'] as String?
      ..customerId = json['customerId'] as String?
      ..userId = json['userId'] as String?
      ..createdAt = json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String)
      ..customerName = json['customerName'] as String?
      ..value = (json['value'] as num?)?.toDouble()
      ..isCashe = json['isCashe'] as bool?
      ..type = json['type'] as String?
      ..checkImage = json['checkImage'] as String?;

Map<String, dynamic> _$CollectionModelToJson(CollectionModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'customerId': instance.customerId,
      'userId': instance.userId,
      'createdAt': instance.createdAt?.toIso8601String(),
      'customerName': instance.customerName,
      'value': instance.value,
      'isCashe': instance.isCashe,
      'type': instance.type,
      'checkImage': instance.checkImage,
    };
