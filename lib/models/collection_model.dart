import 'package:json_annotation/json_annotation.dart';
part 'collection_model.g.dart';

@JsonSerializable()
class CollectionModel {
  String? id;
  String? customerId;
  String? userId;
  DateTime? createdAt;
  String? customerName;
  double? value;
  bool? isCashe;
  String? type;
  String? checkImage;

  factory CollectionModel.fromJson(Map<String, dynamic> json) =>
      _$CollectionModelFromJson(json);

  Map<String, dynamic> toJson() => {
        'customerId': customerId,
        'userId': userId,
        'customerName': customerName,
        'value': value,
        'isCashe': isCashe,
        'type': type,
        'checkImage': checkImage,
      };

  static const fromJsonFactory = _$CollectionModelFromJson;

  CollectionModel();

  @override
  String toString() {
    return 'CollectionModel{id: $id,userId $userId,createdAt,$createdAt,type:$type,checkImage,$checkImage,isCashe,$isCashe ,customerName: $customerName, customerId: $customerId, value: $value}';
  }
}
