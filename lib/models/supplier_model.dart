import 'package:json_annotation/json_annotation.dart';

part 'supplier_model.g.dart';

@JsonSerializable()
class SupplierModel {
  String? id;
  String? name;
  String? address;
  String? phone;

  factory SupplierModel.fromJson(Map<String, dynamic> json) =>
      _$SupplierModelFromJson(json);

  Map<String, dynamic> toJson() => {
        'name': name,
        'address': address,
        'phone': phone,
      };

  static const fromJsonFactory = _$SupplierModelFromJson;

  SupplierModel({this.id, this.name, this.address, this.phone});

  @override
  String toString() {
    return 'SupplierModel{id: $id,name: $name, address: $address, phone: $phone}';
  }
}
