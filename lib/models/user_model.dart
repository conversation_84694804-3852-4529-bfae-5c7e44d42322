import 'package:json_annotation/json_annotation.dart';
import 'package:mandob/core/shared/drop_down_fields/delivery_drop_down.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  String? uid;
  bool? isAdmin = false;
  bool? isActive;
  String? name;
  String? password;
  String? email;
  String? phone;
  String? storeId;

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  UserModel(
      {this.uid,
      this.isAdmin,
      this.isActive = true,
      this.name,
      this.password,
      this.email,
      this.phone,
      this.storeId});

  UserModel.fromJson(Map<String, dynamic> json) {
    uid = json['isAdmin'] == true
        ? currentDelivery.value?.uid ?? json['uid']
        : json['uid'];
    isAdmin = json['isAdmin'] ?? false;
    isActive = json['isActive'] ?? true;
    name = json['name'];
    password = json['password'];
    email = json['email'];
    phone = json['phone'];
    storeId = json['storeId'];
  }

  @override
  String toString() {
    return 'UserModel{uid:$uid,name: $name,isAdmin: $isAdmin,isActive: $isActive, password: $password, email: $email, storeId: $storeId,phone: $phone}';
  }
}
