import 'package:json_annotation/json_annotation.dart';
import 'package:mandob/appwrite_db/appwrite.dart';
import 'package:mandob/appwrite_db/db_consts.dart';

part 'product_model.g.dart';

@JsonSerializable()
class ProductModel {
  String? name;
  String? type; //كارتون أو بالحبة
  String? image;
  double? price;
  double? bulkPrice;
  String? id;

  // factory ProductModel.fromJson(Map<String, dynamic> json) =>
  //     _$ProductModelFromJson(json);

  // Map<String, dynamic> toJson() => _$ProductModelToJson(this);

  factory ProductModel.fromJson(Map<String, dynamic> json) => ProductModel(
        name: json['name'] as String?,
        type: json['type'] as String?,
        image: json['image'] as String?,
        price: num.tryParse(json['price']?.toString() ?? '0')?.toDouble(),
        bulkPrice:
            num.tryParse(json['bulkPrice']?.toString() ?? '0')?.toDouble(),
        id: json['id'] as String?,
      );

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    final isThamraDB = AppwriteDB.databaseId == DbConsts.thamraDB;

    // data['id'] = id;
    data['name'] = name;
    data['type'] = type;
    data['image'] = image;
    data['price'] = isThamraDB ? price.toString() : price;
    data['bulkPrice'] = isThamraDB ? bulkPrice.toString() : bulkPrice;
    //? For thamra DB prod price is string -> Because they needed to make the price
    //? with 5 decimal places or more, eg: 2.43674

    return data;
  }

  ProductModel({
    this.name,
    this.type,
    this.image,
    this.price,
    this.bulkPrice,
    this.id,
  });

// static const fromJsonFactory = _$ProductModelFromJson;
}
