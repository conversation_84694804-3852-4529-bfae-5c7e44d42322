import 'package:json_annotation/json_annotation.dart';
part 'supplier_payment_model.g.dart';

@JsonSerializable()
class SupplierPaymentModel {
  String? id;
  DateTime? createdAt;
  String? supplierName;
  String? supplierId;
  num? value;
  String? userId;

  SupplierPaymentModel({
    this.id,
    this.createdAt,
    this.supplierName,
    this.supplierId,
    this.value,
    this.userId,
  });

  factory SupplierPaymentModel.fromJson(Map<String, dynamic> json) {
    return SupplierPaymentModel(
      supplierName: json['supplierName'],
      supplierId: json['supplierId'],
      value: json['value'],
      userId: json['userId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'supplierName': supplierName,
      'supplierId': supplierId,
      'value': value,
      'userId': userId,
    };
  }

  @override
  String toString() {
    return 'SupplierPaymentModel{id: $id,userId: $userId,createdAt,$createdAt,supplierName: $supplierName, supplierId: $supplierId, value: $value}';
  }
}
