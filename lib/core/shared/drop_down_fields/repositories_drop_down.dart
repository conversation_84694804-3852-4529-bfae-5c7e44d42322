import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../providers/user_provider.dart';

class RepositoriesDropDownButton extends StatelessWidget {
  final onChanged;
  final onTap;
  final repo;
  final repoVal;
  final visible;
  const RepositoriesDropDownButton(
      {Key? key,
      this.onChanged,
      this.onTap,
      this.repo,
      this.repoVal,
      this.visible})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final user = Provider.of<UserProvider>(context, listen: false).activeUser;

    return Padding(
      padding: EdgeInsets.only(left: user!.isAdmin! ? 10 : 0),
      child: user.isAdmin!
          ? !visible
              ? GestureDetector(
                  onTap: onTap,
                  child: Text('اختر المخزن'),
                )
              : Visibility(
                  visible: visible,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('المخزن'),
                      DropdownButton<String>(
                          value: repoVal,
                          underline: Container(),
                          items: [
                            ...repo.repositories.map((value) {
                              return DropdownMenuItem<String>(
                                value: value.name,
                                child: new Text(
                                  value.name ?? "",
                                  style: TextStyle(color: Colors.black),
                                ),
                              );
                            })
                          ],
                          onChanged: onChanged)
                    ],
                  ),
                )
          : Container(),
    );
  }
}
