import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/models/supplier_model.dart';
import 'package:mandob/pages/supplier_page/components/add_supplier_dialog.dart';
import 'package:mandob/providers/suppliers_provider.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../utils/color_manager.dart';
import '../../../utils/loading_widget.dart';

class SuppliersDropDownButton extends HookWidget {
  final void Function(SupplierModel?)? onChanged;
  final SupplierModel? supplier;
  final double width;

  const SuppliersDropDownButton({
    super.key,
    this.onChanged,
    this.supplier,
    this.width = 300.0,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<SuppliersProvider>(
      builder: (context, supplierProvider, child) {
        return HookBuilder(builder: (context) {
          useEffect(() {
            if (supplierProvider.suppliers.isEmpty) {
              supplierProvider.fetchSuppliers();
            }

            return () {};
          }, [
            supplierProvider.suppliers,
          ]);

          if (supplierProvider.isLoading) {
            return const LoadingWidget();
          }

          return Container(
            height: 60,
            width: width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: ColorManager.lightFieldColor.withOpacity(0.5),
            ),
            child: DropdownSearch<SupplierModel>(
              autoValidateMode: AutovalidateMode.onUserInteraction,
              validator: (value) {
                if (value == null) {
                  return context.isEng
                      ? 'Please select a supplier'
                      : 'برجاء اختيار المورد';
                }
              },
              popupProps: PopupProps.menu(
                itemBuilder: (context, supplier, isSelected) {
                  return Column(
                    children: [
                      ListTile(
                        selected: isSelected,
                        title: Text(supplier.name ?? ''),
                      ),
                      const Divider(),
                    ],
                  );
                },
                favoriteItemProps: FavoriteItemProps(
                  favoriteItemsAlignment: MainAxisAlignment.start,
                  showFavoriteItems: true,
                  favoriteItems: (s) {
                    return [
                      SupplierModel(),
                    ];
                  },
                  favoriteItemBuilder: (context, item, isSelected) {
                    return TextButton(
                      onPressed: () async {
                        context.back();

                        await Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (_) =>
                                    Material(child: AddSupplierDialog())));

                        supplierProvider.fetchSuppliers(forceLoad: true);
                      },
                      child: Text(
                        context.isEng ? 'Add New Supplier' : 'اضافة مورد جديد',
                        style: const TextStyle(
                          color: ColorManager.brown,
                          fontSize: 16,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                    );
                  },
                ),
                isFilterOnline: false,
                showSelectedItems: false,
                searchFieldProps: TextFieldProps(
                  decoration: InputDecoration(
                    border: const UnderlineInputBorder(),
                    enabledBorder: const UnderlineInputBorder(),
                    contentPadding: const EdgeInsets.all(12),
                    hintText: context.isEng ? 'Search' : 'بحث',
                    hintStyle: const TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ),
                showSearchBox: true,
              ),
              dropdownBuilder: (context, value) {
                if (value == null && supplier == null) {
                  return Text(
                    context.isEng ? 'Select Supplier' : 'اختر المورد',
                    style: const TextStyle(
                        color: Colors.black,
                        fontSize: 14,
                        fontWeight: FontWeight.normal),
                  );
                }
                return Text(
                  supplier?.name ??
                      (context.isEng ? 'Select Supplier' : 'اختر المورد'),
                  style: const TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.normal),
                );
              },
              dropdownDecoratorProps: DropDownDecoratorProps(
                dropdownSearchDecoration: InputDecoration(
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  labelText: context.isEng ? "Supplier" : "المورد",
                  filled: true,
                  fillColor: Colors.transparent,
                ),
              ),
              items: supplierProvider.suppliers,
              onChanged: onChanged,
            ),
          );
        });
      },
    );
  }
}
