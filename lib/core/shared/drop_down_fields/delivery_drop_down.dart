import 'dart:developer';

import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/pages/delivery_page/components/add_delivery_dialog.dart';
import 'package:mandob/pages/main_screen/main_screen.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:provider/provider.dart';

import '../../../../../models/user_model.dart';
import '../../../../../providers/user_provider.dart';

ValueNotifier<UserModel?> currentDelivery = ValueNotifier(null);

class DeliveryDropDownButton extends StatelessWidget {
  final double width;

  const DeliveryDropDownButton({
    super.key,
    this.width = 300.0,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        return HookBuilder(builder: (context) {
          useEffect(() {
            if (userProvider.users == null || userProvider.users!.isEmpty) {
              userProvider.fetchUsers(withLoading: true);
            }

            return () {};
          }, [
            userProvider.users,
          ]);

          if (userProvider.isLoading) {
            return const LoadingWidget();
          }

          return Container(
            height: 60,
            width: width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: ColorManager.lightFieldColor.withOpacity(0.5),
            ),
            child: DropdownSearch<UserModel>(
              autoValidateMode: AutovalidateMode.onUserInteraction,
              validator: (value) {
                if (value == null) {
                  return context.isEng
                      ? 'Please select a representative'
                      : 'برجاء اختيار المندوب';
                }
              },
              popupProps: PopupProps.menu(
                itemBuilder: (context, delivery, isSelected) {
                  return Column(
                    children: [
                      ListTile(
                        selected: isSelected,
                        title: Text(delivery.name!),
                      ),
                      const Divider(),
                    ],
                  );
                },
                favoriteItemProps: FavoriteItemProps(
                  favoriteItemsAlignment: MainAxisAlignment.start,
                  showFavoriteItems: true,
                  favoriteItems: (s) {
                    return [
                      UserModel(),
                    ];
                  },
                  favoriteItemBuilder: (context, item, isSelected) {
                    return TextButton(
                      onPressed: () async {
                        Navigator.of(context).pop();
                        showDialog(
                          context: context,
                          builder: (context) => const AddDeliveryDialog(),
                        );
                        // Add your navigation to add delivery screen here
                      },
                      child: Text(
                          context.isEng
                              ? 'Add new representative'
                              : 'اضافة مندوب جديد',
                          style: const TextStyle(
                              color: ColorManager.brown,
                              fontSize: 16,
                              fontWeight: FontWeight.normal)),
                    );
                  },
                ),
                isFilterOnline: false,
                showSelectedItems: false,
                searchFieldProps: TextFieldProps(
                  decoration: InputDecoration(
                    border: const UnderlineInputBorder(),
                    enabledBorder: const UnderlineInputBorder(),
                    contentPadding: const EdgeInsets.all(12),
                    hintText: context.isEng ? 'Search' : 'بحث',
                    hintStyle: const TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ),
                showSearchBox: true,
              ),
              dropdownBuilder: (context, value) {
                if (value == null && currentDelivery.value == null) {
                  return Text(
                    context.isEng ? 'Select Representative' : 'اختر المندوب',
                    style: const TextStyle(
                        color: Colors.black,
                        fontSize: 14,
                        fontWeight: FontWeight.normal),
                  );
                }

                return Text(
                  currentDelivery.value?.name ??
                      (context.isEng
                          ? 'Select Representative'
                          : 'اختر المندوب'),
                  style: const TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.normal),
                );
              },
              dropdownDecoratorProps: DropDownDecoratorProps(
                dropdownSearchDecoration: InputDecoration(
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  labelText: context.isEng ? "Representative" : "المندوب",
                  filled: true,
                  fillColor: Colors.transparent,
                ),
              ),
              items: userProvider.users
                      ?.where((user) => !user.isAdmin!)
                      .toList() ??
                  [],
              onChanged: (UserModel? value) {
                currentDelivery.value = value;

                log('asfsfas ${currentDelivery.value?.uid}');

                userProvider.autoLog();

                Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const MainScreen()));
              },
            ),
          );
        });
      },
    );
  }
}
