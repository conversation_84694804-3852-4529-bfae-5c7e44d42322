import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/pages/customer_page/components/add_customer_screen.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:provider/provider.dart';

import '../../../../../models/customer_model.dart';
import '../../../../../providers/customers_provider.dart';
import '../../../../../providers/user_provider.dart';

class CustomersDropDownButton extends StatelessWidget {
  final void Function(CustomerModel?)? onChanged;
  final CustomerModel? customer;
  final double width;
  final bool showBalance;

  const CustomersDropDownButton({
    super.key,
    this.onChanged,
    this.customer,
    this.width = 300.0,
    this.showBalance = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer2<CustomersProvider, UserProvider>(
      builder: (context, customerProvider, userProvider, child) {
        final user = userProvider.activeUser;

        return HookBuilder(builder: (context) {
          useEffect(() {
            if (customerProvider.allCustomers.isEmpty) {
              customerProvider.fetchAllCustomers();
            }

            return () {};
          }, [
            customerProvider.allCustomers,
          ]);

          if (customerProvider.isLoading) {
            return const LoadingWidget();
          }

          return Container(
            height: 60,
            width: width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: ColorManager.lightFieldColor.withOpacity(0.5),
            ),
            child: DropdownSearch<CustomerModel>(
              autoValidateMode: AutovalidateMode.onUserInteraction,
              validator: (value) {
                if (value == null) {
                  return context.isEng
                      ? 'Please select a customer'
                      : 'برجاء اختيار العميل';
                }
              },
              popupProps: PopupProps.menu(
                itemBuilder: (context, client, isSelected) {
                  return Column(
                    children: [
                      ListTile(
                        selected: isSelected,
                        title: Text(client.customerName!),
                        subtitle: Text(context.isEng
                            ? 'Customer Balance:  -${client.debit}${context.currency}'
                            : 'رصيد العميل:  -${client.debit}${context.currency}'),
                      ),
                      const Divider(),
                    ],
                  );
                },
                favoriteItemProps: FavoriteItemProps(
                  favoriteItemsAlignment: MainAxisAlignment.start,
                  showFavoriteItems: true,
                  favoriteItems: (s) {
                    return [
                      CustomerModel(),
                    ];
                  },
                  favoriteItemBuilder: (context, item, isSelected) {
                    return TextButton(
                      onPressed: () async {
                        Navigator.of(context).pop();
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (_) => const AddCustomerScreen(
                                      fromInvoiceDialog: true,
                                    )));
                      },
                      child: Text(
                          context.isEng
                              ? 'Add New Customer'
                              : 'اضافة عميل جديد',
                          style: const TextStyle(
                              color: ColorManager.brown,
                              fontSize: 16,
                              fontWeight: FontWeight.normal)),
                    );
                  },
                ),
                isFilterOnline: false,
                showSelectedItems: false,
                searchFieldProps: TextFieldProps(
                  decoration: InputDecoration(
                    border: const UnderlineInputBorder(),
                    enabledBorder: const UnderlineInputBorder(),
                    contentPadding: const EdgeInsets.all(12),
                    hintText: context.isEng ? 'Search' : 'بحث',
                    hintStyle: const TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ),
                showSearchBox: true,
              ),
              dropdownBuilder: (context, value) {
                if (value == null && customer == null) {
                  return Text(
                    context.isEng ? 'Select Customer' : 'اختر العميل',
                    style: const TextStyle(
                        color: Colors.black,
                        fontSize: 14,
                        fontWeight: FontWeight.normal),
                  );
                }
                return Text(
                  (customer?.customerName ??
                          (context.isEng ? 'Select Customer' : 'اختر العميل')) +
                      (showBalance
                          ? ' - ${customer?.debit ?? 0.0}${context.currency}'
                          : ''),
                  style: const TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.normal),
                );
              },
              dropdownDecoratorProps: DropDownDecoratorProps(
                dropdownSearchDecoration: InputDecoration(
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  labelText: context.isEng ? "Customer" : "العميل",
                  filled: true,
                  fillColor: Colors.transparent,
                ),
              ),
              items: user!.isAdmin!
                  ? customerProvider.allCustomers
                  : customerProvider.mandobCustomers,
              onChanged: onChanged,
            ),
          );
        });
      },
    );
  }
}
