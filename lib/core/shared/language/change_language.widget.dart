import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:mandob/providers/settings_provider.dart';
import 'package:mandob/utils/color_manager.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

void showChangeLanguageDialog(
  BuildContext context, {
  Function()? onEnTap,
  Function()? onArTap,
}) {
  showDialog(
    context: context,
    builder: (context) => ChangeLanguageWidget(
      onEnTap: onEnTap,
      onArTap: onArTap,
    ),
  );
}

class ChangeLanguageWidget extends StatelessWidget {
  final Function()? onEnTap;
  final Function()? onArTap;

  const ChangeLanguageWidget({
    super.key,
    this.onEnTap,
    this.onArTap,
  });

  @override
  Widget build(BuildContext context) {
    final settingsController = Provider.of<AppSettingsController>(context);
    final fromPrint = onEnTap != null || onArTap != null;
    final isEng = settingsController.locale.languageCode == 'en';
    final enColor =
        isEng && !fromPrint ? ColorManager.primaryColor : Colors.black;
    final arColor =
        !isEng && !fromPrint ? ColorManager.primaryColor : Colors.black;

    return AlertDialog(
      surfaceTintColor: Colors.white,
      title: Text(
        context.isEng ? 'Change Language' : 'تغيير اللغة',
        style: AppTextStyles.title
            .copyWith(fontWeight: FontWeight.bold, fontSize: 22),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            onTap: () {
              if (onEnTap != null) {
                context.back();
                onEnTap!();
              } else {
                settingsController.updateLanguage(Locale(isEng ? 'ar' : 'en'));
                Navigator.of(context).pop();
              }
            },
            title: Text('English',
                style: AppTextStyles.subTitle
                    .copyWith(fontWeight: FontWeight.bold, color: enColor)),
            leading: Icon(
              Icons.language,
              color: enColor,
            ),
          ),
          ListTile(
            onTap: () {
              if (onArTap != null) {
                context.back();
                onArTap!();
              } else {
                settingsController.updateLanguage(Locale(isEng ? 'ar' : 'en'));
                Navigator.of(context).pop();
              }
            },
            title: Text('العربية',
                style: AppTextStyles.subTitle
                    .copyWith(fontWeight: FontWeight.bold, color: arColor)),
            leading: Icon(
              Icons.language,
              color: arColor,
            ),
          ),
        ],
      ),
    );
  }
}
