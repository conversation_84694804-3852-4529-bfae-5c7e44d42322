import 'package:flutter/material.dart';
import 'package:mandob/core/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

class UiWidgets {
  //Date Widget ************************
  static Widget datePicker(BuildContext ctx, Function onChange,
      {ValueNotifier<DateTime>? startDate,
      ValueNotifier<DateTime>? endDate,
      String label = 'اختر التاريخ'}) {
    return Row(
      children: [
        Expanded(
            child: BaseDatePicker(
                onChanged: (obj) {
                  onChange();
                },
                selectedDateNotifier:
                    startDate ?? ValueNotifier(DateTime.now()),
                label: ctx.isEng ? 'Start Date' : 'تاريخ البداية')),
        AppGaps.gap12,
        Expanded(
          child: BaseDatePicker(
              firstDate: startDate?.value,
              onChanged: (obj) {
                onChange();
              },
              selectedDateNotifier: endDate ?? ValueNotifier(DateTime.now()),
              label: ctx.isEng ? 'End Date' : 'تاريخ النهاية'),
        ),
      ],
    );
  }
}

// class DatePickerWidget extends StatefulWidget {
//   final Function onDateSelected;
//   final String title;
//
//   const DatePickerWidget(
//       {super.key, required this.onDateSelected, this.title = 'اختر التاريخ'});
//
//   @override
//   State<DatePickerWidget> createState() => _DatePickerWidgetState();
// }

// class _DatePickerWidgetState extends State<DatePickerWidget> {
//   DateTime? selectedDate;
//
//   @override
//   Widget build(BuildContext context) {
//     return ElevatedButton(
//         onPressed: () {
//           Widget _datePicker = Dialog(
//               child: SfDateRangePicker(
//                 showActionButtons: true,
//                 initialSelectedDate: DateTime.now(),
//                 selectionMode: DateRangePickerSelectionMode.single,
//                 onCancel: () => Navigator.of(context).pop(),
//                 onSubmit: (obj) {
//                   if (obj != null) {
//                     final dat = DateTime.parse(obj.toString());
//                     selectedDate = dat;
//                     print(selectedDate);
//                     widget.onDateSelected(obj);
//                   }
//
//                   Navigator.of(context).pop();
//                 },
//               ));
//           showDialog(context: context, builder: (ctx) => _datePicker);
//         },
//         child: FittedBox(
//           fit: BoxFit.contain,
//           child: Text(
//             selectedDate != null
//                 ? DateFormat('dd/MM/yyyy').format(selectedDate!).toString()
//                 : (context.isEng ? 'Select Date' : widget.title),
//           ),
//         ));
//   }
// }
