import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:mandob/app_localization.dart';
import 'package:mandob/pages/splash/splash_screen.dart';
import 'package:mandob/providers/cashier_provider.dart';
import 'package:mandob/providers/collections_provider.dart';
import 'package:mandob/providers/company_provider.dart';
import 'package:mandob/providers/customers_provider.dart';
import 'package:mandob/providers/expense_provider.dart';
import 'package:mandob/providers/invoices_provider.dart';
import 'package:mandob/providers/language.dart';
import 'package:mandob/providers/products_provider.dart';
import 'package:mandob/providers/quantities_provider.dart';
import 'package:mandob/providers/reports_provider.dart';
import 'package:mandob/providers/repository_provider.dart';
import 'package:mandob/providers/supplier_payments_provider.dart';
import 'package:mandob/providers/suppliers_provider.dart';
import 'package:mandob/providers/transitions_provider.dart';
import 'package:mandob/providers/user_provider.dart';
import 'package:mandob/utils/loading_widget.dart';
import 'package:provider/provider.dart';
import 'package:sunmi_printer_plus/sunmi_printer_plus.dart';
import 'package:xr_helper/xr_helper.dart';

import 'appwrite_db/appwrite.dart';
import 'core/theme/theme_manager.dart';
import 'providers/settings_provider.dart';

DateTime? sDate = DateTime.now();
DateTime? eDate = DateTime.now();

// context.isEng => lang.appLocal.languageCode == 'en';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // await Firebase.initializeApp();

  await GetStorageService.init();
  AppwriteDB.init();

  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.grey,
    ),
  );
  // await GetStorageService.removeKey(key: LocalKeys.printer);
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  bool printBinded = false;
  int paperSize = 0;
  String serialNumber = "";
  String printerVersion = "";

  @override
  void initState() {
    super.initState();
    _bindingPrinter().then((bool? isBind) async {
      SunmiPrinter.paperSize().then((int size) {
        setState(() {
          paperSize = size;
        });
      });

      SunmiPrinter.printerVersion().then((String version) {
        setState(() {
          printerVersion = version;
        });
      });

      SunmiPrinter.serialNumber().then((String serial) {
        setState(() {
          serialNumber = serial;
        });
      });

      setState(() {
        printBinded = isBind!;
      });
    });
  }

  Future<bool?> _bindingPrinter() async {
    final bool? result = await SunmiPrinter.bindingPrinter();
    return result;
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<LangProvider>.value(value: LangProvider()),
        ChangeNotifierProvider(create: (ctx) => UserProvider()..autoLog()),
        ChangeNotifierProvider(create: (ctx) => RepositoryProvider()),
        ChangeNotifierProvider(create: (ctx) => ProductsProvider()),
        ChangeNotifierProvider(create: (ctx) => SuppliersProvider()),
        ChangeNotifierProvider(create: (ctx) => TransitionsProvider()),
        ChangeNotifierProvider(create: (ctx) => QuantitiesProvider()),
        ChangeNotifierProvider(create: (ctx) => CompanyInfoProvider()),
        ChangeNotifierProvider(create: (ctx) => ExpensesProvider()),
        ChangeNotifierProvider(create: (ctx) => AppSettingsController()),
        // ChangeNotifierProvider(create: (ctx) => PrinterVM()),
        ChangeNotifierProxyProvider<UserProvider, SupplierPaymentsProvider>(
          update: (ctx, user, previousProducts) =>
              SupplierPaymentsProvider(user: user.activeUser),
          create: (_) => SupplierPaymentsProvider(),
        ),
        ChangeNotifierProxyProvider<UserProvider, InvoicesProvider>(
          update: (ctx, user, previousData) =>
              InvoicesProvider(user: user.activeUser),
          create: (_) => InvoicesProvider(),
        ),
        ChangeNotifierProxyProvider<UserProvider, ReportsProvider>(
          update: (ctx, user, previousData) =>
              ReportsProvider(user: user.activeUser),
          create: (_) => ReportsProvider(),
        ),
        ChangeNotifierProxyProvider<UserProvider, CashierProvider>(
          update: (ctx, user, previousProducts) =>
              CashierProvider(user: user.activeUser),
          create: (_) => CashierProvider(),
        ),
        ChangeNotifierProxyProvider<UserProvider, CustomersProvider>(
          update: (ctx, user, previousProducts) =>
              CustomersProvider(user: user.activeUser),
          create: (_) => CustomersProvider(),
        ),
        ChangeNotifierProxyProvider<UserProvider, CollectionsProvider>(
          update: (ctx, user, previousProducts) =>
              CollectionsProvider(user: user.activeUser),
          create: (_) => CollectionsProvider(),
        ),
      ],
      child: SafeArea(
        child: KeyboardVisibilityProvider(
          child: Consumer<AppSettingsController>(
            builder: (context, settingsController, child) {
              return BaseMaterialApp(
                supportedLocales: const [
                  Locale("ar", "EG"),
                  Locale("en", "US"),
                ],
                loadingWidget: const LoadingWidget(),
                title: 'Mandob',
                locale: settingsController.locale,
                localizationsDelegates: const [
                  AppLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate
                ],
                theme: appTheme,
                home: const SplashScreen(),
              );
            },
          ),
        ),
      ),
    );
  }
}
