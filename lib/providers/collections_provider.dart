import 'dart:async';
import 'dart:developer';

import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart';
import 'package:flutter/foundation.dart';
import 'package:mandob/models/collection_model.dart';
import 'package:mandob/models/operation_model.dart';
import 'package:mandob/models/user_model.dart';

import '../appwrite_db/appwrite.dart';
import '../appwrite_db/db_consts.dart';

class CollectionsProvider extends ChangeNotifier {
  final UserModel? user;

  CollectionsProvider({this.user});

  List<CollectionModel>? _collections = [];

  List<CollectionModel>? get collections => _collections;

  Future addCollection(CollectionModel collections) async {
    try {
      collections.userId = user!.uid;

      debugPrint('addingCollection ${collections.toJson()}');

      await AppwriteDB.createDocument(
          collectionId: DbConsts.collections, data: collections.toJson());

      final collection = OperationModel.fromJson({
        "isIncome": true,
        "value": collections.value,
        "source": "collection",
        "userId": user!.uid,
        "opTime": collections.createdAt!.toIso8601String(),
      });

      await AppwriteDB.createDocument(
          collectionId: DbConsts.cashier, data: collection.toJson());
    } catch (e) {
      debugPrint('addCollectionError: $e');
    }

    notifyListeners();
  }

  Future fetchCollections({
    String? customerId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      _collections = [];
      final Set<String> collectionIds = {};

      for (var i = 0; i < 5000; i += 100) {
        final data = await AppwriteDB.listDocuments(
          collectionId: DbConsts.collections,
          queries: [
            Query.limit(100),
            if (i != 0) Query.offset(i),
            Query.orderDesc('\$createdAt'),
            if (startDate != null && endDate != null)
              Query.greaterThan('\$createdAt', startDate.toIso8601String()),
            if (startDate != null && endDate != null)
              Query.lessThan('\$createdAt', endDate.toIso8601String()),
            if (customerId != null) Query.equal('customerId', customerId),
          ],
        );

        final List<CollectionModel> addedCollections =
            await _handleDataAfterFetch(data);

        if (addedCollections.isEmpty) break;

        for (var collection in addedCollections) {
          if (!collectionIds.contains(collection.id)) {
            collectionIds.add(collection.id!);
            _collections!.add(collection);
          }
        }
      }

      log('collectionsLength: ${_collections!.length}');
    } catch (error) {
      debugPrint('Error in fetchCollections $error');
    }
  }
  // Future fetchCollections({
  //   String? customerId,
  //   DateTime? startDate,
  //   DateTime? endDate,
  // }) async {
  //   try {
  //     _collections = [];
  //
  //
  //     if (_collections!.isEmpty) {
  //       final data = await AppwriteDB.listDocuments(
  //         collectionId: DbConsts.collections,
  //         queries: [
  //           Query.limit(100),
  //           Query.orderDesc('\$createdAt'),
  //           if (startDate != null && endDate != null)
  //             Query.greaterThan('\$createdAt', startDate.toIso8601String()),
  //           if (startDate != null && endDate != null)
  //             Query.lessThan('\$createdAt', endDate.toIso8601String()),
  //           if (customerId != null) Query.equal('customerId', customerId),
  //         ],
  //       );
  //
  //       for (var element in data.documents) {
  //         final collection = CollectionModel.fromJson(element.data);
  //         collection.id = element.$id;
  //         collection.createdAt = DateTime.tryParse(element.$createdAt);
  //
  //         _collections!.add(collection);
  //       }
  //     }
  //
  //     for (var i = 0; i < 5000; i += 100) {
  //       final data = await AppwriteDB.listDocuments(
  //         collectionId: DbConsts.collections,
  //         queries: [
  //           Query.limit(100),
  //           if (i != 0) Query.offset(i + 100),
  //           Query.orderDesc('\$createdAt'),
  //           if (startDate != null && endDate != null)
  //             Query.greaterThan('\$createdAt', startDate.toIso8601String()),
  //           if (startDate != null && endDate != null)
  //             Query.lessThan('\$createdAt', endDate.toIso8601String()),
  //           if (customerId != null) Query.equal('customerId', customerId),
  //         ],
  //       );
  //       final List<CollectionModel> addedCollections =
  //           await _handleDataAfterFetch(data);
  //       if (addedCollections.isEmpty) break;
  //       // final mandobCollections = addedCollections
  //       //     .where((element) => element.userId == user!.uid)
  //       //     .toList();
  //
  //       // if (user!.isAdmin!) {
  //       // } else {
  //       //   _collections!.addAll(mandobCollections);
  //       // }
  //       _collections!.addAll(addedCollections);
  //     }
  //
  //     log('collectionsLength: ${_collections!.length}');
  //   } catch (error) {
  //     debugPrint('Error in fetchCollections $error');
  //   }
  // }

  FutureOr<List<CollectionModel>> _handleDataAfterFetch(DocumentList data) {
    final resData = data.documents;

    final collections = resData.map((data) {
      final CollectionModel collection = CollectionModel.fromJson(data.data);
      collection.id = data.$id;
      collection.createdAt = DateTime.tryParse(data.$createdAt)?.toLocal();
      return collection;
    }).toList();

    return collections;
  }
}
