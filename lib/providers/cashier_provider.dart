import 'dart:async';
import 'dart:developer';

import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart';
import 'package:flutter/cupertino.dart';
import 'package:mandob/models/operation_model.dart';
import 'package:mandob/models/user_model.dart';

import '../appwrite_db/appwrite.dart';
import '../appwrite_db/db_consts.dart';

class CashierProvider extends ChangeNotifier {
  final UserModel? user;

  CashierProvider({this.user});

  // final _service = ApiService.create();
  List<OperationModel>? _operations = [];

  List<OperationModel>? get operations => _operations;

  // double _totalCurrency = 0;
  double get totalCurrency {
    // fetchOps();
    double _total = 0;
    if (user!.isAdmin!) {
      if (_operations != null && _operations!.isNotEmpty) {
        _operations!.map((e) => e).forEach((element) {
          if (element.isIncome!) {
            _total += element.value!;
          } else if (!element.isIncome!) {
            _total -= element.value!;
          }
        });
      }
    } else {
      if (_operations != null && _operations!.isNotEmpty) {
        _operations!.map((e) => e).forEach((element) {
          if (element.userId == user!.uid) {
            if (element.isIncome!) {
              _total += element.value!;
            } else if (!element.isIncome!) {
              _total -= element.value!;
            }
          }
        });
      }
    }

    return _total;
  }

  Future createOperation(OperationModel op) async {
    if (!op.isIncome! && (totalCurrency - op.value!).isNegative) {
      throw "Currency is not enough";
    } else {
      try {
        await AppwriteDB.createDocument(
            // documentId: op.id!,
            collectionId: DbConsts.cashier,
            data: op.toJson());
      } catch (e) {
        debugPrint('addOperationError: $e');
        rethrow;
      }
    }

    fetchOps();

    notifyListeners();
  }

  Future fetchOps({
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    bool? isIncome,
    String? source,
  }) async {
    try {
      _operations = [];
      final Set<String> operationIds = {};

      log('Filters: StartDate: $startDate, EndDate: $endDate, UserId: $userId, IsIncome: $isIncome, Source: $source');

      for (var i = 0; i < 5000; i += 100) {
        final data = await AppwriteDB.listDocuments(
          collectionId: DbConsts.cashier,
          queries: [
            Query.limit(100),
            if (i != 0) Query.offset(i),
            Query.orderDesc('\$createdAt'),
            if (startDate != null && endDate != null)
              Query.greaterThan('\$createdAt', startDate.toIso8601String()),
            if (startDate != null && endDate != null)
              Query.lessThan('\$createdAt', endDate.toIso8601String()),
            Query.equal('userId', userId ?? user?.uid),
            if (isIncome != null) Query.equal('isIncome', isIncome),
            if (source != null) Query.equal('source', source),
          ],
        );

        final List<OperationModel> addedOps = await _handleDataAfterFetch(data);

        if (addedOps.isEmpty) break;

        for (var op in addedOps) {
          if (!operationIds.contains(op.id)) {
            operationIds.add(op.id!);
            _operations!.add(op);
          }
        }
      }

      debugPrint('adsdsd: ${_operations!.length}');
    } catch (error) {
      debugPrint('fetchOpsError: $error');
    }
  }

  FutureOr<List<OperationModel>> _handleDataAfterFetch(DocumentList data) {
    final resData = data.documents;

    final operations = resData.map((data) {
      final OperationModel operation = OperationModel.fromJson(data.data);
      operation.id = data.$id;
      return operation;
    }).toList();

    return operations;
  }
}
