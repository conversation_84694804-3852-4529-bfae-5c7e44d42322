import 'dart:async';

import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart';
import 'package:flutter/foundation.dart';
import 'package:mandob/models/operation_model.dart';
import 'package:mandob/models/supplier_payment_model.dart';
import 'package:mandob/models/user_model.dart';

import '../appwrite_db/appwrite.dart';
import '../appwrite_db/db_consts.dart';

class SupplierPaymentsProvider extends ChangeNotifier {
  final UserModel? user;

  // final _service = ApiService.create();

  List<SupplierPaymentModel>? _payments = [];

  SupplierPaymentsProvider({this.user});

  List<SupplierPaymentModel>? get payments => _payments;

  Future addPayment(SupplierPaymentModel supplierPayment) async {
    try {
      await AppwriteDB.createDocument(
          collectionId: DbConsts.supplierPays, data: supplierPayment.toJson());

      final OperationModel op = OperationModel.fromJson({
        "isIncome": false,
        "value": supplierPayment.value,
        "source": "supplier-payments",
        "userId": user!.uid,
        "opTime": DateTime.now().toIso8601String(),
      });

      await AppwriteDB.createDocument(
        collectionId: DbConsts.cashier,
        data: op.toJson(),
      );
    } catch (e) {
      debugPrint('addOperationError: $e');
      rethrow;
    }

    notifyListeners();
  }

  Future fetchPayments({
    String? supplierId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      _payments = [];

      if (_payments!.isEmpty) {
        final data = await AppwriteDB.listDocuments(
          collectionId: DbConsts.supplierPays,
          queries: [
            Query.limit(100),
            if (supplierId != null) Query.equal('supplierId', supplierId),
            Query.orderDesc('\$createdAt'),
            if (startDate != null && endDate != null)
              Query.greaterThan('\$createdAt', startDate.toIso8601String()),
            if (startDate != null && endDate != null)
              Query.lessThan('\$createdAt', endDate.toIso8601String()),
            Query.equal('userId', user!.uid),
          ],
        );

        for (var element in data.documents) {
          final SupplierPaymentModel payment =
              SupplierPaymentModel.fromJson(element.data);
          payment.id = element.$id;
          payment.createdAt = DateTime.tryParse(element.$createdAt)?.toLocal();

          _payments!.add(payment);
        }
      }

      for (var i = 0; i < 5000; i += 100) {
        final data = await AppwriteDB.listDocuments(
          collectionId: DbConsts.supplierPays,
          queries: [
            Query.limit(100),
            if (i != 0) Query.offset(i + 100),
            if (supplierId != null) Query.equal('supplierId', supplierId),
            Query.orderDesc('\$createdAt'),
            if (startDate != null && endDate != null)
              Query.greaterThan('\$createdAt', startDate.toIso8601String()),
            if (startDate != null && endDate != null)
              Query.lessThan('\$createdAt', endDate.toIso8601String()),
            Query.equal('userId', user!.uid),
          ],
        );

        final List<SupplierPaymentModel> addedPayments =
            await _handleDataAfterFetch(data);

        if (addedPayments.isEmpty) break;
        _payments!.addAll(addedPayments);
      }
    } catch (error) {
      debugPrint('Error in fetchPayments $error');
    }
  }

  FutureOr<List<SupplierPaymentModel>> _handleDataAfterFetch(
      DocumentList data) {
    final resData = data.documents;

    final payments = resData.map((data) {
      final SupplierPaymentModel payment =
          SupplierPaymentModel.fromJson(data.data);
      payment.id = data.$id;
      payment.createdAt = DateTime.tryParse(data.$createdAt)?.toLocal();
      return payment;
    }).toList();

    return payments;
  }
}
