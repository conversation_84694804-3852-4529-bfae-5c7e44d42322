// import 'dart:async';
//
// import 'package:appwrite/appwrite.dart';
// import 'package:appwrite/models.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:mandob/models/invoice_model.dart';
// import 'package:mandob/models/user_model.dart';
//
// import '../appwrite_db/appwrite.dart';
// import '../appwrite_db/db_consts.dart';
//
// class InvoicesProvider extends ChangeNotifier {
//   // final ApiService _service = ApiService.create();
//
//   final UserModel? user;
//
//   InvoicesProvider({this.user});
//   List<InvoiceModel> _invoices = [];
//   List<InvoiceModel> get invoices => _invoices;
//
//   List<InvoiceModel> getFilteredInvoices(String storeId) {
//     return _invoices.where((element) => element.storeId == storeId).toList();
//   }
//
//   Future addInvoice(InvoiceModel invoice) async {
//     try {
//       debugPrint('addingInvoice ${invoice.toJson()}');
//       await AppwriteDB.createDocument(
//         collectionId: DbConsts.invoices,
//         data: invoice.toJson(),
//       );
//     } catch (e) {
//       debugPrint('addInvoiceError: $e');
//       rethrow;
//     }
//     notifyListeners();
//   }
//
//   Future<List<InvoiceModel>> fetchInvoices(
//       {bool? isSales,
//       bool? isReturned,
//       int? paginationLimit,
//       String? lastDocId}) async {
//     try {
//       final invoiceData = await AppwriteDB.listDocuments(
//         collectionId: DbConsts.invoices,
//         queries: [
//           if (isSales != null) Query.equal('isSales', isSales),
//           if (isReturned != null) Query.equal('isReturned', isReturned),
//           Query.orderDesc('createdAt'),
//           if (paginationLimit == null) Query.limit(100),
//           if (paginationLimit != null) Query.limit(paginationLimit),
//           if (paginationLimit != null) Query.cursorAfter(lastDocId!),
//         ],
//       );
//
//       if (paginationLimit == null) {
//         _invoices = await _handleDataAfterFetch(invoiceData);
//       } else {
//         _invoices.addAll(await _handleDataAfterFetch(invoiceData));
//       }
//
//       return _invoices;
//     } catch (e) {
//       debugPrint('gettingInvoicesError $e');
//
//       return [];
//     }
//   }
//
//   Future<List<InvoiceModel>> fetchReportInvoices({
//     bool? isSales,
//     bool? isReturned,
//   }) async {
//     try {
//       _invoices = [];
//
//       if (_invoices.isEmpty) {
//         final data = await AppwriteDB.listDocuments(
//           collectionId: DbConsts.invoices,
//           queries: [
//             if (isSales != null) Query.equal('isSales', isSales),
//             if (isReturned != null) Query.equal('isReturned', isReturned),
//             Query.orderDesc('createdAt'),
//             Query.limit(100),
//           ],
//         );
//
//         for (var element in data.documents) {
//           final invoice = InvoiceModel.fromJson(element.data);
//           invoice.docId = element.$id;
//           invoice.invoiceId = element.$id;
//
//           _invoices.add(invoice);
//         }
//       }
//
//       for (var i = 0; i < 300; i += 100) {
//         final data = await AppwriteDB.listDocuments(
//           collectionId: DbConsts.invoices,
//           queries: [
//             if (isSales != null) Query.equal('isSales', isSales),
//             if (isReturned != null) Query.equal('isReturned', isReturned),
//             Query.orderDesc('createdAt'),
//             Query.limit(100),
//             if (i != 0) Query.offset(i + 100),
//           ],
//         );
//
//         final List<InvoiceModel> addedInvoices =
//             await _handleDataAfterFetch(data);
//
//         if (addedInvoices.isEmpty) break;
//         _invoices.addAll(addedInvoices);
//       }
//
//       debugPrint('adddeLLLen ${_invoices.length}');
//
//       return _invoices;
//     } catch (e) {
//       debugPrint('gettingInvoicesError $e');
//
//       return [];
//     }
//   }
//
//   FutureOr<List<InvoiceModel>> _handleDataAfterFetch(DocumentList data) {
//     final resData = data.documents;
//
//     final invoices = resData.map((data) {
//       final InvoiceModel invoice = InvoiceModel.fromJson(data.data);
//       invoice.docId = data.$id;
//       invoice.invoiceId = data.$id;
//
//       return invoice;
//     }).toList();
//
//     final mandobId = user!.uid;
//
//     final mandobInvoices =
//         invoices.where((element) => element.mandobId == mandobId).toList();
//
//     return user!.isAdmin! ? invoices : mandobInvoices;
//   }
//
//   Future<void> deleteInvoice(String invoiceId) async {
//     try {
//       debugPrint('deletingInvoice $invoiceId');
//       await AppwriteDB.deleteDocument(
//           documentId: invoiceId, collectionId: DbConsts.invoices);
//     } catch (e) {
//       debugPrint('deleteInvoiceError: $e');
//     }
//     notifyListeners();
//   }
// }

import 'dart:async';
import 'dart:developer';

import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart';
import 'package:flutter/cupertino.dart';
import 'package:mandob/models/invoice_model.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/pages/reports_page/components/print/print_best_sellers_products_pdf.dart';

import '../appwrite_db/appwrite.dart';
import '../appwrite_db/db_consts.dart';

class ReportsProvider extends ChangeNotifier {
  final UserModel? user;

  ReportsProvider({this.user});

  List<InvoiceModel> _invoices = [];

  List<InvoiceModel> get invoices => _invoices;

  //? Fetching Report Invoices
  Future<List<InvoiceModel>> fetchReportInvoices({
    BuildContext? context,
    bool isSales = true,
    bool isReturned = false,
    DateTime? startDate,
    DateTime? endDate,
    bool notifyListener = true,
    String? customerId,
  }) async {
    try {
      _invoices = [];

      log('Report_Filters: isSales: $isSales, isReturned: $isReturned, startDate: $startDate, endDate: $endDate, customerId: $customerId');
      for (var i = 0; i < 5000; i += 100) {
        log('START_DATE: $startDate');
        log('END_DATE: $endDate');

        final data = await AppwriteDB.listDocuments(
          collectionId: DbConsts.invoices,
          queries: [
            Query.equal('isSales', isSales),
            Query.equal('isReturned', isReturned),
            Query.orderDesc('createdAt'),
            if (startDate != null && endDate != null)
              Query.greaterThanEqual('createdAt', startDate.toIso8601String()),
            if (startDate != null && endDate != null)
              Query.lessThanEqual('createdAt', endDate.toIso8601String()),
            Query.limit(100),
            Query.equal('mandobId', user!.uid),
            if (customerId != null) Query.equal('customerId', customerId),
          ],
        );

        List<InvoiceModel> addedInvoices = [];

        //? If context is not null then we are fetching for best products sellers report
        //? And context to show pdf dialog
        if (context != null) {
          _products.clear();
          await _handleDataAfterFetchReportProducts(context, data);
        } else {
          addedInvoices = await _handleDataAfterFetchFromReports(data);
        }

        log('AddedInvoices ${addedInvoices.length}');
        if (addedInvoices.isEmpty) break;
        _invoices.addAll(addedInvoices);
      }

      if (context != null) {
        List sortedProducts = _products;

        //? Sort With Name And Quantity And Price
        sortedProducts.sort((a, b) {
          if (b['name'] == a['name']) {
            if (b['quantity'] == a['quantity']) {
              return b['price'].compareTo(a['price']);
            }
            return b['quantity'].compareTo(a['quantity']);
          }
          return b['name'].compareTo(a['name']);
        });

        await generateBestSellersPdf(context, sortedProducts);
      }

      log('asffsaf ${_invoices.length}');

      return _invoices;
    } catch (e) {
      debugPrint('gettingInvoicesError $e');

      return [];
    }
  }

  final List<Map> _products = [];

  // final Map _products = {};

  //! Handle Data After Fetch From Reports
  FutureOr<List<InvoiceModel>> _handleDataAfterFetchFromReports(
      DocumentList data) {
    final resData = data.documents;

    final invoices = resData.map((data) {
      final InvoiceModel invoice = InvoiceModel.fromJson(data.data);
      invoice.docId = data.$id;
      invoice.invoiceId = data.$id;

      //? Check if invoice is already added to list
      final duplicateInvoice = _invoices.firstWhere(
          (element) => element.invoiceId == invoice.invoiceId,
          orElse: () => InvoiceModel.empty());

      if (duplicateInvoice != InvoiceModel.empty()) {
        return InvoiceModel.empty();
      } else {
        return invoice;
      }
    }).toList();

    final mandobId = user!.uid;

    final mandobInvoices =
        invoices.where((element) => element.mandobId == mandobId).toList();

    return (user!.isAdmin! ? invoices : mandobInvoices)
        .where((element) => element != InvoiceModel.empty())
        .toList();
  }

  //! Handle Data After Fetch Report Products
  FutureOr<List<InvoiceModel>> _handleDataAfterFetchReportProducts(
      BuildContext context, DocumentList data) async {
    final resData = data.documents;

    final invoices = _mappingBestSellerProducts(resData);

    final mandobId = user!.uid;

    final mandobInvoices =
        invoices.where((element) => element.mandobId == mandobId).toList();

    return (user!.isAdmin! ? invoices : mandobInvoices)
        .where((element) => element != InvoiceModel.empty())
        .toList();
  }

  //! Mapping Best Seller Products
  List<InvoiceModel> _mappingBestSellerProducts(List<Document> resData) {
    return resData.map((data) {
      final InvoiceModel invoice = InvoiceModel.fromJson(data.data);
      invoice.docId = data.$id;
      invoice.invoiceId = data.$id;

      //! check duplicate invoices --------------------
      // final duplicateInvoice = _invoices.firstWhere(
      //     (element) => element.invoiceId == invoice.invoiceId,
      //     orElse: () => InvoiceModel.empty());

      for (var prod in invoice.products!) {
        //? If it is not in the map then add it to the map
        _products.add({
          'id': prod['id'],
          'name': prod['name'],
          'quantity': prod['quantity'],
          'price': prod['price'],
        });

        log('products: $_products');

        // //? Check if the product id is already in the _products Map
        // if (_products.any((element) => element['id'] == prod.id)) {
        //   //? If it is already in the map then add the quantity to the old quantity
        //   final index = _products.indexWhere((element) => element['id'] == prod.id);
        //   _products[index]['quantity'] += prod.quantity!;
        // } else {
      }
      // }
      return invoice;

      //? If Invoice is not duplicated then add it to the list or return empty
      // if (duplicateInvoice != InvoiceModel.empty()) {
      //   return InvoiceModel.empty();
      // } else {
      // }
    }).toList();
  }

// List<InvoiceModel> _mappingBestSellerProducts(List<Document> resData) {
//   return resData.map((data) {
//     final InvoiceModel invoice = InvoiceModel.fromJson(data.data);
//     invoice.docId = data.$id;
//     invoice.invoiceId = data.$id;
//
//     //! check duplicate invoices --------------------
//     final duplicateInvoice = _invoices.firstWhere(
//         (element) => element.invoiceId == invoice.invoiceId,
//         orElse: () => InvoiceModel.empty());
//
//     for (var prod in invoice.products!) {
//       //? Check if the product id is already in the _products Map
//       if (_products.containsKey(prod['id'])) {
//         _products[prod['id']]['quantity'] += prod['quantity'];
//       } else {
//         _products[prod['id']] = {
//           'id': prod['id'],
//           'name': prod['name'],
//           'quantity': prod['quantity'],
//         };
//       }
//     }
//
//     //? If Invoice is not duplicated then add it to the list or return empty
//     if (duplicateInvoice != InvoiceModel.empty()) {
//       return InvoiceModel.empty();
//     } else {
//       return invoice;
//     }
//   }).toList();
// }
}
