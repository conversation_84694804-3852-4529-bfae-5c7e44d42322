import 'package:appwrite/appwrite.dart';
import 'package:flutter/cupertino.dart';
import 'package:mandob/models/repository_model.dart';

import '../appwrite_db/appwrite.dart';
import '../appwrite_db/db_consts.dart';

class RepositoryProvider extends ChangeNotifier {
  List<RepositoryModel> _repositories = [];
  List<RepositoryModel> get repositories => _repositories;

  Future<void> createRepository(RepositoryModel repo) async {
    try {
      await AppwriteDB.createDocument(
          collectionId: DbConsts.repositories, data: repo.toJson());

      await fetchRepositories();
    } catch (e) {
      debugPrint('addReposError: $e');
    }

    notifyListeners();
  }

  Future<void> editRepository(String id, RepositoryModel repo) async {
    try {
      await AppwriteDB.updateDocument(
          documentId: id,
          collectionId: DbConsts.repositories,
          data: repo.toJson());

      await fetchRepositories();
    } catch (e) {
      debugPrint('editReposError: $e');
      return await editRepository(id, repo);
    }
    notifyListeners();
  }

  Future<List<RepositoryModel>> fetchRepositories() async {
    try {
      _repositories = await AppwriteDB.listDocuments(
          collectionId: DbConsts.repositories,
          queries: [
            Query.limit(100),
          ]).then((value) {
        final resData = value.documents;
        final products = resData.map((data) {
          final repos = RepositoryModel.fromJson(data.data);
          repos.id = data.$id;
          repos.createdAt = data.$createdAt;
          return repos;
        }).toList();
        return products;
      });
    } catch (error) {
      debugPrint('Error in fetchRepos $error');
    }

    return _repositories;
  }

  Future<void> deleteRepository(String id) async {
    try {
      await AppwriteDB.deleteDocument(
          documentId: id, collectionId: DbConsts.repositories);
    } catch (e) {
      debugPrint('deleteRepo error: $e');
    }
    notifyListeners();
  }
}
