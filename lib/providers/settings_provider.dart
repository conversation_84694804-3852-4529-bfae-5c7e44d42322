import 'dart:async';

import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class AppSettingsController extends ChangeNotifier {
  AppSettingsController() {
    loadSettings();
  }

  Locale _locale = const Locale('ar', 'EG');

  Locale get locale => _locale;

  bool get isEnglish => _locale.languageCode == 'en';

  Future<void> loadSettings() async {
    final langCode = await GetStorageService.getData(key: LocalKeys.language);
    if (langCode != null) {
      _locale = Locale(langCode);
    } else {
      _locale = const Locale('ar', 'EG');
    }
    notifyListeners();
  }

  Future<void> updateLanguage(Locale newLocale) async {
    if (_locale == newLocale) return;
    _locale = newLocale;
    await GetStorageService.setData(
        key: LocalKeys.language, value: newLocale.languageCode);
    notifyListeners();
  }
}
