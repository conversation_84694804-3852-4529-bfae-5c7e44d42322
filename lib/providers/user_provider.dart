import 'dart:convert';
import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:mandob/appwrite_db/appwrite.dart';
import 'package:mandob/appwrite_db/db_consts.dart';
import 'package:mandob/models/user_model.dart';
import 'package:mandob/utils/shared_preferences.dart';
import 'package:xr_helper/xr_helper.dart';

class UserProvider extends ChangeNotifier {
  List<UserModel>? _users;

  List<UserModel>? get users => _users;
  UserModel? _activeUser;

  UserModel? get activeUser => _activeUser;

  bool _isLoading = false;

  bool get isLoading => _isLoading;

  Future createUser({name, email, password, phone, storeId}) async {
    try {
      final userData = await AppwriteDB.register(
          email: email, password: password, name: name);

      final user = UserModel.fromJson({
        "uid": userData.$id,
        "email": email,
        "name": name,
        "phone": phone,
        "storeId": storeId,
        "password": password,
        "isAdmin": false
      });

      await _addUser(user);
    } catch (e) {
      debugPrint('addUserError: $e');
    }
  }

  Future loginUser({email, password}) async {
    try {
      final loginData =
          await AppwriteDB.login(email: email, password: password);

      _activeUser = await AppwriteDB.getDocument(
        collectionId: DbConsts.users,
        documentId: loginData.userId,
      ).then((value) => UserModel.fromJson(value.data));

      debugPrint('loginData: ${_activeUser!.toJson()}');

      final locData = jsonEncode(_activeUser!.toJson());

      await saveString(key: "userData", value: locData);

      log('asffffasff ${loadString(key: "userData")}');

      notifyListeners();
    } catch (e) {
      debugPrint('loginError: $e');
      rethrow;
    }
  }

  // get current user from appwrite
  Future getCurrentUser() async {
    try {
      _activeUser = await AppwriteDB.getDocument(
        collectionId: DbConsts.users,
        documentId: _activeUser!.uid.toString(),
      ).then((value) => UserModel.fromJson(value.data));

      debugPrint('loginData: ${_activeUser!.toJson()}');

      final locData = jsonEncode(_activeUser!.toJson());

      await saveString(key: "userData", value: locData);

      notifyListeners();
    } catch (e) {
      debugPrint('loginError: $e');
      rethrow;
    }
  }

  Future fetchUsers({
    bool withLoading = false,
  }) async {
    if (withLoading) {
      _isLoading = true;
      notifyListeners();
    }
    final usersData = await AppwriteDB.listDocuments(
      collectionId: DbConsts.users,
    );

    _users = usersData.documents
        .map((e) => UserModel.fromJson(e.data))
        .toList()
        .cast<UserModel>();

    if (withLoading) {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future editUser(UserModel user) => _addUser(user, isEdit: true);

  Future _addUser(UserModel user, {bool isEdit = false}) async {
    try {
      if (isEdit) {
        await AppwriteDB.updateDocument(
          collectionId: DbConsts.users,
          documentId: user.uid!,
          data: user.toJson(),
        );
      } else {
        await AppwriteDB.createDocumentWithId(
            documentId: user.uid!,
            collectionId: DbConsts.users,
            data: user.toJson());
      }

      fetchUsers();
    } catch (e) {
      debugPrint('addUserError: $e');
    }

    notifyListeners();
  }

  Future<void> deleteUser(UserModel user) async {
    try {
      final dUser = user;
      _users!.remove(dUser);

      await AppwriteDB.deleteDocument(
        collectionId: DbConsts.users,
        documentId: dUser.uid!,
      );
      notifyListeners();
    } catch (e) {
      debugPrint('deleteUserError: $e');
    }
  }

  Future<void> autoLog() async {
    Log.w('autoLogUser: ${_activeUser?.toJson()}');
    final locData = loadString(key: "userData");
    if (locData != null) {
      final userData = jsonDecode(locData);
      _activeUser = UserModel.fromJson(userData);
      Log.w('autoLogUserrrrr: ${_activeUser!.toJson()}');
      notifyListeners();
    }
  }

  logOut() async {
    try {
      await GetStorageService.removeKey(key: "userData");
      _activeUser = null;
      notifyListeners();
    } catch (e) {
      debugPrint('logOutError: $e');
    }
  }
}
