// import  /'package:drago_blue_printer/drago_blue_printer.dart';
// import 'package:flutter/material.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class PrinterVM extends ChangeNotifier {
//   BluetoothDevice? _printer;
//
//   BluetoothDevice? get printer => _printer;
//
//   bool _isLoading = false;
//
//   bool get isLoading => _isLoading;
//
//   Future<void> fetchPrinters() async {
//     _isLoading = true;
//     notifyListeners();
//
//     final printersData = await GetStorageService.getData(
//       key: LocalKeys.printer,
//     );
//
//     if (printersData != null) {
//       _printer = BluetoothDevice.fromMap(printersData);
//     }
//
//     _isLoading = false;
//     notifyListeners();
//   }
//
//   Future<void> addPrinter({required BluetoothDevice printer}) async {
//     _isLoading = true;
//     notifyListeners();
//
//     await GetStorageService.setData(
//       key: LocalKeys.printer,
//       value: printer.toMap(),
//     );
//
//     await fetchPrinters();
//     _isLoading = false;
//     notifyListeners();
//
//   }
//
// }
