import 'package:flutter/cupertino.dart';
import 'package:mandob/appwrite_db/db_consts.dart';
import 'package:mandob/models/supplier_model.dart';

import '../appwrite_db/appwrite.dart';

class SuppliersProvider extends ChangeNotifier {
  static const _suppliersCollectionId = DbConsts.suppliers;

  bool _isLoading = false;

  bool get isLoading => _isLoading;

  void setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  List<SupplierModel> _suppliers = [];

  List<SupplierModel> get suppliers => _suppliers;

  Future addSupplier(SupplierModel supplier) async {
    try {
      await AppwriteDB.createDocument(
          collectionId: _suppliersCollectionId, data: supplier.toJson());

      await fetchSuppliers();

      notifyListeners();
    } catch (e) {
      debugPrint('addSupplierError $e');
    }
  }

  Future editSupplier(String id, SupplierModel supplier) async {
    try {
      await AppwriteDB.updateDocument(
          collectionId: _suppliersCollectionId,
          documentId: id,
          data: supplier.toJson());

      await fetchSuppliers();

      notifyListeners();
    } catch (e) {
      debugPrint('editSupplierError $e');
    }
  }

  Future fetchSuppliers({
    bool forceLoad = false,
  }) async {
    if (_suppliers.isNotEmpty && !forceLoad) return;

    _suppliers = [];

    setLoading(true);

    final res =
        await AppwriteDB.listDocuments(collectionId: _suppliersCollectionId);

    final resData = res.documents;

    final suppliers = resData.map((data) {
      final SupplierModel supplier = SupplierModel.fromJson(data.data);
      supplier.id = data.$id;
      return supplier;
    }).toList();

    _suppliers = suppliers;

    setLoading(false);
  }

  Future<void> deleteSupplier(String id) async {
    try {
      await AppwriteDB.deleteDocument(
          collectionId: _suppliersCollectionId, documentId: id);

      _suppliers.removeWhere((element) => element.id == id);
    } catch (e) {
      debugPrint('addCustomerError $e');
    }
    notifyListeners();
  }
}
