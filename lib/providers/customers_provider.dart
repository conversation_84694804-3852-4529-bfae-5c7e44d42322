import 'dart:async';
import 'dart:developer';

import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart';
import 'package:flutter/cupertino.dart';
import 'package:mandob/appwrite_db/appwrite.dart';
import 'package:mandob/appwrite_db/db_consts.dart';
import 'package:mandob/models/customer_model.dart';
import 'package:mandob/models/user_model.dart';

class CustomersProvider extends ChangeNotifier {
  final UserModel? user;

  CustomersProvider({this.user});

  List<CustomerModel> _allCustomers = [];

  List<CustomerModel> get allCustomers => _allCustomers;

  List<CustomerModel> _mandobCustomers = [];

  List<CustomerModel> get mandobCustomers => _mandobCustomers;

  bool _isLoading = false;

  bool get isLoading => _isLoading;

  void setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  static const _customerCollectionId = DbConsts.customer;

  Future addCustomer(CustomerModel customer) async {
    try {
      debugPrint('addingCustomer ${customer.toJson()}');

      await AppwriteDB.createDocument(
          collectionId: _customerCollectionId, data: customer.toJson());

      notifyListeners();
    } catch (e) {
      debugPrint('addCustomerError $e');
    }
  }

  //search customers
  Future<List<CustomerModel>> searchCustomers(String query) async {
    try {
      final data = await AppwriteDB.listDocuments(
        collectionId: _customerCollectionId,
        queries: [
          Query.search('customerName', query),
        ],
      );

      log('searchCustomers ${data.documents}');

      final List<CustomerModel> addedCustomers =
          await _handleDataAfterFetch(data);

      return addedCustomers;
    } catch (e) {
      debugPrint('searchCustomersError $e');
      return [];
    }
  }

  Future editCustomer(String docId, CustomerModel customer) async {
    try {
      await AppwriteDB.updateDocument(
          collectionId: _customerCollectionId,
          documentId: docId,
          data: customer.toJson());

      notifyListeners();
    } catch (e) {
      debugPrint('editCustomerError $e');
    }
  }

  Future editDebit(String id, double? debit) async {
    await AppwriteDB.updateDocument(
        collectionId: _customerCollectionId,
        documentId: id,
        data: {'debit': debit});
    notifyListeners();
  }

  Future<void> fetchAllCustomers({
    bool forceLoad = false,
  }) async {
    if (_allCustomers.isNotEmpty && !forceLoad) return;

    try {
      _allCustomers = [];
      _mandobCustomers = [];

      setLoading(true);

      final Map<String, CustomerModel> allCustomersMap = {};
      final Map<String, CustomerModel> mandobCustomersMap = {};

      if (_allCustomers.isEmpty && _mandobCustomers.isEmpty) {
        final data = await AppwriteDB.listDocuments(
          collectionId: _customerCollectionId,
          queries: [Query.limit(100), Query.equal('mandobId', user!.uid)],
        );

        for (var element in data.documents) {
          final customer = CustomerModel.fromJson(element.data);
          customer.docId = element.$id;
          customer.id = element.data['customerId'] == null ||
                  element.data['customerId'] == ''
              ? element.$id
              : element.data['customerId'];

          allCustomersMap[customer.id!] = customer;
          if (customer.mandobId == user!.uid) {
            mandobCustomersMap[customer.id!] = customer;
          }
        }
      }

      for (var i = 0; i < 5000; i += 100) {
        debugPrint('fetchingCustomers $i');
        final data = await AppwriteDB.listDocuments(
          collectionId: _customerCollectionId,
          queries: [
            Query.limit(100),
            if (i != 0) Query.offset(i + 100),
            Query.equal('mandobId', user!.uid)
          ],
        );

        final List<CustomerModel> addedCustomers =
            await _handleDataAfterFetch(data);
        if (addedCustomers.isEmpty) break;
        final filteredCustomers =
            addedCustomers.where((element) => element.mandobId == user!.uid!);

        for (var customer in addedCustomers) {
          allCustomersMap[customer.id!] = customer;
        }
        for (var customer in filteredCustomers) {
          mandobCustomersMap[customer.id!] = customer;
        }
      }

      _allCustomers = allCustomersMap.values.toList();
      _mandobCustomers = mandobCustomersMap.values.toList();

      setLoading(false);

      debugPrint('customersFetched ${_allCustomers.length}');
      debugPrint('mandobCustomers ${_mandobCustomers.length}');
    } catch (e) {
      debugPrint('gettingCustomersError $e');
    }
  }

  // Future<void> fetchAllCustomers({
  //   bool forceLoad = false,
  // }) async {
  //   if (_allCustomers.isNotEmpty && !forceLoad) return;
  //   try {
  //     _allCustomers = [];
  //     _mandobCustomers = [];
  //
  //     setLoading(true);
  //
  //     final Set<CustomerModel> allCustomersSet = {};
  //     final Set<CustomerModel> mandobCustomersSet = {};
  //
  //     if (_allCustomers.isEmpty && _mandobCustomers.isEmpty) {
  //       final data = await AppwriteDB.listDocuments(
  //         collectionId: _customerCollectionId,
  //         queries: [Query.limit(100), Query.equal('mandobId', user!.uid)],
  //       );
  //
  //       for (var element in data.documents) {
  //         final customer = CustomerModel.fromJson(element.data);
  //         customer.docId = element.$id;
  //         customer.id = element.data['customerId'] == null ||
  //                 element.data['customerId'] == ''
  //             ? element.$id
  //             : element.data['customerId'];
  //
  //         allCustomersSet.add(customer);
  //         if (customer.mandobId == user!.uid) {
  //           mandobCustomersSet.add(customer);
  //         }
  //       }
  //     }
  //
  //     for (var i = 0; i < 5000; i += 100) {
  //       debugPrint('fetchingCustomers $i');
  //       final data = await AppwriteDB.listDocuments(
  //         collectionId: _customerCollectionId,
  //         queries: [
  //           Query.limit(100),
  //           if (i != 0) Query.offset(i + 100),
  //           Query.equal('mandobId', user!.uid)
  //         ],
  //       );
  //
  //       final List<CustomerModel> addedCustomers =
  //           await _handleDataAfterFetch(data);
  //       if (addedCustomers.isEmpty) break;
  //       final filteredCustomers =
  //           addedCustomers.where((element) => element.mandobId == user!.uid!);
  //
  //       allCustomersSet.addAll(addedCustomers);
  //       mandobCustomersSet.addAll(filteredCustomers);
  //     }
  //
  //     _allCustomers = allCustomersSet.toList();
  //     _mandobCustomers = mandobCustomersSet.toList();
  //
  //     setLoading(false);
  //
  //     debugPrint('customersFetched ${_allCustomers.length}');
  //     debugPrint('mandobCustomers ${_mandobCustomers.length}');
  //   } catch (e) {
  //     debugPrint('gettingCustomersError $e');
  //   }
  // }
  // Future<void> fetchAllCustomers({
  //   bool forceLoad = false,
  // }) async {
  //   if (_allCustomers.isNotEmpty && !forceLoad) return;
  //   try {
  //     _allCustomers = [];
  //     _mandobCustomers = [];
  //
  //     setLoading(true);
  //
  //     if (_allCustomers.isEmpty && _mandobCustomers.isEmpty) {
  //       final data = await AppwriteDB.listDocuments(
  //         collectionId: _customerCollectionId,
  //         queries: [Query.limit(100)],
  //       );
  //
  //       for (var element in data.documents) {
  //         final customer = CustomerModel.fromJson(element.data);
  //         customer.docId = element.$id;
  //         customer.id = element.data['customerId'] == null ||
  //                 element.data['customerId'] == ''
  //             ? element.$id
  //             : element.data['customerId'];
  //
  //         _allCustomers.add(customer);
  //         if (customer.mandobId == user!.uid) {
  //           _mandobCustomers.add(customer);
  //         }
  //       }
  //     }
  //
  //     for (var i = 0; i < 5000; i += 100) {
  //       debugPrint('fetchingCustomers $i');
  //       final data = await AppwriteDB.listDocuments(
  //         collectionId: _customerCollectionId,
  //         queries: [Query.limit(100), if (i != 0) Query.offset(i + 100)],
  //       );
  //
  //       final List<CustomerModel> addedCustomers =
  //           await _handleDataAfterFetch(data);
  //       if (addedCustomers.isEmpty) break;
  //       final filteredCustomers =
  //           addedCustomers.where((element) => element.mandobId == user!.uid!);
  //
  //       _allCustomers.addAll(addedCustomers);
  //       _mandobCustomers.addAll(filteredCustomers);
  //     }
  //
  //     _allCustomers = _allCustomers.toSet().toList();
  //     _mandobCustomers = _mandobCustomers.toSet().toList();
  //
  //     setLoading(false);
  //
  //     debugPrint('customersFetched ${_allCustomers.length}');
  //     debugPrint('mandobCustomers ${_mandobCustomers.length}');
  //
  //     // debugPrint('customersFetchedMMM ${_mandobCustomers.length}');
  //   } catch (e) {
  //     debugPrint('gettingCustomersError $e');
  //   }
  // }

  FutureOr<List<CustomerModel>> _handleDataAfterFetch(
      DocumentList customersData) {
    return customersData.documents.map((data) {
      final CustomerModel customers = CustomerModel.fromJson(data.data);
      customers.docId = data.$id;
      customers.id =
          data.data['customerId'] == null || data.data['customerId'] == ''
              ? data.$id
              : data.data['customerId'];

      return customers;
    }).toList();
  }

  Future<void> deleteCustomer(String id) async {
    try {
      await AppwriteDB.deleteDocument(
          collectionId: _customerCollectionId, documentId: id);

      _allCustomers.removeWhere((element) => element.id == id);
    } catch (e) {
      debugPrint('addCustomerError $e');
    }
    notifyListeners();
  }
}
