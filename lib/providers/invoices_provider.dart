import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import 'package:mandob/models/invoice_model.dart';
import 'package:mandob/models/user_model.dart';

import '../appwrite_db/appwrite.dart';
import '../appwrite_db/db_consts.dart';

class InvoicesProvider extends ChangeNotifier {
  final UserModel? user;

  InvoicesProvider({this.user});

  List<InvoiceModel> _invoices = [];

  List<InvoiceModel> get invoices => _invoices;

  //? Get Filtered Invoices (Local)
  List<InvoiceModel> getFilteredInvoices(String storeId) {
    return _invoices.where((element) => element.storeId == storeId).toList();
  }

  //? Adding Invoice
  Future<Document> addInvoice(InvoiceModel invoice) async {
    try {
      debugPrint('addingInvoice ${invoice.toJson()}');

      final doc = await AppwriteDB.createDocument(
        collectionId: DbConsts.invoices,
        data: invoice.toJson(),
      );

      notifyListeners();

      return doc;
    } catch (e) {
      debugPrint('addInvoiceError: $e');
      rethrow;
    }
  }

  //? Zakah Integration
  Future zakahIntegration(
    Map<String, dynamic> data, {
    required bool isRefund,
    required bool isSimplfiedInvoice,
  }) async {
    try {
      debugPrint('zakahData ${data}');

      final number = isRefund ? '1' : '0';

      final type = isSimplfiedInvoice ? 'Reporting' : 'Clearance';
      // Clearance or Reporting

      final res = await http.post(
        Uri.parse(
            'http://mandoob-app-zatca-int.opti4it.com/api/v2/upload-invoice/$type/$number'),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=utf-8',
          'Accept': 'application/json',
          'Api-Password': '4yYh5dNDpu^Y#FnJ',
        },
        body: jsonEncode(data),
      );
      log('zakahRESCDDD ${res.statusCode} RRR ${res.body}');
    } catch (e) {
      debugPrint('addInvoiceError: $e');
      rethrow;
    }
    notifyListeners();
  }

  //? Fetching Invoices
  Future<List<InvoiceModel>> fetchInvoices({
    bool? isSales,
    bool? isReturned,
    int? paginationLimit,
    String? lastDocId,
    String? customerId,
    String? userId,
  }) async {
    try {
      log('aasfaFFf $customerId');
      final invoiceData = await AppwriteDB.listDocuments(
        collectionId: DbConsts.invoices,
        queries: [
          if (isSales != null) Query.equal('isSales', isSales),
          if (isReturned != null) Query.equal('isReturned', isReturned),
          if (customerId != null && customerId.isNotEmpty)
            Query.equal('customerId', customerId),
          Query.orderDesc('createdAt'),
          if (paginationLimit == null) Query.limit(100),
          if (paginationLimit != null) Query.limit(paginationLimit),
          if (paginationLimit != null && lastDocId != null)
            Query.cursorAfter(lastDocId),
          Query.equal('mandobId', userId ?? user!.uid),
        ],
      );

      log('asdlksad ${invoiceData.documents.length}');

      if (paginationLimit == null) {
        _invoices = await _handleDataAfterFetch(invoiceData);
      } else {
        _invoices.addAll(await _handleDataAfterFetch(invoiceData));
      }

      return _invoices.where((element) => element.isSales == isSales).toList();
    } catch (e, s) {
      debugPrint('gettingInvoicesError $e\n$s');

      return [];
    }
  }

  void clearInvoices() {
    _invoices = [];
    notifyListeners();
  }

  num totalSales = 0;
  num totalPurchases = 0;

  Future<void> fetchTodayInvoices() async {
    try {
      log('fetchTodayInvoicesData');
      final invoiceData = await AppwriteDB.listDocuments(
        collectionId: DbConsts.invoices,
        queries: [
          // if (isSales != null) Query.equal('isSales', isSales),
          Query.equal('isReturned', false),
          Query.equal('mandobId', user!.uid),
          Query.orderDesc('createdAt'),
          Query.limit(100),
          Query.greaterThan(
              'createdAt',
              DateTime.now()
                  .subtract(const Duration(days: 1))
                  .toIso8601String())
        ],
      );

      _invoices = await _handleDataAfterFetch(invoiceData);

      totalSales = _invoices
          .where((element) => element.isSales == true)
          .map((e) => e.totalPrice)
          .fold(0, (previousValue, element) => previousValue + (element ?? 0));

      totalPurchases = _invoices
          .where((element) => element.isSales == false)
          .map((e) => e.totalPrice)
          .fold(0, (previousValue, element) => previousValue + (element ?? 0));

      notifyListeners();
    } catch (e) {
      debugPrint('gettingInvoicesTotalError $e');
    }
  }

  //? Handle Invoices After Fetching (Filter admin or mandob) and convert to model
  FutureOr<List<InvoiceModel>> _handleDataAfterFetch(DocumentList data) {
    final resData = data.documents;

    final invoices = resData.map((data) {
      final InvoiceModel invoice = InvoiceModel.fromJson(data.data);
      invoice.docId = data.$id;
      invoice.invoiceId = data.$id;

      return invoice;
    }).toList();

    final mandobId = user!.uid;

    final mandobInvoices =
        invoices.where((element) => element.mandobId == mandobId).toList();

    return (user!.isAdmin! ? invoices : mandobInvoices).toList();
  }

  //? Delete Invoice (Only Admin)
  Future<void> deleteInvoice(String invoiceId) async {
    try {
      debugPrint('deletingInvoice $invoiceId');
      await AppwriteDB.deleteDocument(
          documentId: invoiceId, collectionId: DbConsts.invoices);
    } catch (e) {
      debugPrint('deleteInvoiceError: $e');
    }
    notifyListeners();
  }
}
