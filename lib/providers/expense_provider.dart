import 'dart:async';

import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart';
import 'package:flutter/cupertino.dart';
import 'package:mandob/models/expense_model.dart';

import '../appwrite_db/appwrite.dart';
import '../appwrite_db/db_consts.dart';
// import 'package:mandob/models/operation_model.dart';

class ExpensesProvider extends ChangeNotifier {
  List<ExpenseModel>? _expenses = [];

  List<ExpenseModel>? get expenses => _expenses;

  Future createExpense(ExpenseModel op) async {
    try {
      await AppwriteDB.createDocument(
          // documentId: op.id!,
          collectionId: DbConsts.expenses,
          data: op.toJson());
    } catch (e) {
      debugPrint('addExpenseError: $e');
    }

    notifyListeners();
  }

  // Future fetchExpenses({
  //   String? userId,
  //   String? type,
  //   DateTime? startDate,
  //   DateTime? endDate,
  // }) async {
  //   try {
  //     _expenses = [];
  //
  //     if (_expenses!.isEmpty) {
  //       final data = await AppwriteDB.listDocuments(
  //         collectionId: DbConsts.expenses,
  //         queries: [
  //           Query.limit(100),
  //           Query.orderDesc('\$createdAt'),
  //           if (startDate != null && endDate != null)
  //             Query.greaterThan('\$createdAt', startDate.toIso8601String()),
  //           if (startDate != null && endDate != null)
  //             Query.lessThan('\$createdAt', endDate.toIso8601String()),
  //           if (userId != null) Query.equal('mandobId', userId),
  //           if (type != null) Query.equal('source', type),
  //         ],
  //       );
  //
  //       for (var element in data.documents) {
  //         final expense = ExpenseModel.fromJson(element.data);
  //         expense.id = element.$id;
  //         expense.createdAt = DateTime.tryParse(element.$createdAt);
  //
  //         _expenses!.add(expense);
  //       }
  //     }
  //
  //     for (var i = 0; i < 5000; i += 100) {
  //       final data = await AppwriteDB.listDocuments(
  //         collectionId: DbConsts.expenses,
  //         queries: [
  //           Query.limit(100),
  //           if (i != 0) Query.offset(i + 100),
  //           Query.orderDesc('\$createdAt'),
  //           if (startDate != null && endDate != null)
  //             Query.greaterThan('\$createdAt', startDate.toIso8601String()),
  //           if (startDate != null && endDate != null)
  //             Query.lessThan('\$createdAt', endDate.toIso8601String()),
  //           if (userId != null) Query.equal('mandobId', userId),
  //           if (type != null) Query.equal('source', type),
  //         ],
  //       );
  //
  //       final List<ExpenseModel> addedExpenses =
  //           await _handleDataAfterFetch(data);
  //
  //       if (addedExpenses.isEmpty) break;
  //       _expenses!.addAll(addedExpenses);
  //     }
  //   } catch (error) {
  //     debugPrint('Error in fetchExpenses $error');
  //   }
  // }
  Future fetchExpenses({
    String? userId,
    String? type,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      _expenses = [];
      final Set<String> expenseIds = {};

      if (_expenses!.isEmpty) {
        final data = await AppwriteDB.listDocuments(
          collectionId: DbConsts.expenses,
          queries: [
            Query.limit(100),
            Query.orderDesc('\$createdAt'),
            if (startDate != null && endDate != null)
              Query.greaterThan('\$createdAt', startDate.toIso8601String()),
            if (startDate != null && endDate != null)
              Query.lessThan('\$createdAt', endDate.toIso8601String()),
            if (userId != null) Query.equal('mandobId', userId),
            if (type != null) Query.equal('source', type),
          ],
        );

        for (var element in data.documents) {
          final expense = ExpenseModel.fromJson(element.data);
          expense.id = element.$id;
          expense.createdAt = DateTime.tryParse(element.$createdAt);

          if (!expenseIds.contains(expense.id)) {
            expenseIds.add(expense.id!);
            _expenses!.add(expense);
          }
        }
      }

      for (var i = 0; i < 5000; i += 100) {
        final data = await AppwriteDB.listDocuments(
          collectionId: DbConsts.expenses,
          queries: [
            Query.limit(100),
            if (i != 0) Query.offset(i + 100),
            Query.orderDesc('\$createdAt'),
            if (startDate != null && endDate != null)
              Query.greaterThan('\$createdAt', startDate.toIso8601String()),
            if (startDate != null && endDate != null)
              Query.lessThan('\$createdAt', endDate.toIso8601String()),
            if (userId != null) Query.equal('mandobId', userId),
            if (type != null) Query.equal('source', type),
          ],
        );

        final List<ExpenseModel> addedExpenses =
            await _handleDataAfterFetch(data);

        if (addedExpenses.isEmpty) break;

        for (var expense in addedExpenses) {
          if (!expenseIds.contains(expense.id)) {
            expenseIds.add(expense.id!);
            _expenses!.add(expense);
          }
        }
      }
    } catch (error) {
      debugPrint('Error in fetchExpenses $error');
    }
  }

  FutureOr<List<ExpenseModel>> _handleDataAfterFetch(DocumentList data) {
    final resData = data.documents;

    final expenses = resData.map((data) {
      final ExpenseModel expense = ExpenseModel.fromJson(data.data);
      expense.id = data.$id;
      expense.createdAt = DateTime.tryParse(data.$createdAt);
      return expense;
    }).toList();

    return expenses;
  }
}
