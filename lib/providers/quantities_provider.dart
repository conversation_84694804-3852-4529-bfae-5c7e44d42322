import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:mandob/models/quantity_model.dart';

import '../appwrite_db/appwrite.dart';
import '../appwrite_db/db_consts.dart';

class QuantitiesProvider extends ChangeNotifier {
  List<QuantityModel> _quantities = [];
  List<QuantityModel> get quantities => _quantities;

  Future<void> fetchQuantities() async {
    try {
      _quantities =
          await AppwriteDB.listDocuments(collectionId: DbConsts.quantities)
              .then((value) {
        final resData = value.documents;
        final quantities = resData.map((doc) {
          final QuantityModel quantity = QuantityModel.fromJson({
            'productId': doc.$id,
            'quantities': doc.data['quantities'],
          });

          return quantity;
        }).toList();
        return quantities;
      });
    } catch (error) {
      debugPrint('Error in fetchQuantities $error');
    }
  }

  Future setQuantities(QuantityModel quantityModel) async {
    try {
      await AppwriteDB.createDocumentWithId(
          documentId: quantityModel.productId!,
          collectionId: DbConsts.quantities,
          data: {
            'quantities': jsonEncode(quantityModel.quantities),
          });
    } catch (e) {
      debugPrint('setQuantityError: $e');
    }

    notifyListeners();
  }

  Future<void> modifyQuantity(
      {required String? productId,
      required String? storeId,
      required num? quantity}) async {
    try {
      debugPrint('modifyingQuantity $productId $storeId $quantity');
      if (_quantities.isEmpty) await fetchQuantities();
      final oldQuantities = _quantities
              .firstWhere((element) => element.productId == productId)
              .quantities ??
          {};

      final quantitiesMap = {
        ...oldQuantities,
        storeId: quantity,
      };

      final quantitiesString = json.encode(quantitiesMap);

      await AppwriteDB.updateDocument(
          documentId: productId!,
          collectionId: DbConsts.quantities,
          data: {
            'quantities': quantitiesString,
          });
    } catch (e) {
      debugPrint('modifyQuantityError: $e');
      return await modifyQuantity(
          productId: productId, storeId: storeId, quantity: quantity);
    }
    notifyListeners();
  }

  Future<void> deleteQuantity(String productId) async {
    try {
      await AppwriteDB.deleteDocument(
          documentId: productId, collectionId: DbConsts.quantities);
      _quantities.removeWhere((element) => element.productId == productId);
    } catch (e) {
      debugPrint('deleteQuantity error: $e');
    }
    notifyListeners();
  }
}
