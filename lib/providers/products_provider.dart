import 'dart:developer';

import 'package:appwrite/appwrite.dart';
import 'package:flutter/cupertino.dart';
import 'package:mandob/appwrite_db/appwrite.dart';
import 'package:mandob/appwrite_db/db_consts.dart';
import 'package:mandob/models/product_model.dart';

class ProductsProvider extends ChangeNotifier {
  List<ProductModel>? _products;
  List<ProductModel>? get products => _products;

  Future addProduct(ProductModel product) async {
    try {
      await AppwriteDB.createDocumentWithId(
          documentId: product.id!,
          collectionId: DbConsts.products,
          data: product.toJson());
    } catch (e) {
      debugPrint('addProduct error: $e');
    }
    notifyListeners();
  }

  Future<void> editProduct(ProductModel product) async {
    debugPrint(
        'editProductId: ${product.id!}\neditProduct: ${product.toJson()}');

    try {
      await AppwriteDB.updateDocument(
          documentId: product.id!,
          collectionId: DbConsts.products,
          data: product.toJson());
    } catch (e) {
      debugPrint('editProduct error: $e');
    }
    notifyListeners();
  }

  Future<void> fetchProducts({
    int? paginationLimit,
  }) async {
    try {
      final products = await AppwriteDB.listDocuments(
          collectionId: DbConsts.products,
          queries: [
            if (paginationLimit == null) Query.limit(100),
            if (paginationLimit != null) Query.limit(paginationLimit),
            if (paginationLimit != null) Query.cursorAfter(_products!.last.id!),
          ]).then((value) {
        log('value ${value.documents.length}');
        final resData = value.documents;

        final products = resData.map((data) {
          final ProductModel product = ProductModel.fromJson(data.data);
          product.id = data.$id;
          return product;
        }).toList();

        return products;
      });

      if (paginationLimit == null) {
        _products = products;
      } else {
        _products!.addAll(products);
      }
    } catch (error) {
      debugPrint('Error in fetchProducts $error');
    }
  }

  Future<void> deleteProduct(String id) async {
    try {
      await AppwriteDB.deleteDocument(
          documentId: id, collectionId: DbConsts.products);
    } catch (e) {
      debugPrint('deleteProductError: $e');
    }
    notifyListeners();
  }
}
