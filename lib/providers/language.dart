import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LangProvider with ChangeNotifier {

  var prefs = SharedPreferences.getInstance().then((a) {
    var s = a.getString('languageCode');
    _appLocale = Locale(s ?? 'ar');
  });

  static Locale _appLocale = const Locale('ar');


  setLangPref(l) async {
    var prefs = await SharedPreferences.getInstance();
    prefs.setString('languageCode', l);
  }



  getLangPref() async {
    var prefs = await SharedPreferences.getInstance();
    prefs.get('languageCode');
  }

  Locale get appLocal => _appLocale;

  String get lngSetLoc => _appLocale.languageCode;

  set lngSet(String appLoc) => appLoc = _appLocale.languageCode;

  void changeDirection({required String lang}) {
    if (_appLocale != Locale(lang)) {
      if (lang == "ar") {
        _appLocale = const Locale("ar");
        setLangPref("ar");
      } else {
        _appLocale = const Locale("en");
        setLangPref("en");
      }
    }

    notifyListeners();
  }
}