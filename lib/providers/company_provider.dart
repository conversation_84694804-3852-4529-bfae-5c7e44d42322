import 'package:flutter/foundation.dart';
import 'package:mandob/models/company_info_model.dart';

import '../appwrite_db/appwrite.dart';
import '../appwrite_db/db_consts.dart';

class CompanyInfoProvider extends ChangeNotifier {
  CompanyInfoModel? _companyInfo;

  CompanyInfoModel? get companyInfo => _companyInfo;

  Future addCompanyInfo(CompanyInfoModel comp) async {
    try {
      await AppwriteDB.updateDocument(
          documentId: DbConsts.companyInfo,
          collectionId: DbConsts.company,
          data: comp.toJson());
    } catch (e) {
      debugPrint('addCompanyError: $e');
    }

    await fetchCompanyInfo();
    notifyListeners();
  }

  Future<CompanyInfoModel?> fetchCompanyInfo() async {
    try {
      _companyInfo = await AppwriteDB.getDocument(
              collectionId: DbConsts.company, documentId: DbConsts.companyInfo)
          .then((value) {
        final resData = value.data;

        return CompanyInfoModel.fromJson(resData);
      });

      return _companyInfo;
    } catch (error) {
      debugPrint('fetchCompanyError $error');
      rethrow;
    }
  }
}
