import 'package:flutter/foundation.dart';
import 'package:mandob/models/transition_model.dart';

import '../appwrite_db/appwrite.dart';
import '../appwrite_db/db_consts.dart';

class TransitionsProvider extends ChangeNotifier {
  List<TransitionModel> _transitions = [];
  List<TransitionModel> get transitions => _transitions;

  Future addTransition(TransitionModel transition) async {
    try {
      await AppwriteDB.createDocument(
          collectionId: DbConsts.transitions, data: transition.toJson());

      await fetchTransition();
    } catch (e) {
      debugPrint('addTransitionError: $e');
    }

    notifyListeners();
  }

  Future<void> fetchTransition() async {
    try {
      _transitions =
          await AppwriteDB.listDocuments(collectionId: DbConsts.transitions)
              .then((value) {
        final resData = value.documents;
        final products = resData.map((data) {
          final repos = TransitionModel.fromJson(data.data);
          repos.id = data.$id;
          return repos;
        }).toList();
        return products;
      });
    } catch (error) {
      debugPrint('Error in fetchTransition $error');
    }
    return notifyListeners();
  }

  Future<void> deleteTransition(String id) async {
    try {
      await AppwriteDB.deleteDocument(
          documentId: id, collectionId: DbConsts.transitions);
      await fetchTransition();
    } catch (e) {
      debugPrint('deleteTransitionError: $e');
    }
    notifyListeners();
  }
}
