import 'dart:io';
import 'dart:typed_data';

import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart' as mdl;
import 'package:mandob/utils/shared_preferences.dart';
import 'package:mandob/utils/xid.dart';

import 'db_consts.dart';

class AppwriteDB {
  static late String databaseId;

  AppwriteDB.init() {
    // loadString(key: DbConsts.databaseIdPref).then((value) {
    //   if (value != null) {
    //     databaseId = value;
    //   }
    // });
    databaseId = loadString(key: DbConsts.databaseIdPref) ?? '';
  }

  static final Client client = Client()
      .setEndpoint(_host) //? Appwrite Endpoint
      .setProject(DbConsts.projectId) //? project ID
      .setSelfSigned();

  static final Databases _database = Databases(client);

  static final _account = Account(client);

  static final _storage = Storage(client);

  static const _host = 'https://app-write.opti4it.com:99/v1';

  static Future<mdl.DocumentList> listDocuments({
    required String collectionId,
    List<String>? queries,
  }) async {
    final data = await _database.listDocuments(
        databaseId: databaseId,
        collectionId: collectionId,
        queries: queries ?? [Query.limit(100)]);

    return data;
  }

  static Future<mdl.Document> getDocument({
    required String collectionId,
    required String documentId,
  }) async {
    return await _database.getDocument(
      documentId: documentId,
      databaseId: databaseId,
      collectionId: collectionId,
    );
  }

  static Future<mdl.Document> createDocument(
      {required String collectionId,
      required Map<String, dynamic> data}) async {
    return await _database.createDocument(
      documentId: generateId(),
      databaseId: databaseId,
      collectionId: collectionId,
      data: data,
    );
  }

  static Future<mdl.Document> createDocumentWithId(
      {required String collectionId,
      required String documentId,
      required Map<String, dynamic> data}) async {
    return await _database.createDocument(
      documentId: documentId,
      databaseId: databaseId,
      collectionId: collectionId,
      data: data,
    );
  }

  static Future<mdl.Document> updateDocument(
      {required String collectionId,
      required String documentId,
      required Map<String, dynamic> data}) async {
    return await _database.updateDocument(
      documentId: documentId,
      databaseId: databaseId,
      collectionId: collectionId,
      data: data,
    );
  }

  static Future<dynamic> deleteDocument(
      {required String collectionId, required String documentId}) async {
    return await _database.deleteDocument(
      documentId: documentId,
      databaseId: databaseId,
      collectionId: collectionId,
    );
  }

  static Future<mdl.Session> login(
      {required String email, required String password}) async {
    return await _account.createEmailSession(email: email, password: password);
  }

  static Future<mdl.User> register(
      {required String email,
      required String password,
      required String name}) async {
    return await _account.create(
      userId: generateId(),
      email: email,
      password: password,
      name: name,
    );
  }

  static Future<mdl.File> uploadFile({required File file}) async {
    return await _storage.createFile(
      file: InputFile(
        path: file.path,
        filename: file.path.split('/').last,
      ),
      bucketId: databaseId,
      fileId: generateId(),
    );
  }

  static Future<Uint8List> getFile({required String fileId}) async {
    return await _storage.getFileView(
      fileId: fileId,
      bucketId: databaseId,
    );
  }

// void main() { // Init SDK
//   Client client = Client();
//   Databases databases = Databases(client);
//
//   client
//       .setEndpoint('https://cloud.appwrite.io/v1') // Your API Endpoint
//       .setProject('5df5acd0d48c2') // Your project ID
//       .setKey('919c2d18fb5d4...a2ae413da83346ad2') // Your secret API key
//       ;
//
//   Future result = databases.create(
//     databaseId: '[DATABASE_ID]',
//     name: '[NAME]',
//   );
//
//   result
//       .then((response) {
//     print(response);
//   }).catchError((error) {
//     print(error.response);
//   });
// }
}
