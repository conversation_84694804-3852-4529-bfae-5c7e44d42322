name: mandob
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.6+6

environment:
  sdk: '>=3.0.6 <4.0.0'
#  sdk: ">=2.13.0 <3.0.0"z

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  printing: ^5.13.2
#  drago_blue_printer: ^0.0.6
  #     ^5.13.2
  #  printing:
  #    path: packages/printing_package

  cupertino_icons: ^1.0.8
  shared_preferences: ^2.2.3
  #  adobe_xd: ^2.0.1
  http: ^0.13.3
  provider: ^6.1.2
  flutter_slider_drawer: ^2.1.3
#  syncfusion_flutter_datepicker: ^28.2.7
  material_floating_search_bar: ^0.3.7
  animated_text_kit: ^4.2.2
  bubble_tab_indicator: ^0.1.6
  image_picker: ^1.1.1
  multiselect: ^0.1.1
  reactive_forms:
  chopper:
  json_annotation: ^4.9.0
  url_launcher:
  #  flutter_share_me: ^1.4.0
  font_awesome_flutter: ^10.7.0
  another_flushbar: ^1.12.30
  sunmi_printer_plus: ^2.1.3
  zxing2: ^0.2.3
  binary_codec: ^2.0.3
  sprintf: ^7.0.0
  intl: ^0.19.0
  persian_number_utility: ^1.1.4
  flutter_keyboard_visibility: ^6.0.0
  sunmi_printer_service:
    path: packages/sunmi_printer_service
  salomon_bottom_bar: ^3.3.2
  floating_action_bubble: ^1.1.4
  flutter_switch: ^0.3.2
  syncfusion_flutter_xlsio:
    path: packages/syncfusion_flutter_xlsio
  path_provider: ^2.1.5
  permission_handler: ^11.3.1
  flutter_hooks:
  #? Local Storage
  get_storage: ^2.1.1
  xr_helper:
    path: packages/xr_helper
#  blue_print_pos_arabic:
#  blue_print_pos:
#    path: packages/blue_print_pos_arabic
  screenshot: ^3.0.0




  #firebase//////////////
  #  firebase_auth: ^4.19.5
  #  cloud_firestore: ^4.17.3
#  firebase_core:
#  firebase_storage:
  barcode_widget: ^2.0.4
  #  flutter_barcode_scanner: ^2.0.0
  #  loadmore: ^2.1.0


  #  appwrite: ^12.0.3
  appwrite: ^9.0.0
  xid: ^1.2.1
  dropdown_search: ^5.0.6
  share_plus:
  equatable: ^2.0.5
  loading_animation_widget:
  google_fonts:
  animated_bottom_navigation_bar: ^1.3.3
  quickalert:
    path: packages/quickalert


  #dev_dependencies:
  #  archive: ^3.6.1
  #  win32: ^5.5.4
dependency_overrides:
  intl: ^0.20.2
  syncfusion_flutter_xlsio:
    path: packages/syncfusion_flutter_xlsio
  flutter_web_auth_2: ^4.1.0
  appwrite:
    path: packages/appwrite

  flutter_test:
    sdk: flutter
  chopper_generator:
  flutter_lints: ^4.0.0
  json_serializable: ^6.8.0
  build_runner: ^2.4.10
  #  flutter_app_name: ^0.1.1
#  flutter_launcher_icons: ^0.14.2
  flutter_native_splash:


#? dart run flutter_launcher_icons:main
flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/mandob_logo.png"

#  #? dart run flutter_native_splash:create
flutter_native_splash:
  android: true
  ios: true
  web: false
  fullscreen: true
  color: '#ffffff'
  image: 'assets/images/logo.png'
  android_12:
    color: '#ffffff'
    image: 'assets/images/logo.png'

# The following section is specific to Flutter.
flutter:


  uses-material-design: true

  assets:
    - assets/images/
    - lang/en.json
    - lang/ar.json
    - assets/fonts/cairo/Cairo-Bold.ttf
    - assets/fonts/cairo/Cairo-Regular.ttf
    - shorebird.yaml

  fonts:
    - family: Droid
      fonts:
        - asset: assets/fonts/Droid.ttf

    - family: Cairo
      fonts:
        - asset: assets/fonts/cairo/Cairo-Regular.ttf

    - family: CairoBold
      fonts:
        - asset: assets/fonts/cairo/Cairo-Bold.ttf

    - family: gess
      fonts:
        - asset: assets/fonts/gess/ge-medium.otf