/// Represents merged cell.
class MergeCell {
  /// Represent merged cell reference.
  late String reference;

  /// Represents left cell index value.
  late int x;

  /// Represents width of the merged cell.
  late int width;

  /// Represents top cell index value.
  late int y;

  /// Represents height of the merged cell.
  late int height;
}

/// Represents the extended format cell

class ExtendCell {
  /// Gets/Sets X value.
  late int x;

  /// Gets/Sets Y value.
  late int y;
}
