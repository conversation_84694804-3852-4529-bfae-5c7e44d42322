## [19.2.44-beta] - 06/30/2021

**Features**

* Provided the support to import data from collection objects to worksheet.

## [19.1.54-beta] - 03/31/2021 

**Features**

* Provided the support to import data from List<Object> to worksheet.
* Provided the support to apply conditional formatting for worksheet.

## [18.4.30-beta] - 12/17/2020

**Features**

* Provided support to add hyperlinks to texts and images.
* Provided support to insert or delete rows and columns.
* Provided support to autofit rows and columns.
* Provided support to add logical functions, string functions, and nested formulas.
* Provided support to protect workbooks and worksheets.

## [18.3.40] - 10/13/2020

**API Changes**
* Changed the property name for Worksheet showGridLines to showGridlines.
* Changed the method name for Workbook class saveStream to saveAsStream.
* Removed the save method from Workbook class.
* Removed the addFile method from PicturesCollection class.

## [18.3.35-beta.1] - 10/02/2020

**Features**
* Updated the code with respect to coding standards.

## [18.3.35-beta] - 10/01/2020

Initial release

**Features** 
* Create simple Excel documents.
* Add text, number, and date time values in the worksheet cells.
* Apply cell formatting, merge and unmerge cells.
* Add basic formulas to Excel worksheet cells.
* Add images (JPEG and PNG) to Excel worksheets.
* Add pie chart, bar chart, column chart, line chart, stacked column chart,stacked line chart and stacked bar chart to Excel worksheets.