enum BitmapPrintType {
  /// same as printBitmap()
  ///
  /// 同⽅法 printBitmap()
  normal,

  /// Threshold 200 black and white image
  ///
  /// 阈值200的 ⿊⽩化图⽚
  black,

  /// Grayscaled image
  ///
  /// 灰度图⽚
  grey,
}

extension BitmapPrintTypeExt on BitmapPrintType {
  int get value {
    switch (this) {
      case BitmapPrintType.normal:
        return 0;
      case BitmapPrintType.black:
        return 1;
      case BitmapPrintType.grey:
        return 2;
    }
  }
}
