group 'tech.laihz.sunmi_printer_service'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.5.20'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion 34
    namespace 'tech.laihz.sunmi_printer_service'

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    defaultConfig {
        minSdkVersion 19
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "com.sunmi:printerlibrary:1.0.14"
}
//group 'tech.laihz.sunmi_printer_service'
//version '1.0-SNAPSHOT'
//
//buildscript {
//    ext.kotlin_version = '1.5.20'
//    repositories {
//        google()
//        mavenCentral()
//    }
//
//    dependencies {
//        classpath 'com.android.tools.build:gradle:4.1.0'
//        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
//    }
//}
//
//rootProject.allprojects {
//    repositories {
//        google()
//        jcenter()
//    }
//}
//
//apply plugin: 'com.android.library'
//apply plugin: 'kotlin-android'
//
//android {
//    compileSdkVersion 34
//    namespace 'tech.laihz.sunmi_printer_service'
//
//    sourceSets {
//        main.java.srcDirs += 'src/main/kotlin'
//    }
//    defaultConfig {
//        minSdkVersion 19
//    }
//}
//
//dependencies {
//    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
//    implementation "com.sunmi:printerlibrary:1.0.14"
//}