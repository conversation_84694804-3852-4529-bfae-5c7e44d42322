// part of xr_helper;
//
// class NotificationService {
//   static Future<void> _firebaseMessagingBackgroundHandler(
//       RemoteMessage message) async {
//     if (Firebase.apps.isEmpty) await Firebase.initializeApp();
//   }
//
//   static void init() async {
//     final fcm = FirebaseMessaging.instance;
//
//     await fcm.requestPermission(
//       alert: true,
//       badge: true,
//       provisional: false,
//       sound: true,
//     );
//
//     fcm.setForegroundNotificationPresentationOptions(
//         badge: true, alert: true, sound: true);
//
//     FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
//
//     return;
//   }
//
//   //? Get Token
//   static Future<String> getToken() async {
//     final fcm = FirebaseMessaging.instance;
//
//     final token = await fcm.getToken();
//
//     return token ?? '';
//   }
//
//   //? Subscribe to topic
//   static Future<void> subscribeToTopic(String topic) async {
//     final fcm = FirebaseMessaging.instance;
//
//     Log.w('SUBSCRIBED TO $topic');
//
//     await fcm.subscribeToTopic(topic);
//   }
//
//   //! Send Notification
//   static Future<void> sendNotification(
//       {required String title,
//       required String body,
//       required String userTokenOrTopic,
//       bool isTopic = false}) async {
//     const firebaseProjectId = 'opti-tickets';
//
//     Log.w('SentNotificationTo $userTokenOrTopic');
//
//     const String fcmUrl =
//         'https://fcm.googleapis.com/v1/projects/$firebaseProjectId/messages:send';
//
//     final accessToken = await AccessTokenFirebase().getAccessToken();
//
//     final Map<String, dynamic> message = {
//       'message': {
//         'token': isTopic ? '/topics/$userTokenOrTopic' : userTokenOrTopic,
//         'notification': {
//           'title': title,
//           'body': body,
//         },
//       }
//     };
//
//     final response = await http.post(
//       Uri.parse(fcmUrl),
//       headers: {
//         'Content-Type': 'application/json; charset=UTF-8',
//         'Authorization': 'Bearer $accessToken',
//       },
//       body: jsonEncode(message),
//     );
//
//     if (response.statusCode == 200) {
//       Log.w('NotificationSentSuccessfully ${response.body}');
//     } else {
//       Log.e('Failed to send notification: ${response.statusCode}');
//       Log.e(response.body);
//     }
//   }
//
//   static void listenToNotifications({
//     Function(RemoteMessage)? onMessage,
//     Function(RemoteMessage)? onMessageOpenedApp,
//   }) {
//     FirebaseMessaging.onMessage.listen((message) {
//       Log.i(
//           'onMessage:\nTitle ${message.notification?.title}\nBody ${message.notification?.body}\nData ${message.data}');
//
//       if (onMessage != null) onMessage(message);
//     });
//
//     FirebaseMessaging.onMessageOpenedApp.listen((message) {
//       Log.i(
//           'onOpenAppMessage:\nTitle ${message.notification?.title}\nBody ${message.notification?.body}\nData ${message.data}');
//
//       if (onMessageOpenedApp != null) onMessageOpenedApp(message);
//     });
//   }
// }
//
// class AccessTokenFirebase {
//   static const firebaseMessagingScope =
//       'https://www.googleapis.com/auth/firebase.messaging';
//
//   Future<String> getAccessToken() async {
//     final jsonMap = {
//       "type": "service_account",
//       "project_id": "opti-tickets",
//       "private_key_id": "015e5c2c65272057f662a87278bac6343242fc7d",
//       "private_key":
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
//       "client_email":
//           "<EMAIL>",
//       "client_id": "106142292781576359036",
//       "auth_uri": "https://accounts.google.com/o/oauth2/auth",
//       "token_uri": "https://oauth2.googleapis.com/token",
//       "auth_provider_x509_cert_url":
//           "https://www.googleapis.com/oauth2/v1/certs",
//       "client_x509_cert_url":
//           "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-plh6h%40opti-tickets.iam.gserviceaccount.com",
//       "universe_domain": "googleapis.com"
//     };
//
//     final client = await clientViaServiceAccount(
//         ServiceAccountCredentials.fromJson(jsonMap), [firebaseMessagingScope]);
//
//     final accessToken = client.credentials.accessToken.data;
//
//     return accessToken;
//   }
// }
