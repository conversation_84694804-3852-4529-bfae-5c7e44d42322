name: printing
description: >
  Plugin that allows Flutter apps to generate and print documents to
  compatible printers on Android, iOS, macOS, Windows, and Linux,
  as well as web print.
homepage: https://github.com/DavBfr/dart_pdf/tree/master/printing
repository: https://github.com/DavBfr/dart_pdf
issue_tracker: https://github.com/DavBfr/dart_pdf/issues
version: 5.6.5

environment:
  sdk: ">=2.12.0 <3.0.0"
  flutter: ">=1.16.0"

dependencies:
  ffi: ^2.1.2
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  http: ^0.13.3
#  http: ^0.13.0
#  http: ^1.2.1
  image: ^4.1.7
  js: ^0.7.1
  meta: ^1.12.0
  pdf: ^3.2.0
  plugin_platform_interface: ^2.1.8

dev_dependencies:
  flutter_lints: ^4.0.0
#  flutter_test:
#    sdk: flutter
  mockito: ^5.4.4

_dependency_overrides:
  pdf:
    path: ../pdf

flutter:
  plugin:
    platforms:
      android:
        package: net.nfet.flutter.printing
        pluginClass: PrintingPlugin
      ios:
        pluginClass: PrintingPlugin
      linux:
        pluginClass: PrintingPlugin
      macos:
        pluginClass: PrintingPlugin
      web:
        fileName: printing_web.dart
        pluginClass: PrintingPlugin
      windows:
        pluginClass: PrintingPlugin
