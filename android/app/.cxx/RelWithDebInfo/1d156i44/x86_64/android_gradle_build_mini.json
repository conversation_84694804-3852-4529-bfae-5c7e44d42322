{"buildFiles": ["/Users/<USER>/FlutterDev/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Flutter-Projects/Optimum-Apps/latest_mandob_app/android/app/.cxx/RelWithDebInfo/1d156i44/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Flutter-Projects/Optimum-Apps/latest_mandob_app/android/app/.cxx/RelWithDebInfo/1d156i44/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}