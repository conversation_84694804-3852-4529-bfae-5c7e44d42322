{"buildFiles": ["/Users/<USER>/FlutterDev/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Flutter-Projects/Optimum-Apps/latest_mandob_app/android/app/.cxx/Debug/1w4i5v55/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Flutter-Projects/Optimum-Apps/latest_mandob_app/android/app/.cxx/Debug/1w4i5v55/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}