pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }




}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version "8.2.1" apply false
    id "org.jetbrains.kotlin.android" version "1.8.10" apply false
}

include ":app"

//include ':app'
//
//def localPropertiesFile = new File(rootProject.projectDir, "local.properties")
//def properties = new Properties()
//
//assert localPropertiesFile.exists()
//localPropertiesFile.withReader("UTF-8") { reader -> properties.load(reader) }
//
//def flutterSdkPath = properties.getProperty("flutter.sdk")
//assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
//apply from: "$flutterSdkPath/packages/flutter_tools/gradle/app_plugin_loader.gradle"
